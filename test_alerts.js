#!/usr/bin/env node

const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function testAlertSystem() {
    console.log('🧪 Testando Sistema de Alertas DataHero4\n');

    try {
        // Test API endpoint using curl
        const apiUrl = 'http://localhost:8000/api/v1/kpis?client_id=L2M&sector=cambio&timeframe=week&currency=all';
        console.log('📡 Fazendo requisição para API...');
        console.log(`URL: ${apiUrl}\n`);

        const { stdout } = await execAsync(`curl -s "${apiUrl}"`);
        const data = JSON.parse(stdout);
        
        console.log('✅ API respondeu com sucesso!\n');
        console.log('📊 Analisando KPIs com alertas:\n');
        
        let alertsFound = 0;
        
        data.kpis.forEach(kpi => {
            const hasAlert = kpi.alert && kpi.alert.isActive;
            const alertIcon = hasAlert ? (kpi.alert.type === 'critical' ? '🔴' : '🟡') : '✅';
            const alertText = hasAlert ? `ALERTA ${kpi.alert.type.toUpperCase()}` : 'OK';
            
            console.log(`${alertIcon} ${kpi.id}`);
            console.log(`   Nome: ${kpi.name}`);
            console.log(`   Valor: ${kpi.formatted_value || kpi.value}`);
            console.log(`   Status: ${alertText}`);
            
            if (hasAlert) {
                console.log(`   🚨 Mensagem: ${kpi.alert.message}`);
                console.log(`   📏 Threshold: ${kpi.alert.threshold}`);
                alertsFound++;
            }
            
            console.log('');
        });
        
        console.log(`\n📈 Resumo:`);
        console.log(`   Total de KPIs: ${data.kpis.length}`);
        console.log(`   Alertas ativos: ${alertsFound}`);
        console.log(`   KPIs normais: ${data.kpis.length - alertsFound}`);
        
        if (alertsFound > 0) {
            console.log('\n🎯 Sistema de alertas FUNCIONANDO! ✅');
        } else {
            console.log('\n⚠️  Nenhum alerta ativo encontrado');
        }
        
    } catch (error) {
        console.error('❌ Erro ao testar sistema de alertas:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Dica: Certifique-se de que o backend está rodando em http://localhost:8000');
        }
    }
}

// Test frontend API as well
async function testFrontendAPI() {
    console.log('\n🌐 Testando Frontend...');

    try {
        const { stdout, stderr } = await execAsync('curl -s -o /dev/null -w "%{http_code}" http://localhost:3000');
        const statusCode = stdout.trim();

        if (statusCode === '200') {
            console.log('✅ Frontend está rodando em http://localhost:3000');
        } else {
            console.log(`⚠️  Frontend retornou status: ${statusCode}`);
        }
    } catch (error) {
        console.log('❌ Frontend não está acessível em http://localhost:3000');
        console.log('💡 Dica: Execute "turbo dev" para iniciar o frontend');
    }
}

async function main() {
    await testAlertSystem();
    await testFrontendAPI();
    
    console.log('\n🏁 Teste concluído!');
    console.log('\n📋 Próximos passos:');
    console.log('   1. Abra http://localhost:3000 no browser');
    console.log('   2. Verifique se os ícones de alerta aparecem nos cards dos KPIs');
    console.log('   3. Procure por ícones 🔴 (crítico) ou 🟡 (warning) nos cards');
}

main();
