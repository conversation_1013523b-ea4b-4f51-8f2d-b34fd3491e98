#!/usr/bin/env node

const testFilters = async () => {
  console.log('🔍 Testing Dashboard Filters...\n');
  
  // Test different timeframes
  const tests = [
    { timeframe: '1d', currency: 'all', description: '1 day, all currencies' },
    { timeframe: 'week', currency: 'all', description: '1 week, all currencies' },
    { timeframe: 'month', currency: 'all', description: '1 month, all currencies' },
    { timeframe: 'week', currency: 'usd', description: '1 week, USD only' },
  ];

  for (const test of tests) {
    try {
      const url = `http://localhost:8000/api/dashboard/kpis?client_id=L2M&timeframe=${test.timeframe}&currency=${test.currency}`;
      console.log(`📡 Testing: ${test.description}`);
      
      const response = await fetch(url);
      const data = await response.json();
      
      const totalVolume = data.kpis.find(kpi => kpi.id === 'total_volume')?.currentValue || 0;
      const avgSpread = data.kpis.find(kpi => kpi.id === 'average_spread')?.currentValue || 0;
      
      console.log(`   📊 Total Volume: ${totalVolume.toLocaleString()}`);
      console.log(`   📈 Average Spread: ${avgSpread.toFixed(4)}%`);
      console.log(`   ✅ Status: ${response.status} (${data.kpis.length} KPIs)\n`);
      
    } catch (error) {
      console.error(`❌ Error testing ${test.description}:`, error.message);
    }
  }
};

testFilters().catch(console.error);