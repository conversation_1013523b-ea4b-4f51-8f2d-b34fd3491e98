# DataHero4 - <PERSON><PERSON><PERSON> de Testes na Interface
## Como Testar KPIs Personalizados por Perfil

**Data:** 21 de Janeiro de 2025  
**Versão:** 7.0 - Production Ready  
**Objetivo:** Validar funcionalidades na interface web

---

## 🚀 **SETUP INICIAL**

### **1. Iniciar Aplicação**
```bash
# Backend
cd datahero4/apps/backend
poetry run python webapp_unified.py

# Frontend (se disponível)
cd datahero4/apps/frontend
npm run dev
```

### **2. Verificar Saúde do Sistema**
```bash
# Health check completo
curl http://localhost:8000/health | jq

# Resposta esperada:
{
  "status": "healthy",
  "checks": {
    "memory": {"status": "healthy"},
    "cpu": {"status": "healthy"},
    "disk": {"status": "healthy"},
    "bcb_api": {"status": "healthy"}
  }
}
```

---

## 🧪 **TESTES POR PERFIL DE USUÁRIO**

### **👔 TESTE 1: Perfil CEO**

#### **A. Acesso ao Dashboard**
1. **URL:** `http://localhost:8000/api/dashboard/snapshot`
2. **Método:** GET
3. **Headers:** `{"user-profile": "CEO"}`

#### **B. Validação de Response**
```json
{
  "profile": "CEO",
  "kpis": {
    "spread_income_detailed": {
      "value": 1.2,
      "unit": "%",
      "status": "healthy",
      "response_time_ms": 19
    },
    "margem_liquida_operacional": {
      "value": 18.5,
      "unit": "%", 
      "status": "excellent",
      "response_time_ms": 19
    }
  },
  "routing_metadata": {
    "layer_used": "snapshot",
    "cache_hit": true,
    "total_time_ms": 19
  }
}
```

#### **C. Testes Específicos CEO**
```bash
# 1. Teste de performance (deve ser <50ms)
time curl -H "user-profile: CEO" http://localhost:8000/api/dashboard/snapshot

# 2. Teste de KPIs específicos
curl -H "user-profile: CEO" http://localhost:8000/api/kpi/spread_income_detailed

# 3. Teste de cache (segunda chamada deve ser mais rápida)
time curl -H "user-profile: CEO" http://localhost:8000/api/dashboard/snapshot
```

---

### **💰 TESTE 2: Perfil CFO**

#### **A. Acesso Personalizado**
```bash
# Dashboard CFO
curl -H "user-profile: CFO" http://localhost:8000/api/dashboard/snapshot

# KPI específico - Margem Líquida
curl -H "user-profile: CFO" http://localhost:8000/api/kpi/margem_liquida_operacional

# KPI específico - Custo por Transação  
curl -H "user-profile: CFO" http://localhost:8000/api/kpi/custo_por_transacao
```

#### **B. Validação de Personalização**
- ✅ **KPIs Prioritários:** margem_liquida_operacional, custo_por_transacao
- ✅ **Cache TTL:** 30 minutos (1800s)
- ✅ **Layer:** Snapshot para dados críticos
- ✅ **Response Time:** <50ms

---

### **⚠️ TESTE 3: Perfil Risk Manager**

#### **A. Testes Real-Time**
```bash
# Dashboard Risk Manager (deve usar Direct Layer)
curl -H "user-profile: Risk_Manager" http://localhost:8000/api/dashboard/snapshot

# Tempo de processamento (crítico para Risk)
curl -H "user-profile: Risk_Manager" http://localhost:8000/api/kpi/tempo_processamento_medio

# Teste de alertas (simular condição crítica)
curl -X POST http://localhost:8000/api/alerts/simulate \
  -H "Content-Type: application/json" \
  -d '{"kpi": "tempo_processamento_medio", "value": 65, "profile": "Risk_Manager"}'
```

#### **B. Validação Real-Time**
- ✅ **Layer:** Direct (dados em tempo real)
- ✅ **Cache TTL:** 5 minutos (300s)
- ✅ **Alertas:** Ativados para valores críticos
- ✅ **Response Time:** Pode ser >100ms (dados reais)

---

### **📈 TESTE 4: Perfil Trader**

#### **A. Testes de Velocidade**
```bash
# Dashboard Trader (cache otimizado)
time curl -H "user-profile: Trader" http://localhost:8000/api/dashboard/snapshot

# Spread em tempo quase real
curl -H "user-profile: Trader" http://localhost:8000/api/kpi/spread_income_detailed

# Tempo de processamento para operações
curl -H "user-profile: Trader" http://localhost:8000/api/kpi/tempo_processamento_medio
```

#### **B. Teste de Concorrência (Simular Trading)**
```bash
# Múltiplas requisições simultâneas
for i in {1..10}; do
  curl -H "user-profile: Trader" http://localhost:8000/api/kpi/spread_income_detailed &
done
wait
```

#### **C. Validação Trader**
- ✅ **Layer:** Cache (balance velocidade/atualização)
- ✅ **Cache TTL:** 1 minuto (60s)
- ✅ **Concorrência:** Suporta múltiplas requisições
- ✅ **Response Time:** <100ms

---

### **⚙️ TESTE 5: Perfil Operations**

#### **A. Testes Operacionais**
```bash
# Dashboard Operations
curl -H "user-profile: Operations" http://localhost:8000/api/dashboard/snapshot

# Custo por transação (foco principal)
curl -H "user-profile: Operations" http://localhost:8000/api/kpi/custo_por_transacao

# Tempo de processamento (eficiência)
curl -H "user-profile: Operations" http://localhost:8000/api/kpi/tempo_processamento_medio
```

#### **B. Validação Operations**
- ✅ **KPIs Prioritários:** custo_por_transacao, tempo_processamento_medio
- ✅ **Cache TTL:** 15 minutos (900s)
- ✅ **Layer:** Cache (eficiência operacional)
- ✅ **Focus:** Custos e processos

---

## 🔍 **TESTES DE VALIDAÇÃO AVANÇADA**

### **TESTE 6: Profile Detection Automático**
```bash
# Simular comportamento CEO (consultas estratégicas)
curl http://localhost:8000/api/kpi/spread_income_detailed
curl http://localhost:8000/api/kpi/margem_liquida_operacional
curl http://localhost:8000/api/dashboard/snapshot

# Verificar detecção de perfil
curl http://localhost:8000/api/profile/detect/user123
```

### **TESTE 7: Fail-Fast Behavior**
```bash
# Simular falha de banco (deve falhar fast, não usar fallback)
curl http://localhost:8000/api/kpi/invalid_kpi_id

# Resposta esperada: 400/500 com erro claro, NÃO dados mock
{
  "error": "KPI not found",
  "message": "KPI 'invalid_kpi_id' not found - FAIL FAST",
  "timestamp": "2025-01-21T10:30:00Z"
}
```

### **TESTE 8: Performance Benchmarks**
```bash
# Benchmark de performance por perfil
echo "=== CEO Performance Test ==="
time curl -H "user-profile: CEO" http://localhost:8000/api/dashboard/snapshot

echo "=== Risk Manager Performance Test ==="
time curl -H "user-profile: Risk_Manager" http://localhost:8000/api/dashboard/snapshot

echo "=== Trader Performance Test ==="
time curl -H "user-profile: Trader" http://localhost:8000/api/dashboard/snapshot
```

**Targets Esperados:**
- **CEO/CFO:** <50ms (Snapshot Layer)
- **Risk Manager:** <200ms (Direct Layer)
- **Trader:** <100ms (Cache Layer)
- **Operations:** <150ms (Cache Layer)

---

## 📊 **TESTES DE MONITORAMENTO**

### **TESTE 9: Health Checks Detalhados**
```bash
# Health check individual por componente
curl http://localhost:8000/health/database
curl http://localhost:8000/health/redis  
curl http://localhost:8000/health/memory
curl http://localhost:8000/health/cpu

# Métricas Prometheus
curl http://localhost:8000/metrics | grep datahero
```

### **TESTE 10: Logs Estruturados**
```bash
# Verificar logs estruturados (JSON)
tail -f logs/datahero4.log | jq

# Exemplo de log esperado:
{
  "timestamp": "2025-01-21T10:30:00Z",
  "level": "INFO",
  "event": "request_completed",
  "method": "GET",
  "path": "/api/dashboard/snapshot",
  "status_code": 200,
  "duration_ms": 19.5,
  "user_profile": "CEO",
  "routing_layer": "snapshot"
}
```

---

## ✅ **CHECKLIST DE VALIDAÇÃO**

### **Funcionalidades Básicas:**
- [ ] Aplicação inicia sem erros
- [ ] Health check retorna status healthy
- [ ] Endpoints respondem corretamente
- [ ] Logs estruturados funcionando

### **Personalização por Perfil:**
- [ ] CEO: KPIs estratégicos, response <50ms
- [ ] CFO: KPIs financeiros, cache 30min
- [ ] Risk Manager: Dados real-time, alertas funcionando
- [ ] Trader: Cache otimizado, concorrência OK
- [ ] Operations: KPIs operacionais, eficiência

### **Performance:**
- [ ] Snapshot Layer: <50ms
- [ ] Cache Layer: <100ms  
- [ ] Direct Layer: <200ms
- [ ] Concorrência: Suporta 10+ requests simultâneas

### **Fail-Fast:**
- [ ] Erros retornam status apropriados
- [ ] Não há fallbacks ou dados mock
- [ ] Mensagens de erro são claras
- [ ] Sistema falha rápido quando necessário

### **Monitoramento:**
- [ ] Métricas Prometheus funcionando
- [ ] Health checks detalhados
- [ ] Logs estruturados em JSON
- [ ] Alertas configurados

---

## 🐛 **TROUBLESHOOTING**

### **Problema: Response Time Alto**
```bash
# Verificar qual layer está sendo usado
curl -v -H "user-profile: CEO" http://localhost:8000/api/dashboard/snapshot

# Verificar cache
curl http://localhost:8000/metrics | grep cache_hit_rate
```

### **Problema: KPI Não Encontrado**
```bash
# Verificar KPIs disponíveis
curl http://localhost:8000/api/kpis/list

# Verificar configuração do perfil
curl http://localhost:8000/api/profile/CEO/kpis
```

### **Problema: Profile Detection Falha**
```bash
# Verificar histórico de queries do usuário
curl http://localhost:8000/api/profile/detect/user123/debug

# Forçar perfil específico
curl -H "user-profile: CEO" http://localhost:8000/api/dashboard/snapshot
```

---

## 📞 **SUPORTE**

**Em caso de problemas:**
1. Verificar logs: `tail -f logs/datahero4.log`
2. Verificar health: `curl http://localhost:8000/health`
3. Verificar métricas: `curl http://localhost:8000/metrics`
4. Contato técnico: <EMAIL>

**Status do Sistema:**
- 🟢 Funcionando: Todos os testes passam
- 🟡 Degradado: Alguns testes falham, funcionalidade básica OK
- 🔴 Crítico: Múltiplos testes falham, investigação necessária

---

*Guia de testes validado com DataHero4 v7.0 - Production Ready*
