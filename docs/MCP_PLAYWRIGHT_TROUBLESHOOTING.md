# MCP Playwright Troubleshooting Guide

## 🚨 Problemas Comuns e Soluções

### **Problema Principal: B<PERSON>er Travado em Loop**

**Erro típico:**
```
Error: Browser is already in use for /Users/<USER>/Library/Caches/ms-playwright/mcp-chrome-profile, use --isolated to run multiple instances of the same browser
```

**Sintomas:**
- Browser abre páginas em branco
- Comandos ficam em loop sem resposta
- `browser_console_messages_playwright()` retorna erro
- `browser_snapshot_playwright()` falha

---

## 🛠️ Soluções Imediatas

### **1. Limpeza de Processos (Primeira tentativa)**

```bash
# Mata todos os processos Chrome do Playwright
pkill -f "chrome.*playwright" || true

# Aguarda alguns segundos
sleep 3

# Tenta navegar novamente
browser_navigate_playwright(url="http://localhost:3000")
```

### **2. Limpeza de Cache (Se problema persistir)**

```bash
# Remove cache do Playwright
rm -rf /Users/<USER>/Library/Caches/ms-playwright/

# Reinstala o browser
browser_install_playwright()
```

### **3. Verificação de Processos Ativos**

```bash
# Lista processos Chrome ativos
ps aux | grep chrome | grep playwright

# Se houver processos, mata todos
pkill -9 chrome
```

---

## 🔧 Estratégias de Prevenção

### **1. Sempre Fechar Sessões**

```javascript
// ❌ ERRADO: Deixar sessão aberta
browser_navigate_playwright(url="http://localhost:3000")
// ... trabalho ...
// (esquece de fechar)

// ✅ CORRETO: Sempre fechar ao final
browser_navigate_playwright(url="http://localhost:3000")
// ... trabalho ...
browser_close_playwright()
```

### **2. Timeout Adequado**

```javascript
// ✅ Use timeouts apropriados
browser_wait_for_playwright(time=5)  // Não mais que 10s

// ❌ Evite timeouts muito longos
browser_wait_for_playwright(time=60) // Pode travar
```

### **3. Verificar Estado Antes de Usar**

```javascript
// Sempre verificar se há páginas abertas
try {
    browser_snapshot_playwright()
} catch (error) {
    // Se falhar, limpar e reiniciar
    browser_close_playwright()
    browser_navigate_playwright(url="http://localhost:3000")
}
```

---

## 🚀 Estratégias de Debugging Alternativas

### **Quando o Browser Trava, Use:**

#### **1. Teste APIs Diretamente**
```bash
# Teste backend sem browser
curl -s "http://localhost:8000/api/endpoint" | jq '.'

# Verifique se frontend está rodando
curl -s http://localhost:3000 | head -5
```

#### **2. Logs de Código**
```javascript
// Adicione logs detalhados no código
console.log('🔍 [DEBUG] Estado:', {
    dados: dados?.length,
    loading: isLoading,
    erro: error
});
```

#### **3. Análise de Rede**
```bash
# Verifique se serviços estão rodando
netstat -an | grep :3000  # Frontend
netstat -an | grep :8000  # Backend
```

---

## 📋 Checklist de Troubleshooting

### **Antes de Usar Playwright:**
- [ ] Verificar se não há processos Chrome ativos
- [ ] Confirmar que frontend/backend estão rodando
- [ ] Testar APIs com curl primeiro

### **Durante o Uso:**
- [ ] Usar timeouts curtos (≤10s)
- [ ] Capturar snapshots regularmente
- [ ] Verificar logs do console frequentemente
- [ ] Não deixar sessões abertas por muito tempo

### **Quando Travar:**
- [ ] Tentar `browser_close_playwright()`
- [ ] Matar processos Chrome: `pkill -f chrome.*playwright`
- [ ] Limpar cache se necessário
- [ ] Usar debugging alternativo (curl, logs)

### **Após Resolver:**
- [ ] Sempre fechar sessão adequadamente
- [ ] Documentar o que causou o problema
- [ ] Implementar logs preventivos

---

## 🎯 Boas Práticas

### **1. Estrutura de Sessão Ideal**

```javascript
// Início da sessão
browser_navigate_playwright(url="http://localhost:3000")

// Trabalho principal
browser_snapshot_playwright()
browser_click_playwright(element="button", ref="e123")
browser_wait_for_playwright(time=3)

// Verificação
browser_console_messages_playwright()

// SEMPRE fechar
browser_close_playwright()
```

### **2. Debugging Híbrido**

```javascript
// Combine Playwright + métodos alternativos
try {
    // Tenta usar browser
    browser_snapshot_playwright()
    browser_console_messages_playwright()
} catch (error) {
    // Fallback para métodos alternativos
    console.log("Browser travado, usando curl...")
    // curl para testar APIs
    // análise de logs de código
}
```

### **3. Monitoramento Preventivo**

```bash
# Script para monitorar processos
watch -n 5 'ps aux | grep chrome | grep playwright | wc -l'

# Se número > 1, há problema potencial
```

---

## 🆘 Recuperação de Emergência

### **Se Tudo Falhar:**

1. **Reset Completo:**
```bash
# Para todos os processos
sudo pkill -9 chrome
sudo pkill -9 playwright

# Remove cache
rm -rf ~/.cache/ms-playwright/
rm -rf ~/Library/Caches/ms-playwright/

# Reinicia serviços
cd /path/to/project
npm run dev  # ou turbo dev
```

2. **Reiniciar Sistema:**
- Como último recurso, reiniciar o computador
- Isso limpa todos os processos travados

3. **Debugging sem Browser:**
- Use curl para APIs
- Analise logs de código
- Verifique network requests
- Teste funcionalidades manualmente

---

## 📝 Logs Úteis para Debugging

```bash
# Processos Chrome ativos
ps aux | grep chrome

# Portas em uso
lsof -i :3000
lsof -i :8000

# Logs do sistema (macOS)
log show --predicate 'process == "chrome"' --last 5m
```

---

---

## 🤖 Scripts de Automação

### **Script de Limpeza Automática**

Crie um arquivo `scripts/cleanup-playwright.sh`:

```bash
#!/bin/bash
echo "🧹 Limpando Playwright..."

# Mata processos Chrome
pkill -f "chrome.*playwright" 2>/dev/null || true
sleep 2

# Remove cache
rm -rf ~/Library/Caches/ms-playwright/ 2>/dev/null || true

# Verifica se limpou
CHROME_PROCESSES=$(ps aux | grep chrome | grep playwright | wc -l)
if [ $CHROME_PROCESSES -eq 0 ]; then
    echo "✅ Playwright limpo com sucesso!"
else
    echo "⚠️  Ainda há processos ativos. Tente reiniciar o sistema."
fi
```

### **Script de Verificação de Saúde**

Crie um arquivo `scripts/check-playwright-health.sh`:

```bash
#!/bin/bash
echo "🔍 Verificando saúde do Playwright..."

# Verifica processos
CHROME_COUNT=$(ps aux | grep chrome | grep playwright | wc -l)
echo "Processos Chrome ativos: $CHROME_COUNT"

# Verifica cache
CACHE_SIZE=$(du -sh ~/Library/Caches/ms-playwright/ 2>/dev/null | cut -f1)
echo "Tamanho do cache: ${CACHE_SIZE:-"0B"}"

# Verifica portas
FRONTEND_PORT=$(lsof -i :3000 | wc -l)
BACKEND_PORT=$(lsof -i :8000 | wc -l)
echo "Frontend (3000): $FRONTEND_PORT conexões"
echo "Backend (8000): $BACKEND_PORT conexões"

# Status geral
if [ $CHROME_COUNT -le 1 ] && [ $FRONTEND_PORT -gt 0 ] && [ $BACKEND_PORT -gt 0 ]; then
    echo "✅ Sistema saudável para usar Playwright"
else
    echo "⚠️  Sistema pode ter problemas. Execute cleanup primeiro."
fi
```

---

## 📊 Monitoramento em Tempo Real

### **Dashboard de Status**

```bash
# Execute em terminal separado para monitorar
watch -n 2 '
echo "=== PLAYWRIGHT STATUS ==="
echo "Chrome processes: $(ps aux | grep chrome | grep playwright | wc -l)"
echo "Frontend (3000): $(lsof -i :3000 | wc -l) connections"
echo "Backend (8000): $(lsof -i :8000 | wc -l) connections"
echo "Cache size: $(du -sh ~/Library/Caches/ms-playwright/ 2>/dev/null | cut -f1 || echo "0B")"
echo "========================="
'
```

---

## 🎯 Casos de Uso Específicos

### **Para Desenvolvimento DataHero4:**

```javascript
// Template seguro para testes no DataHero4
function safePlaywrightTest(testFunction) {
    try {
        // 1. Verificar se serviços estão rodando
        console.log("Verificando serviços...");

        // 2. Navegar com timeout
        browser_navigate_playwright(url="http://localhost:3000/dashboard");
        browser_wait_for_playwright(time=5);

        // 3. Executar teste
        testFunction();

        // 4. Capturar logs se necessário
        const logs = browser_console_messages_playwright();
        console.log("Logs capturados:", logs.length);

    } catch (error) {
        console.log("❌ Playwright falhou, usando fallback...");

        // Fallback: teste via API
        const apiTest = `curl -s http://localhost:3000/dashboard | grep -i "dashboard"`;
        console.log("Testando via curl...");

    } finally {
        // SEMPRE fechar
        try {
            browser_close_playwright();
        } catch (e) {
            console.log("Browser já estava fechado");
        }
    }
}
```

### **Para Debugging de KPIs:**

```javascript
// Estratégia híbrida para debug de KPIs
function debugKpiIssue(kpiId) {
    console.log(`🔍 Debugging KPI: ${kpiId}`);

    // 1. Teste API primeiro
    const apiCommand = `curl -s "http://localhost:8000/api/personalized-kpis" -X POST -H "Content-Type: application/json" -d '{"user_id": "test_user_ceo", "profile_type": "CEO", "timeframe": "week", "currency": "all"}' | jq '.kpis[] | select(.id == "${kpiId}")'`;

    console.log("Testando API...");
    // Execute apiCommand

    // 2. Se API OK, teste frontend
    try {
        browser_navigate_playwright(url="http://localhost:3000/dashboard");
        browser_wait_for_playwright(time=5);

        // Procurar KPI na tela
        const snapshot = browser_snapshot_playwright();
        console.log("Frontend carregado, procurando KPI...");

        // Clicar no KPI se encontrado
        browser_click_playwright(element=`heading "${kpiId}"`, ref="auto");

    } catch (error) {
        console.log("Frontend falhou, mas API funcionou. Problema no frontend.");
    }
}
```

---

## 📚 Referências Rápidas

### **Comandos Essenciais:**

```bash
# Limpeza rápida
pkill -f "chrome.*playwright" && sleep 2

# Verificação rápida
ps aux | grep chrome | grep playwright

# Reset completo
rm -rf ~/Library/Caches/ms-playwright/ && browser_install_playwright()

# Teste de conectividade
curl -s http://localhost:3000 | head -1
curl -s http://localhost:8000/health || echo "Backend down"
```

### **Logs Importantes:**

```bash
# Logs do Chrome (macOS)
log show --predicate 'process == "chrome"' --last 5m

# Logs do sistema
tail -f /var/log/system.log | grep -i chrome

# Logs do Node.js (se aplicável)
tail -f ~/.npm/_logs/*.log
```

---

**💡 Lembre-se: O Playwright é uma ferramenta poderosa, mas sempre tenha métodos alternativos de debugging prontos!**

**🎯 Dica Final: Mantenha este guia sempre à mão e execute os scripts de verificação regularmente para evitar problemas.**
