# 📋 Template de Teste MCP Playwright

## 🎯 **DEFINIÇÃO DO TESTE**

**Objetivo:** [Descrever especificamente o que está sendo testado]

**Cenários a testar:**
- [ ] Cenário 1: [Descrição]
- [ ] Cenário 2: [Des<PERSON>ri<PERSON>]
- [ ] Cenário 3: [Descrição]

**Critérios de sucesso:**
- [ ] Critério 1: [Definir quando considerar sucesso]
- [ ] Critério 2: [Definir métricas específicas]

---

## 📊 **EXECUÇÃO SISTEMÁTICA**

### **CENÁRIO 1: [Nome do cenário]**

#### **📊 Estado Inicial:**
```
Data/Hora: [timestamp]
URL: [url atual]
Perfil: [perfil selecionado]
Timeframe: [período selecionado]
Currency: [moeda selecionada]

KPIs Visíveis:
- KPI 1: [nome] = [valor exato]
- KPI 2: [nome] = [valor exato]
- KPI 3: [nome] = [valor exato]
- ...

Observações: [qualquer detalhe relevante]
```

#### **🔄 Ação Executada:**
```
Ação: [Descrever exatamente o que foi feito]
Elemento clicado: [ref do elemento]
Valor alterado: [de X para Y]
```

#### **📊 Estado Final:**
```
Perfil: [perfil após ação]
Timeframe: [período após ação]
Currency: [moeda após ação]

KPIs Visíveis:
- KPI 1: [nome] = [valor exato] (Δ: [diferença] = [%])
- KPI 2: [nome] = [valor exato] (Δ: [diferença] = [%])
- KPI 3: [nome] = [valor exato] (Δ: [diferença] = [%])
- ...

Tempo de resposta: [segundos para atualizar]
```

#### **📝 Verificação de Logs:**
```
Logs verificados: [Sim/Não]
Timestamp dos logs: [timestamp]
Evidências nos logs:
- [Log 1: evidência de processamento]
- [Log 2: confirmação de dados]
- [Log 3: erros encontrados]

Correlação logs ↔ frontend: [Sim/Não/Parcial]
```

#### **✅ Resultado do Cenário:**
- [ ] ✅ Funcionou conforme esperado
- [ ] ⚠️ Funcionou parcialmente
- [ ] ❌ Não funcionou

**Detalhes:** [Explicar o resultado]

---

### **CENÁRIO 2: [Nome do cenário]**
[Repetir estrutura acima]

---

### **CENÁRIO 3: [Nome do cenário]**
[Repetir estrutura acima]

---

## 📈 **ANÁLISE CONSOLIDADA**

### **📊 Tabela de Resultados:**

| Cenário | KPIs Testados | Valores Mudaram | Logs OK | Status |
|---------|---------------|-----------------|---------|--------|
| Cenário 1 | [número] | [Sim/Não] | [Sim/Não] | [✅/⚠️/❌] |
| Cenário 2 | [número] | [Sim/Não] | [Sim/Não] | [✅/⚠️/❌] |
| Cenário 3 | [número] | [Sim/Não] | [Sim/Não] | [✅/⚠️/❌] |

### **📊 Estatísticas:**
- **Total de cenários testados:** [número]
- **Cenários funcionando:** [número] ([%])
- **Cenários com problemas:** [número] ([%])
- **Cenários não funcionando:** [número] ([%])

---

## ✅ **FUNCIONALIDADES CONFIRMADAS**

### **✅ O que está funcionando corretamente:**
1. [Funcionalidade 1] - [Evidência específica]
2. [Funcionalidade 2] - [Evidência específica]
3. [Funcionalidade 3] - [Evidência específica]

---

## ⚠️ **PROBLEMAS IDENTIFICADOS**

### **⚠️ Funcionamento parcial:**
1. **Problema:** [Descrição específica]
   - **Evidência:** [Como foi identificado]
   - **Impacto:** [Gravidade do problema]
   - **Cenários afetados:** [Quais casos]

2. **Problema:** [Descrição específica]
   - **Evidência:** [Como foi identificado]
   - **Impacto:** [Gravidade do problema]
   - **Cenários afetados:** [Quais casos]

---

## ❌ **FALHAS CRÍTICAS**

### **❌ O que não está funcionando:**
1. **Falha:** [Descrição específica]
   - **Evidência:** [Valores zerados, erros nos logs, etc.]
   - **Impacto:** [Gravidade da falha]
   - **Cenários afetados:** [Quais casos]

2. **Falha:** [Descrição específica]
   - **Evidência:** [Valores zerados, erros nos logs, etc.]
   - **Impacto:** [Gravidade da falha]
   - **Cenários afetados:** [Quais casos]

---

## 🎯 **CONCLUSÃO FINAL**

### **Status Geral:** [✅ FUNCIONANDO / ⚠️ PARCIAL / ❌ NÃO FUNCIONANDO]

### **Resumo Executivo:**
[Parágrafo conciso explicando o estado real da funcionalidade baseado em evidências concretas]

### **Próximos Passos Recomendados:**
1. [Ação 1] - [Prioridade: Alta/Média/Baixa]
2. [Ação 2] - [Prioridade: Alta/Média/Baixa]
3. [Ação 3] - [Prioridade: Alta/Média/Baixa]

### **Evidências Anexas:**
- [ ] Screenshots dos estados inicial/final
- [ ] Logs do backend correlacionados
- [ ] Tabela de valores documentados
- [ ] Lista de problemas específicos

---

## 🔍 **CHECKLIST DE VALIDAÇÃO**

Antes de finalizar o teste, confirme:

- [ ] **Testei TODOS os cenários planejados?**
- [ ] **Documentei valores específicos (não apenas "mudou")?**
- [ ] **Verifiquei logs do backend para cada cenário?**
- [ ] **Identifiquei problemas explicitamente?**
- [ ] **Calculei percentuais de mudança quando relevante?**
- [ ] **Testei cenários negativos (casos que devem falhar)?**
- [ ] **Correlacionei resultados visuais com logs?**
- [ ] **Listei evidências concretas para cada conclusão?**
- [ ] **Evitei declarações vagas como "funcionando bem"?**
- [ ] **Forneci próximos passos específicos?**

---

**📝 LEMBRE-SE:** 
- Um teste que identifica 5 problemas específicos é mais valioso que um teste que declara "tudo funcionando" sem evidências
- Valores zerados ou estranhos são SEMPRE um sinal de problema, não de sucesso
- "Apareceu na tela" ≠ "Está funcionando corretamente"
