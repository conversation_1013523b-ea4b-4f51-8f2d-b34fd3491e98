# DataHero4 - Regras para Criação de Novos KPIs
## Guia Completo e Obrigatório

### 📋 **Visão Geral**

Este documento estabelece as regras obrigatórias para criar novos KPIs no DataHero4. **TODOS os novos KPIs devem seguir estas regras sem exceção**.

**Princípios Fundamentais**:
- ✅ **SEMPRE usar dados reais** da base de dados
- ❌ **NUNCA usar mock data, fallbacks ou hardcode**
- ✅ **Validar com MCP Postgres** antes da implementação
- ✅ **Seguir a arquitetura existente** sem refatoração

---

## 🎯 **Regra 1: Definição Obrigatória**

### **Arquivo**: `apps/backend/src/config/kpi_definitions.py`

**OBRIGATÓRIO**: Todo novo KPI deve ser adicionado ao dicionário `KPI_DEFINITIONS`:

```python
"novo_kpi_id": {
    "id": "novo_kpi_id",                    # OBRIGATÓRIO: ID único
    "name": "Nome Descritivo do KPI",       # OBRIGATÓRIO: Nome para exibição
    "description": "Descrição detalhada",   # OBRIGATÓRIO: Explicação clara
    "category": KpiCategory.VOLUME,         # OBRIGATÓRIO: VOLUME, PERFORMANCE, FINANCIAL, RISK
    "priority": KpiPriority.HIGH,           # OBRIGATÓRIO: CRITICAL, HIGH, MEDIUM, LOW
    "unit": "USD",                          # OBRIGATÓRIO: USD, percent, count, seconds
    "format_type": "currency",              # OBRIGATÓRIO: currency, percentage, number, duration
    "chart_type": "line",                   # OBRIGATÓRIO: line, area, bar, pie
    "display_order": 15,                    # OBRIGATÓRIO: Ordem de exibição (número único)
    "is_priority": True,                    # OBRIGATÓRIO: true/false
    "frequency": "daily",                   # OBRIGATÓRIO: realtime, daily, weekly, monthly
    "cache_ttl": 300,                       # OBRIGATÓRIO: TTL em segundos (300-3600)
    "thresholds": {                         # OPCIONAL: Alertas
        "warning": 1000,
        "critical": 500
    }
}
```

**VALIDAÇÕES OBRIGATÓRIAS**:
- ID deve ser único e seguir padrão `snake_case`
- `display_order` deve ser único (verificar números já usados)
- `cache_ttl` entre 300 (5min) e 3600 (1h) segundos
- Todos os campos obrigatórios devem estar presentes

---

## 🎯 **Regra 2: Mapeamento de Perfil**

### **Arquivo**: `apps/backend/src/config/profile_kpi_mapping.py`

**OBRIGATÓRIO**: Adicionar o KPI ao perfil apropriado:

```python
# Escolher o perfil correto:
CEO_PROFILE_KPIS = [
    # KPIs existentes...
    'novo_kpi_id',  # ADICIONAR AQUI
]

# Se for KPI crítico, adicionar também:
CRITICAL_KPIS_BY_PROFILE = {
    ProfileType.CEO: [
        # KPIs críticos existentes...
        'novo_kpi_id',  # ADICIONAR SE CRÍTICO
    ],
}
```

**PERFIS DISPONÍVEIS**:
- `CEO_PROFILE_KPIS` - KPIs estratégicos e financeiros
- `CFO_PROFILE_KPIS` - KPIs financeiros e de compliance
- `RISK_MANAGER_PROFILE_KPIS` - KPIs de risco e monitoramento
- `OPERATIONS_PROFILE_KPIS` - KPIs operacionais e de performance
- `TRADER_PROFILE_KPIS` - KPIs de trading e vendas

---

## 🎯 **Regra 3: Implementação do Cálculo**

### **Arquivo**: `apps/backend/src/services/hybrid_kpi_service.py`

**PASSO 1**: Adicionar ao mapeamento de calculadoras (linha ~77):

```python
self.kpi_calculators = {
    # KPIs existentes...
    'novo_kpi_id': self._calculate_novo_kpi_id,  # ADICIONAR ESTA LINHA
}
```

**PASSO 2**: Implementar o método de cálculo (final da classe):

```python
def _calculate_novo_kpi_id(
    self,
    client_id: str,
    timeframe: str = "month",
    currency: str = "all"
) -> Dict[str, Any]:
    """
    OBRIGATÓRIO: Docstring explicando o cálculo.
    
    Args:
        client_id: ID do cliente
        timeframe: Período de análise (month, quarter, year)
        currency: Moeda para filtro (all, USD, BRL)
        
    Returns:
        Dict com dados do KPI calculado
    """
    logger.info(f"📊 Calculating novo_kpi_id for timeframe: {timeframe}")
    
    try:
        # OBRIGATÓRIO: Construir filtro de data
        if timeframe == "month":
            date_filter = "data_operacao >= DATE_TRUNC('month', CURRENT_DATE)"
        elif timeframe == "quarter":
            date_filter = "data_operacao >= DATE_TRUNC('quarter', CURRENT_DATE)"
        elif timeframe == "year":
            date_filter = "data_operacao >= DATE_TRUNC('year', CURRENT_DATE)"
        else:
            date_filter = "data_operacao >= CURRENT_DATE - INTERVAL '30 days'"
        
        # OBRIGATÓRIO: Query SQL real (NUNCA usar mock data)
        query = f"""
        SELECT 
            COALESCE(SUM(campo_valor), 0) as kpi_value,
            COUNT(*) as record_count,
            AVG(campo_valor) as avg_value
        FROM tabela_real 
        WHERE {date_filter}
        AND campo_cliente = :client_id
        AND campo_valor IS NOT NULL
        """
        
        # OBRIGATÓRIO: Executar query com sessão do banco
        with self.db_manager.get_session() as session:
            result = session.execute(text(query), {"client_id": client_id})
            row = result.fetchone()
            
            # OBRIGATÓRIO: Tratar caso sem dados (SEM fallback)
            if not row:
                return {
                    'error': 'no_data',
                    'message': 'Nenhum dado encontrado para o período especificado',
                    'kpi_id': 'novo_kpi_id',
                    'timeframe': timeframe
                }
            
            kpi_value = float(row.kpi_value or 0)
            
            # OBRIGATÓRIO: Retornar estrutura padrão
            return {
                'kpi_id': 'novo_kpi_id',
                'title': 'Nome do KPI',
                'currentValue': kpi_value,
                'formattedValue': f"R$ {kpi_value:,.2f}",  # Ajustar formato
                'format': 'currency',  # Deve coincidir com kpi_definitions.py
                'source': 'hybrid_calculation',
                'metadata': {
                    'calculation_method': 'real_database_query',
                    'record_count': int(row.record_count or 0),
                    'avg_value': float(row.avg_value or 0),
                },
                'chartData': {
                    'type': 'line',  # Deve coincidir com kpi_definitions.py
                    'data': [],  # Dados para gráfico se necessário
                    'title': 'Evolução do KPI'
                },
                'timeframe': timeframe,
                'currency': currency
            }
            
    except Exception as e:
        logger.error(f"❌ Error calculating novo_kpi_id: {e}")
        # OBRIGATÓRIO: Retornar erro (SEM fallback)
        return {
            'error': 'calculation_failed',
            'message': f'Falha ao calcular KPI: {str(e)}',
            'kpi_id': 'novo_kpi_id',
            'timeframe': timeframe
        }
```

---

## 🎯 **Regra 4: Validação com MCP Postgres**

**OBRIGATÓRIO**: Antes de implementar, validar a query:

```sql
-- Testar a query exata que será usada
SELECT 
    COALESCE(SUM(campo_valor), 0) as kpi_value,
    COUNT(*) as record_count,
    AVG(campo_valor) as avg_value
FROM tabela_real 
WHERE data_operacao >= DATE_TRUNC('month', CURRENT_DATE)
AND campo_valor IS NOT NULL
LIMIT 5;
```

**VERIFICAÇÕES OBRIGATÓRIAS**:
- ✅ Query executa sem erro
- ✅ Retorna dados reais (não vazios)
- ✅ Performance aceitável (< 5 segundos)
- ✅ Campos existem na tabela
- ✅ Tipos de dados corretos

---

## 🎯 **Regra 5: Testes Obrigatórios**

### **Arquivo**: `apps/backend/tests/test_profile_personalization_api.py`

**OBRIGATÓRIO**: Criar teste para o novo KPI:

```python
def test_novo_kpi_calculation(self, kpi_calculator):
    """Test novo KPI calculation - OBRIGATÓRIO."""
    
    payload = {
        "user_id": "test_novo_kpi",
        "profile_type": "CEO",  # Ajustar conforme perfil
        "timeframe": "month",
        "currency": "all",
        "priority_only": False
    }
    
    response = requests.post(API_ENDPOINT, json=payload, timeout=30)
    assert response.status_code == 200
    
    data = response.json()
    kpis = data["kpis"]
    
    # OBRIGATÓRIO: Verificar presença do KPI
    novo_kpi = next((kpi for kpi in kpis if kpi["id"] == "novo_kpi_id"), None)
    assert novo_kpi is not None, "Novo KPI deve estar presente"
    
    # OBRIGATÓRIO: Validar estrutura
    assert novo_kpi["currentValue"] is not None, "KPI deve ter valor"
    assert novo_kpi["format"] == "currency", "Formato deve coincidir"
    assert "metadata" in novo_kpi, "Deve incluir metadata"
    
    # OBRIGATÓRIO: Validar metadata
    metadata = novo_kpi["metadata"]
    assert "calculation_method" in metadata
    assert "record_count" in metadata
    
    # OBRIGATÓRIO: Validar que não é mock data
    assert metadata["calculation_method"] == "real_database_query"
```

---

## ❌ **Regras de PROIBIÇÃO**

### **NUNCA FAZER**:

1. **❌ Mock Data ou Fallbacks**:
```python
# PROIBIDO - Nunca fazer isso
if not data:
    return {"currentValue": 1000}  # Mock data
```

2. **❌ Hardcode de Valores**:
```python
# PROIBIDO - Nunca fazer isso
kpi_value = 50000  # Valor fixo
```

3. **❌ Try/Catch com Fallback**:
```python
# PROIBIDO - Nunca fazer isso
try:
    real_calculation()
except:
    return mock_data()  # Fallback mascarando erro
```

4. **❌ Queries Genéricas**:
```python
# PROIBIDO - Nunca fazer isso
query = "SELECT 1000 as value"  # Query fake
```

---

## ✅ **Checklist de Implementação**

**ANTES DE COMEÇAR**:
- [ ] Validar query com MCP Postgres
- [ ] Confirmar que tabelas/campos existem
- [ ] Verificar performance da query

**DURANTE IMPLEMENTAÇÃO**:
- [ ] Adicionar definição em `kpi_definitions.py`
- [ ] Mapear para perfil em `profile_kpi_mapping.py`
- [ ] Implementar cálculo em `hybrid_kpi_service.py`
- [ ] Usar APENAS dados reais da base
- [ ] Seguir estrutura de retorno padrão

**APÓS IMPLEMENTAÇÃO**:
- [ ] Criar teste obrigatório
- [ ] Testar integração completa
- [ ] Verificar cache e performance
- [ ] Validar que não há fallbacks
- [ ] Confirmar que erros são expostos

---

## 🚨 **Consequências do Não Cumprimento**

**Se estas regras não forem seguidas**:
- ❌ KPI será rejeitado na revisão
- ❌ Problemas de debugging serão mascarados
- ❌ Dados inconsistentes no dashboard
- ❌ Performance degradada
- ❌ Falhas silenciosas em produção

**Filosofia**: "Fail fast and fail loud" - preferir falhas imediatas e visíveis a problemas ocultos que dificultam o debugging.

---

**📋 LEMBRE-SE**: Estas regras garantem qualidade, consistência e facilidade de manutenção. Não há exceções!
