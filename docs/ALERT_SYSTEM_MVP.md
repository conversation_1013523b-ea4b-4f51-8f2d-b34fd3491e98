# Sistema de Alertas MVP - DataHero4

## 🎯 Visão Geral

Este documento descreve a implementação do sistema de alertas MVP para o DataHero4, que adiciona indicadores visuais nos cards de KPI quando valores ultrapassam thresholds configurados.

---

## 📋 O Que Foi Implementado

### **1. Backend - Avaliação de Thresholds**

#### **Arquivo Modificado:** `apps/backend/src/services/hybrid_kpi_service.py`

**Função Adicionada:**
```python
def _evaluate_kpi_alert(kpi_id: str, current_value: float) -> Optional[Dict[str, Any]]:
    """
    Avalia se um KPI dispara alerta baseado nos thresholds configurados.
    
    Retorna:
    - None: Sem alerta
    - Dict: Objeto de alerta com type, message, threshold, severity, isActive
    """
```

**Lógica Implementada:**
- **Higher is Better KPIs:** Alerta quando valor < threshold (receita, volume, etc.)
- **Lower is Better KPIs:** Alerta quando valor > threshold (custos, tempo de processamento)
- **Tipos de Alerta:** `warning` e `critical` baseados nos thresholds configurados
- **Severidade:** `medium` para warning, `high` para critical

**KPIs Atualizados com Alertas:**
- `spread_income_detailed`
- `margem_liquida_operacional` 
- `custo_por_transacao`
- `tempo_processamento_medio`

**Exemplo de Retorno:**
```json
{
  "kpi_id": "spread_income_detailed",
  "currentValue": 25000,
  "alert": {
    "type": "critical",
    "message": "Valor crítico: 25000 abaixo do limite de 50000",
    "threshold": 50000,
    "severity": "high",
    "isActive": true
  }
}
```

---

### **2. Frontend - Indicadores Visuais**

#### **Componentes Modificados:**

1. **`KpiBentoCard.tsx`**
2. **`KpiTopRankingCard.tsx`**  
3. **`KpiHorizontalBarCard.tsx`**

#### **Implementação Visual:**

**Ícone de Alerta:**
```tsx
{kpi.alert?.isActive && (
  <AlertCircle className={cn(
    "flex-shrink-0",
    kpi.alert.type === 'critical' ? "text-red-500" : "text-amber-500",
    isLarge ? "w-5 h-5" : "w-4 h-4"
  )} />
)}
```

**Border Styling:**
```tsx
// Já existia, foi mantido
kpi.alert?.isActive ? "border-amber-400 border-2" : "border-gray-200"
```

**Características:**
- **Ícone:** `AlertCircle` do Lucide React
- **Cores:** Vermelho para `critical`, Âmbar para `warning`  
- **Posição:** No cabeçalho do card, próximo ao título
- **Border:** Âmbar para todos os alertas ativos
- **Responsivo:** Tamanhos diferentes para cards grandes/pequenos

---

## 🔧 Configuração de Thresholds

### **Arquivo:** `apps/backend/src/config/kpi_definitions.py`

**Estrutura:**
```python
"total_volume": {
    "thresholds": {
        "warning": 1000000,    # Alerta amarelo
        "critical": 500000     # Alerta vermelho
    }
},
"custo_por_transacao": {
    "thresholds": {
        "warning": 5.0,        # Alerta se > 5.0
        "critical": 10.0       # Alerta crítico se > 10.0
    }
}
```

**KPIs com Thresholds Configurados:**
- `total_volume`: warning: 1M, critical: 500K
- `average_spread`: warning: 0.05, critical: 0.10  
- `average_ticket`: warning: 1K, critical: 500
- `conversion_rate`: warning: 0.15, critical: 0.10
- `retention_rate`: warning: 0.70, critical: 0.50
- `compliance_score`: warning: 80, critical: 70
- `spread_income_detailed`: warning: 50K, critical: 25K
- `margem_liquida_operacional`: warning: 15%, critical: 10%
- `custo_por_transacao`: warning: 5.0, critical: 10.0
- `tempo_processamento_medio`: warning: 30s, critical: 60s

---

## 🎨 Padrão Visual

### **Estados do Card:**

1. **Normal:** Border cinza, sem ícone
2. **Warning:** Border âmbar + ícone âmbar  
3. **Critical:** Border âmbar + ícone vermelho
4. **Priority:** Ring âmbar (mantido, independente de alerta)
5. **Selected:** Border azul + ring azul (sobrepõe alerta)

### **Hierarchy Visual:**
```
Selected > Critical Alert > Warning Alert > Priority > Normal
```

---

## 🚀 Como Funciona

### **Fluxo Completo:**

1. **Requisição KPI:** Frontend solicita KPI via API
2. **Cálculo:** Backend calcula valor atual do KPI  
3. **Avaliação:** `_evaluate_kpi_alert()` compara com thresholds
4. **Resposta:** API retorna KPI + campo `alert` (se aplicável)
5. **Renderização:** Frontend mostra ícone e border se `alert.isActive = true`

### **Exemplo de Uso:**

**Cenário:** KPI "Spread Income" com valor 30.000
- **Threshold Warning:** 50.000
- **Threshold Critical:** 25.000
- **Resultado:** Alerta Warning (30K < 50K mas > 25K)
- **Visual:** Border âmbar + ícone âmbar

---

## 📊 Interface TypeScript

### **Tipo Existente:** `KpiAlert`
```typescript
export interface KpiAlert {
  id?: string;
  type: 'warning' | 'critical' | 'info' | 'above' | 'below';
  message: string;
  threshold?: number;
  severity?: 'low' | 'medium' | 'high';
  isActive?: boolean;
}
```

### **Integração:** `KpiData`
```typescript
export interface KpiData {
  id: string;
  currentValue: number;
  alert?: KpiAlert;  // ← Campo de alerta
  // ... outros campos
}
```

---

## ✅ Status da Implementação

### **Completo:**
- [x] Função de avaliação de threshold no backend
- [x] Integração com 4 KPIs principais
- [x] Indicadores visuais em todos os tipos de card
- [x] Diferenciação visual entre warning/critical
- [x] Configuração de thresholds existente

### **Testado:**
- [x] Lógica de avaliação de threshold
- [x] Renderização dos componentes
- [x] Integração TypeScript

### **Pendente:**
- [ ] Teste visual com dados reais que disparam alertas
- [ ] Validação em diferentes cenários de dados
- [ ] Teste de performance com muitos alertas ativos

---

## 🎯 Expansões Futuras

### **Curto Prazo:**
1. **Tooltip:** Mostrar detalhes do threshold ao hover no ícone
2. **Animação:** Pulsar ou piscar para alertas críticos  
3. **Mais KPIs:** Estender para todos os 34 KPIs disponíveis

### **Médio Prazo:**
1. **Dashboard de Alertas:** Página centralizada de alertas
2. **Configuração Dinâmica:** Permitir usuário ajustar thresholds
3. **Histórico:** Tracking de quando alertas foram disparados
4. **Notificações:** Push/email quando alertas críticos ocorrem

### **Longo Prazo:**
1. **Machine Learning:** Sugestão automática de thresholds
2. **Alertas Contextuais:** Baseados em perfil do usuário
3. **Integração:** Slack, Teams, webhooks
4. **Alertas Temporais:** "Se métrica X não melhorar em Y dias"

---

## 🔍 Como Testar

### **Backend:**
```bash
# Testar endpoint individual
curl "http://localhost:8000/api/v1/kpis/spread_income_detailed?client_id=L2M&sector=cambio&timeframe=week&currency=all"

# Verificar se campo 'alert' está presente na resposta
```

### **Frontend:**
1. Acessar `http://localhost:3000`
2. Observar cards no dashboard
3. Procurar por:
   - Ícones `AlertCircle` nos cabeçalhos
   - Borders âmbar em cards
   - Cores diferentes (âmbar vs vermelho)

### **Forçar Alertas:**
1. **Modificar thresholds** em `kpi_definitions.py` para valores altos
2. **Restart backend**
3. **Verificar** se alertas aparecem

---

## 📝 Arquivos Modificados

```
apps/backend/src/services/hybrid_kpi_service.py
├── _evaluate_kpi_alert() [NOVA FUNÇÃO]
├── _calculate_spread_income_detailed() [+ alerta]
├── _calculate_margem_liquida_operacional() [+ alerta]  
├── _calculate_custo_por_transacao() [+ alerta]
└── _calculate_tempo_processamento_medio() [+ alerta]

apps/frontend/src/components/dashboard/
├── KpiBentoCard.tsx [+ ícone alerta]
├── KpiTopRankingCard.tsx [+ ícone alerta]
└── KpiHorizontalBarCard.tsx [+ ícone alerta + border]

apps/backend/src/config/kpi_definitions.py [JÁ EXISTIA]
├── thresholds configurados para 10 KPIs

apps/frontend/src/types/kpi.ts [JÁ EXISTIA]  
├── interface KpiAlert
└── interface KpiData com campo alert
```

---

## 🎨 Preview Visual

```
╔══════════════════════════════════════╗
║ 🏆 Spread Income  ⚠️               ║  <- Ícone âmbar
║ Receita de spreads cambiais          ║
║                                      ║
║ $25.000 📈 +5%                      ║
║ ▁▂▃▅▆▇█                            ║
╚══════════════════════════════════════╝  <- Border âmbar
```

**vs**

```
╔══════════════════════════════════════╗
║ 💰 Volume Total                      ║  <- Sem alerta
║ Volume total negociado               ║
║                                      ║
║ $2.5M 📈 +12%                       ║
║ ▁▂▃▅▆▇█                            ║
╚══════════════════════════════════════╝  <- Border cinza
```

---

## 💡 Observações Técnicas

### **Performance:**
- Avaliação de alerta é O(1) - lookup simples no dicionário
- Impacto mínimo no tempo de resposta da API
- Renderização condicional no frontend (só renderiza se `isActive`)

### **Escalabilidade:**
- Fácil adicionar novos KPIs ao sistema
- Configuração centralizada em `kpi_definitions.py`
- Componentes reutilizáveis para diferentes tipos de card

### **Manutenibilidade:**
- Lógica isolada em função específica
- TypeScript garante type safety
- Padrão visual consistente entre componentes

---

**🎯 Este MVP fornece uma base sólida para um sistema de alertas mais avançado, mantendo simplicidade e performance.**