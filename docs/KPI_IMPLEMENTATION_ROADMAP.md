# DataHero4 KPI Implementation Roadmap
## Comprehensive Plan for Remaining 70+ KPIs

### 📊 **Executive Summary**

Based on comprehensive database schema analysis using MCP Postgres, this document provides a detailed roadmap for implementing the remaining **72 KPIs** across all user profiles. Our validation shows **88% of tested KPIs are immediately implementable** with the current L2M database schema.

**Current Status**: 4 KPIs implemented (6% of total planned)  
**Implementable with current data**: 58 KPIs (81% of remaining)  
**Requires external data**: 14 KPIs (19% of remaining)

---

## 🗄️ **Database Schema Analysis Results**

### **Available Tables (89 total)**
- **Core Business**: `boleta` (23,684 records), `pessoa` (6,071), `conta_receber` (5,454)
- **Financial**: `operational_costs`, `taxa_cambio_oficial` (42,422 records)
- **Compliance**: `pld_compliance` (145), `tb_compliance_score` (131), `tb_risk_factor` (170)
- **Relationships**: `boleta_moeda`, `boleta_status`, `usuario`, `perfil`

### **Data Availability Validation**
- ✅ **January 2025**: 432 operations, 155 unique clients, 8 currencies
- ✅ **Historical Data**: 2016-2025 (23,684 total operations)
- ✅ **Complete Cost Structure**: All operational cost fields populated
- ⚠️ **Spread Data**: Zero spread (taxa_cambio = taxa_base) - policy or test data

---

## 📋 **KPI Implementation Matrix by Profile**

### **🏢 CEO Profile: 16 KPIs Planned → 14 Implementable**

| KPI | Status | Data Source | Implementation Complexity |
|-----|--------|-------------|---------------------------|
| ✅ `spread_income_detailed` | **IMPLEMENTED** | boleta + boleta_moeda | ✅ Done |
| ✅ `margem_liquida_operacional` | **IMPLEMENTED** | boleta + operational_costs | ✅ Done |
| 🟢 `roe_return_on_equity` | **HIGH PRIORITY** | conta_receber + conta_pagar | 🟡 Medium |
| 🟢 `receita_total_mensal` | **HIGH PRIORITY** | conta_receber | 🟢 Easy |
| 🟢 `crescimento_receita_yoy` | **HIGH PRIORITY** | conta_receber (historical) | 🟡 Medium |
| 🟢 `adequacao_capital_regulatorio` | **IMPLEMENTABLE** | pld_compliance + tb_compliance_score | 🟡 Medium |
| 🟢 `concentracao_top10_clientes` | **IMPLEMENTABLE** | boleta + pessoa | 🟢 Easy |
| 🟢 `exposicao_maxima_consolidada` | **IMPLEMENTABLE** | boleta (by currency) | 🟡 Medium |
| 🟢 `volume_vs_concorrentes` | **IMPLEMENTABLE** | boleta + taxa_cambio_oficial | 🟡 Medium |
| 🔴 `market_share_estimado` | **EXTERNAL DATA** | Industry reports needed | 🔴 Hard |
| 🔴 `nps_consolidado` | **EXTERNAL DATA** | Customer feedback system | 🔴 Hard |
| 🔴 `brand_awareness` | **EXTERNAL DATA** | Marketing surveys | 🔴 Hard |
| 🔴 `stress_test_resultado` | **EXTERNAL DATA** | Risk modeling system | 🔴 Hard |
| 🔴 `cac_customer_acquisition_cost` | **EXTERNAL DATA** | Marketing spend data | 🔴 Hard |
| 🔴 `ltv_lifetime_value` | **IMPLEMENTABLE** | boleta + pessoa (historical) | 🟡 Medium |
| 🔴 `taxa_crescimento_clientes` | **IMPLEMENTABLE** | pessoa (creation dates) | 🟢 Easy |

### **💰 CFO Profile: 16 KPIs Planned → 13 Implementable**

| KPI | Status | Data Source | Implementation Complexity |
|-----|--------|-------------|---------------------------|
| ✅ `margem_liquida_operacional` | **IMPLEMENTED** | boleta + operational_costs | ✅ Done |
| 🟢 `margem_bruta_por_produto` | **HIGH PRIORITY** | boleta (by currency/type) | 🟢 Easy |
| 🟢 `ebitda_mensal` | **HIGH PRIORITY** | conta_receber + conta_pagar | 🟡 Medium |
| 🟢 `custo_operacional_por_transacao` | **HIGH PRIORITY** | boleta (cost fields) | 🟢 Easy |
| 🟢 `cash_flow_operacional` | **IMPLEMENTABLE** | conta_receber + conta_pagar | 🟡 Medium |
| 🟢 `working_capital_ratio` | **IMPLEMENTABLE** | conta_receber + conta_pagar | 🟡 Medium |
| 🟢 `dias_recebimento_medio` | **IMPLEMENTABLE** | conta_receber (payment dates) | 🟡 Medium |
| 🟢 `liquidez_imediata` | **IMPLEMENTABLE** | conta_receber + conta_pagar | 🟡 Medium |
| 🟢 `roe_return_on_equity` | **IMPLEMENTABLE** | Financial statements calc | 🟡 Medium |
| 🟢 `roa_return_on_assets` | **IMPLEMENTABLE** | Financial statements calc | 🟡 Medium |
| 🟢 `debt_to_equity_ratio` | **IMPLEMENTABLE** | conta_pagar analysis | 🟡 Medium |
| 🟢 `capital_efficiency_ratio` | **IMPLEMENTABLE** | Revenue vs capital calc | 🟡 Medium |
| 🟢 `adequacao_capital_basileia` | **IMPLEMENTABLE** | pld_compliance + risk factors | 🟡 Medium |
| 🟢 `provisoes_perdas_credito` | **IMPLEMENTABLE** | conta_receber (aging) | 🟡 Medium |
| 🟢 `auditoria_compliance_score` | **IMPLEMENTABLE** | tb_compliance_score | 🟢 Easy |
| 🟢 `regulatory_capital_ratio` | **IMPLEMENTABLE** | pld_compliance calculations | 🟡 Medium |

### **⚠️ Risk Manager Profile: 16 KPIs Planned → 12 Implementable**

| KPI | Status | Data Source | Implementation Complexity |
|-----|--------|-------------|---------------------------|
| ✅ `tempo_processamento_medio` | **IMPLEMENTED** | boleta (date calculations) | ✅ Done |
| 🟢 `var_diario_por_moeda` | **HIGH PRIORITY** | boleta + taxa_cambio_oficial | 🟡 Medium |
| 🟢 `exposicao_liquida_consolidada` | **HIGH PRIORITY** | boleta (currency positions) | 🟡 Medium |
| 🟢 `utilizacao_limites_cliente` | **IMPLEMENTABLE** | pld_compliance (saldo_limite) | 🟢 Easy |
| 🟢 `utilizacao_limites_moeda` | **IMPLEMENTABLE** | boleta + predefined limits | 🟡 Medium |
| 🟢 `limite_contraparte_utilizacao` | **IMPLEMENTABLE** | boleta + pessoa analysis | 🟡 Medium |
| 🟢 `default_rate_clientes` | **IMPLEMENTABLE** | conta_receber (overdue) | 🟡 Medium |
| 🟢 `aging_receivables` | **IMPLEMENTABLE** | conta_receber (aging buckets) | 🟢 Easy |
| 🟢 `credit_score_medio_carteira` | **IMPLEMENTABLE** | tb_compliance_score | 🟢 Easy |
| 🟢 `provisao_devedores_duvidosos` | **IMPLEMENTABLE** | conta_receber analysis | 🟡 Medium |
| 🟢 `aml_alerts_pendentes` | **IMPLEMENTABLE** | pld_compliance (status) | 🟢 Easy |
| 🟢 `kyc_completion_rate` | **IMPLEMENTABLE** | pld_compliance analysis | 🟢 Easy |
| 🟢 `regulatory_breaches_count` | **IMPLEMENTABLE** | pld_compliance (violations) | 🟡 Medium |
| 🔴 `stress_test_cenarios` | **EXTERNAL DATA** | Risk modeling required | 🔴 Hard |
| 🔴 `correlacao_posicoes` | **EXTERNAL DATA** | Advanced analytics | 🔴 Hard |
| 🔴 `overnight_position_limits` | **EXTERNAL DATA** | Real-time position data | 🔴 Hard |
| 🔴 `suspicious_activity_reports` | **EXTERNAL DATA** | AML system integration | 🔴 Hard |

### **⚙️ Operations Profile: 16 KPIs Planned → 14 Implementable**

| KPI | Status | Data Source | Implementation Complexity |
|-----|--------|-------------|---------------------------|
| ✅ `custo_por_transacao` | **IMPLEMENTED** | operational_costs + boleta | ✅ Done |
| ✅ `tempo_processamento_medio` | **IMPLEMENTED** | boleta (date calculations) | ✅ Done |
| 🟢 `throughput_transacoes_hora` | **HIGH PRIORITY** | boleta (hourly analysis) | 🟢 Easy |
| 🟢 `produtividade_equipe` | **HIGH PRIORITY** | boleta + usuario | 🟢 Easy |
| 🟢 `taxa_erro_transacoes` | **IMPLEMENTABLE** | boleta_status (error analysis) | 🟡 Medium |
| 🟢 `sla_compliance_rate` | **IMPLEMENTABLE** | boleta (processing times) | 🟡 Medium |
| 🟢 `utilizacao_capacidade_sistema` | **IMPLEMENTABLE** | boleta (peak analysis) | 🟡 Medium |
| 🟢 `fila_processamento_tamanho` | **IMPLEMENTABLE** | boleta_status (pending) | 🟢 Easy |
| 🟢 `recursos_humanos_utilizacao` | **IMPLEMENTABLE** | usuario + boleta analysis | 🟡 Medium |
| 🟢 `peak_load_handling` | **IMPLEMENTABLE** | boleta (hourly peaks) | 🟡 Medium |
| 🟢 `tempo_resolucao_incidentes` | **IMPLEMENTABLE** | boleta_status transitions | 🟡 Medium |
| 🟢 `automation_rate_processos` | **IMPLEMENTABLE** | boleta (manual vs auto) | 🟡 Medium |
| 🟢 `process_improvement_score` | **IMPLEMENTABLE** | Efficiency metrics calc | 🟡 Medium |
| 🔴 `uptime_sistema_percentual` | **EXTERNAL DATA** | System monitoring | 🔴 Hard |
| 🔴 `first_call_resolution` | **EXTERNAL DATA** | Support system | 🔴 Hard |
| 🔴 `suggestions_implemented` | **EXTERNAL DATA** | Process improvement tracking | 🔴 Hard |

### **📈 Trader Profile: 8 KPIs Planned → 6 Implementable**

| KPI | Status | Data Source | Implementation Complexity |
|-----|--------|-------------|---------------------------|
| ✅ `spread_income_detailed` | **IMPLEMENTED** | boleta + boleta_moeda | ✅ Done |
| 🟢 `volume_vendas_mensal` | **HIGH PRIORITY** | boleta (sales analysis) | 🟢 Easy |
| 🟢 `numero_novos_clientes` | **HIGH PRIORITY** | pessoa (creation tracking) | 🟢 Easy |
| 🟢 `ticket_medio_por_vendedor` | **IMPLEMENTABLE** | boleta + usuario | 🟢 Easy |
| 🟢 `conversao_leads_clientes` | **IMPLEMENTABLE** | pessoa (conversion analysis) | 🟡 Medium |
| 🟢 `nps_por_segmento` | **IMPLEMENTABLE** | Customer feedback integration | 🟡 Medium |
| 🟢 `churn_rate_mensal` | **IMPLEMENTABLE** | boleta + pessoa (activity) | 🟡 Medium |
| 🔴 `upselling_success_rate` | **EXTERNAL DATA** | Sales tracking system | 🔴 Hard |

---

## 🎯 **Implementation Priority Matrix**

### **Phase 1: Quick Wins (4-6 weeks) - 18 KPIs**
**Target**: High-impact KPIs with existing data

```sql
-- Example: Revenue Total Monthly (CFO Priority)
SELECT 
    DATE_TRUNC('month', data_emissao) as month,
    SUM(valor_total) as total_revenue,
    COUNT(*) as invoice_count,
    AVG(valor_total) as avg_invoice_value
FROM conta_receber 
WHERE data_emissao >= DATE_TRUNC('year', CURRENT_DATE)
GROUP BY DATE_TRUNC('month', data_emissao)
ORDER BY month;
```

**Phase 1 KPIs**:
- `receita_total_mensal` (CFO)
- `concentracao_top10_clientes` (CEO) 
- `margem_bruta_por_produto` (CFO)
- `throughput_transacoes_hora` (Operations)
- `produtividade_equipe` (Operations)
- `volume_vendas_mensal` (Trader)
- `numero_novos_clientes` (Trader)
- `utilizacao_limites_cliente` (Risk Manager)
- `aging_receivables` (Risk Manager)
- `credit_score_medio_carteira` (Risk Manager)
- `aml_alerts_pendentes` (Risk Manager)
- `kyc_completion_rate` (Risk Manager)
- `custo_operacional_por_transacao` (CFO)
- `auditoria_compliance_score` (CFO)
- `fila_processamento_tamanho` (Operations)
- `ticket_medio_por_vendedor` (Trader)
- `taxa_crescimento_clientes` (CEO)
- `ltv_lifetime_value` (CEO)

### **Phase 2: Medium Complexity (6-10 weeks) - 28 KPIs**
**Target**: KPIs requiring calculations and joins

```sql
-- Example: VaR Daily by Currency (Risk Manager Priority)
WITH daily_returns AS (
    SELECT 
        bm.simbolo,
        b.data_operacao,
        b.taxa_cambio,
        LAG(b.taxa_cambio) OVER (PARTITION BY bm.simbolo ORDER BY b.data_operacao) as prev_rate,
        LN(b.taxa_cambio / LAG(b.taxa_cambio) OVER (PARTITION BY bm.simbolo ORDER BY b.data_operacao)) as daily_return
    FROM boleta b
    JOIN boleta_moeda bm ON b.id_moeda = bm.id
    WHERE b.data_operacao >= CURRENT_DATE - INTERVAL '252 days'
)
SELECT 
    simbolo,
    PERCENTILE_CONT(0.05) WITHIN GROUP (ORDER BY daily_return) * SQRT(1) as var_1_day_95,
    STDDEV(daily_return) * SQRT(1) as volatility_1_day
FROM daily_returns 
WHERE daily_return IS NOT NULL
GROUP BY simbolo;
```

### **Phase 3: Advanced Analytics (10-16 weeks) - 12 KPIs**
**Target**: Complex financial and risk calculations

### **Phase 4: External Integration (16+ weeks) - 14 KPIs**
**Target**: KPIs requiring external data sources

---

## 🏗️ **Technical Architecture**

### **Integration with Existing KPI Storage**
```
# EXISTING STRUCTURE (DO NOT CHANGE)
apps/backend/src/config/
├── setores/cambio/
│   ├── kpis-exchange-json.json     # EXISTING: KPI definitions
│   ├── L2M/L2M_schema_relevance.json # EXISTING: Schema mapping
│   └── kpi_queries.json            # EXISTING: Query templates
├── kpi_definitions.py              # EXISTING: KPI configuration
└── profile_kpi_mapping.py          # NEW: Profile-specific KPI mapping

# NEW ADDITIONS (EXTEND EXISTING)
apps/backend/src/config/profiles/
├── ceo_profile_kpis.py             # CEO-specific KPI list
├── cfo_profile_kpis.py             # CFO-specific KPI list
├── risk_manager_profile_kpis.py    # Risk Manager KPI list
├── operations_profile_kpis.py      # Operations KPI list
└── trader_profile_kpis.py          # Trader KPI list
```

### **Extending HybridKpiService**
```python
# EXISTING: HybridKpiService (4 KPIs implemented)
# EXTEND: Add new KPI calculators to existing mapping

class HybridKpiService:
    def __init__(self):
        # EXISTING: Current 4 KPIs
        self.kpi_calculators = {
            'spread_income_detailed': self._calculate_spread_income_detailed,
            'margem_liquida_operacional': self._calculate_margem_liquida_operacional,
            'custo_por_transacao': self._calculate_custo_por_transacao,
            'tempo_processamento_medio': self._calculate_tempo_processamento_medio,

            # NEW: Phase 1 additions (18 KPIs)
            'receita_total_mensal': self._calculate_receita_total_mensal,
            'concentracao_top10_clientes': self._calculate_concentracao_clientes,
            'margem_bruta_por_produto': self._calculate_margem_bruta_produto,
            'throughput_transacoes_hora': self._calculate_throughput_hora,
            'produtividade_equipe': self._calculate_produtividade_equipe,
            # ... additional KPIs
        }
```

### **Integration with Existing Architecture**

#### **Existing Services to Leverage**
```python
# Current Architecture Components (DO NOT REFACTOR)
- SmartQueryRouter: 3-layer routing (Snapshot → Cache → Direct)
- PersonalizedCacheSystem: Profile-aware TTL strategies
- ProfileAwareSnapshotService: Daily snapshots with profile optimization
- HybridKpiService: 4 KPIs already implemented
- UnifiedCacheSystem: LRU cache with intelligent TTL
- KpiRepository: Repository pattern for data access
```

#### **Existing TTL Configuration (PersonalizedCacheSystem)**
```python
# Profile-specific TTL (ALREADY IMPLEMENTED)
profile_ttl_config = {
    'CEO': {
        'kpi:value': 3600,      # 1 hour - strategic data
        'kpi:chart': 3600,      # 1 hour
        'query:result': 1800,   # 30 minutes
    },
    'CFO': {
        'kpi:value': 1800,      # 30 minutes - financial data
        'kpi:chart': 1800,      # 30 minutes
        'query:result': 900,    # 15 minutes
    },
    'Risk_Manager': {
        'kpi:value': 300,       # 5 minutes - risk data (real-time)
        'kpi:chart': 300,       # 5 minutes
        'query:result': 180,    # 3 minutes
    },
    'Trader': {
        'kpi:value': 60,        # 1 minute - operational data
        'kpi:chart': 60,        # 1 minute
        'query:result': 30,     # 30 seconds
    },
    'Operations': {
        'kpi:value': 900,       # 15 minutes - operational efficiency
        'kpi:chart': 900,       # 15 minutes
        'query:result': 600,    # 10 minutes
    }
}
```

#### **Existing Routing Strategy (SmartQueryRouter)**
```python
# Profile-based routing (ALREADY IMPLEMENTED)
routing_strategy = {
    'CEO': {
        'preferred_layer': QueryLayer.SNAPSHOT,  # Daily snapshots
        'max_wait_time': 10.0,  # 10 seconds max
    },
    'CFO': {
        'preferred_layer': QueryLayer.SNAPSHOT,  # Daily snapshots
        'max_wait_time': 15.0,  # 15 seconds max
    },
    'Risk_Manager': {
        'preferred_layer': QueryLayer.DIRECT,    # Real-time queries
        'max_wait_time': 5.0,   # 5 seconds max
    },
    'Trader': {
        'preferred_layer': QueryLayer.CACHE,     # Hot cache
        'max_wait_time': 2.0,   # 2 seconds max
    },
    'Operations': {
        'preferred_layer': QueryLayer.CACHE,     # Warm cache
        'max_wait_time': 8.0,   # 8 seconds max
    }
}
```

### **Performance Targets (Existing)**
- **API Response Time**: <10s (CEO), <5s (Risk), <2s (Trader), <8s (Operations)
- **Cache Hit Rate**: Managed by UnifiedCacheSystem LRU
- **Concurrent Users**: Handled by existing FastAPI + SQLAlchemy pool

---

## 📊 **Success Metrics**

### **Implementation KPIs**
- **Coverage**: 72/76 total KPIs (95% target)
- **Data Quality**: >95% successful calculations
- **Performance**: All response times within targets
- **User Adoption**: >80% daily active usage per profile

### **Business Impact**
- **Decision Speed**: 50% faster executive decisions
- **Risk Detection**: 90% faster risk identification  
- **Operational Efficiency**: 30% improvement in process metrics
- **Compliance**: 100% regulatory KPI coverage

---

---

## 🔧 **Detailed Query Implementation Examples**

### **CEO Profile - High Priority KPIs**

#### **1. Client Concentration (concentracao_top10_clientes)**
```sql
-- KPI: Top 10 Client Concentration Risk
WITH client_volumes AS (
    SELECT
        p.nome as client_name,
        p.id_pessoa as client_id,
        SUM(b.valor_me) as total_volume,
        COUNT(*) as operation_count,
        ROUND((SUM(b.valor_me) / (SELECT SUM(valor_me) FROM boleta WHERE data_operacao >= :start_date AND data_operacao <= :end_date)) * 100, 2) as percentage_of_total
    FROM boleta b
    JOIN pessoa p ON b.id_cliente = p.id_pessoa
    WHERE b.data_operacao >= :start_date AND b.data_operacao <= :end_date
    GROUP BY p.id_pessoa, p.nome
    ORDER BY total_volume DESC
    LIMIT 10
),
concentration_metrics AS (
    SELECT
        SUM(percentage_of_total) as top10_concentration,
        SUM(CASE WHEN ROW_NUMBER() OVER (ORDER BY total_volume DESC) <= 5 THEN percentage_of_total ELSE 0 END) as top5_concentration,
        MAX(percentage_of_total) as largest_client_percentage
    FROM client_volumes
)
SELECT
    cv.*,
    cm.top10_concentration,
    cm.top5_concentration,
    cm.largest_client_percentage,
    CASE
        WHEN cm.top10_concentration > 80 THEN 'HIGH_RISK'
        WHEN cm.top10_concentration > 60 THEN 'MEDIUM_RISK'
        ELSE 'LOW_RISK'
    END as concentration_risk_level
FROM client_volumes cv
CROSS JOIN concentration_metrics cm;
```

#### **2. Revenue Growth YoY (crescimento_receita_yoy)**
```sql
-- KPI: Year-over-Year Revenue Growth
WITH monthly_revenue AS (
    SELECT
        DATE_TRUNC('month', data_emissao) as month,
        SUM(valor_total) as monthly_revenue,
        COUNT(*) as invoice_count
    FROM conta_receber
    WHERE data_emissao >= DATE_TRUNC('year', CURRENT_DATE) - INTERVAL '1 year'
    GROUP BY DATE_TRUNC('month', data_emissao)
),
yoy_comparison AS (
    SELECT
        month,
        monthly_revenue,
        LAG(monthly_revenue, 12) OVER (ORDER BY month) as same_month_last_year,
        CASE
            WHEN LAG(monthly_revenue, 12) OVER (ORDER BY month) > 0
            THEN ROUND(((monthly_revenue - LAG(monthly_revenue, 12) OVER (ORDER BY month)) / LAG(monthly_revenue, 12) OVER (ORDER BY month)) * 100, 2)
            ELSE NULL
        END as yoy_growth_percent
    FROM monthly_revenue
)
SELECT
    month,
    monthly_revenue,
    same_month_last_year,
    yoy_growth_percent,
    CASE
        WHEN yoy_growth_percent > 15 THEN 'EXCELLENT'
        WHEN yoy_growth_percent > 5 THEN 'GOOD'
        WHEN yoy_growth_percent > 0 THEN 'POSITIVE'
        WHEN yoy_growth_percent < -10 THEN 'CONCERNING'
        ELSE 'STABLE'
    END as growth_category
FROM yoy_comparison
WHERE month >= DATE_TRUNC('year', CURRENT_DATE)
ORDER BY month;
```

### **CFO Profile - High Priority KPIs**

#### **3. EBITDA Monthly (ebitda_mensal)**
```sql
-- KPI: Monthly EBITDA Calculation
WITH monthly_financials AS (
    SELECT
        DATE_TRUNC('month', cr.data_emissao) as month,
        SUM(cr.valor_total) as gross_revenue,
        SUM(cr.valor_iss + cr.valor_pis + cr.valor_cofins + cr.valor_csll + cr.valor_irpj) as taxes,
        SUM(cr.valor_total - (cr.valor_iss + cr.valor_pis + cr.valor_cofins + cr.valor_csll + cr.valor_irpj)) as net_revenue
    FROM conta_receber cr
    WHERE cr.data_emissao >= DATE_TRUNC('year', CURRENT_DATE)
    GROUP BY DATE_TRUNC('month', cr.data_emissao)
),
monthly_costs AS (
    SELECT
        DATE_TRUNC('month', oc.date) as month,
        SUM(oc.amount) as operational_costs,
        SUM(CASE WHEN oc.category = 'personnel' THEN oc.amount ELSE 0 END) as personnel_costs,
        SUM(CASE WHEN oc.category = 'infrastructure' THEN oc.amount ELSE 0 END) as infrastructure_costs
    FROM operational_costs oc
    WHERE oc.date >= DATE_TRUNC('year', CURRENT_DATE)
    GROUP BY DATE_TRUNC('month', oc.date)
)
SELECT
    COALESCE(mf.month, mc.month) as month,
    COALESCE(mf.gross_revenue, 0) as gross_revenue,
    COALESCE(mf.net_revenue, 0) as net_revenue,
    COALESCE(mc.operational_costs, 0) as operational_costs,
    COALESCE(mf.net_revenue, 0) - COALESCE(mc.operational_costs, 0) as ebitda,
    CASE
        WHEN COALESCE(mf.gross_revenue, 0) > 0
        THEN ROUND(((COALESCE(mf.net_revenue, 0) - COALESCE(mc.operational_costs, 0)) / mf.gross_revenue) * 100, 2)
        ELSE 0
    END as ebitda_margin_percent
FROM monthly_financials mf
FULL OUTER JOIN monthly_costs mc ON mf.month = mc.month
ORDER BY month;
```

### **Risk Manager Profile - High Priority KPIs**

#### **4. Currency Exposure Limits (utilizacao_limites_moeda)**
```sql
-- KPI: Currency Exposure and Limit Utilization
WITH currency_positions AS (
    SELECT
        bm.simbolo as currency,
        SUM(CASE WHEN b.tipo_operacao = 'COMPRA' THEN b.valor_me ELSE -b.valor_me END) as net_position,
        SUM(b.valor_me) as gross_exposure,
        COUNT(*) as operation_count,
        MAX(b.data_operacao) as last_operation_date
    FROM boleta b
    JOIN boleta_moeda bm ON b.id_moeda = bm.id
    WHERE b.data_operacao >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY bm.simbolo, bm.id
),
currency_limits AS (
    -- Predefined limits per currency (could be from a limits table)
    SELECT 'USD' as currency, 50000000 as exposure_limit, 10000000 as position_limit
    UNION ALL SELECT 'EUR', 20000000, 5000000
    UNION ALL SELECT 'GBP', 10000000, 2000000
    UNION ALL SELECT 'CHF', 5000000, 1000000
    UNION ALL SELECT 'CAD', 8000000, 1500000
    UNION ALL SELECT 'AUD', 8000000, 1500000
    UNION ALL SELECT 'SEK', 15000000, 3000000
    UNION ALL SELECT 'NOK', 10000000, 2000000
)
SELECT
    cp.currency,
    cp.net_position,
    cp.gross_exposure,
    cl.exposure_limit,
    cl.position_limit,
    ROUND((ABS(cp.net_position) / cl.position_limit) * 100, 2) as position_utilization_percent,
    ROUND((cp.gross_exposure / cl.exposure_limit) * 100, 2) as exposure_utilization_percent,
    cp.operation_count,
    cp.last_operation_date,
    CASE
        WHEN ABS(cp.net_position) / cl.position_limit > 0.9 THEN 'CRITICAL'
        WHEN ABS(cp.net_position) / cl.position_limit > 0.8 THEN 'HIGH'
        WHEN ABS(cp.net_position) / cl.position_limit > 0.6 THEN 'MEDIUM'
        ELSE 'LOW'
    END as position_risk_level,
    CASE
        WHEN cp.gross_exposure / cl.exposure_limit > 0.9 THEN 'CRITICAL'
        WHEN cp.gross_exposure / cl.exposure_limit > 0.8 THEN 'HIGH'
        WHEN cp.gross_exposure / cl.exposure_limit > 0.6 THEN 'MEDIUM'
        ELSE 'LOW'
    END as exposure_risk_level
FROM currency_positions cp
LEFT JOIN currency_limits cl ON cp.currency = cl.currency
ORDER BY position_utilization_percent DESC;
```

### **Operations Profile - High Priority KPIs**

#### **5. Hourly Transaction Throughput (throughput_transacoes_hora)**
```sql
-- KPI: Hourly Transaction Throughput Analysis
WITH hourly_operations AS (
    SELECT
        DATE_TRUNC('hour', data_operacao) as hour,
        COUNT(*) as transaction_count,
        SUM(valor_me) as total_volume,
        AVG(valor_me) as avg_transaction_size,
        COUNT(DISTINCT id_cliente) as unique_clients,
        COUNT(DISTINCT id_funcionario_criador) as active_operators
    FROM boleta
    WHERE data_operacao >= CURRENT_DATE - INTERVAL '7 days'
    GROUP BY DATE_TRUNC('hour', data_operacao)
),
throughput_stats AS (
    SELECT
        AVG(transaction_count) as avg_hourly_throughput,
        MAX(transaction_count) as peak_hourly_throughput,
        MIN(transaction_count) as min_hourly_throughput,
        STDDEV(transaction_count) as throughput_volatility
    FROM hourly_operations
)
SELECT
    ho.hour,
    ho.transaction_count,
    ho.total_volume,
    ho.avg_transaction_size,
    ho.unique_clients,
    ho.active_operators,
    ROUND(ho.transaction_count / NULLIF(ho.active_operators, 0), 2) as transactions_per_operator,
    ts.avg_hourly_throughput,
    ts.peak_hourly_throughput,
    ROUND((ho.transaction_count / ts.peak_hourly_throughput) * 100, 2) as capacity_utilization_percent,
    CASE
        WHEN ho.transaction_count >= ts.peak_hourly_throughput * 0.9 THEN 'HIGH_LOAD'
        WHEN ho.transaction_count >= ts.avg_hourly_throughput * 1.5 THEN 'ABOVE_AVERAGE'
        WHEN ho.transaction_count <= ts.avg_hourly_throughput * 0.5 THEN 'LOW_ACTIVITY'
        ELSE 'NORMAL'
    END as load_category
FROM hourly_operations ho
CROSS JOIN throughput_stats ts
ORDER BY ho.hour DESC;
```

---

## 🧪 **Testing and Validation Strategy**

### **Automated Query Testing**
```python
# apps/backend/tests/test_kpi_queries.py
import pytest
from src.services.kpi_calculator import KPICalculator

class TestKPIQueries:

    @pytest.fixture
    def kpi_calculator(self):
        return KPICalculator()

    def test_client_concentration_calculation(self, kpi_calculator):
        """Test client concentration KPI calculation accuracy"""
        result = kpi_calculator.calculate_kpi(
            kpi_id="concentracao_top10_clientes",
            start_date="2025-01-01",
            end_date="2025-01-31"
        )

        assert result["status"] == "success"
        assert "top10_concentration" in result["data"]
        assert result["data"]["top10_concentration"] <= 100
        assert len(result["data"]["clients"]) <= 10

    def test_revenue_growth_yoy(self, kpi_calculator):
        """Test year-over-year revenue growth calculation"""
        result = kpi_calculator.calculate_kpi(
            kpi_id="crescimento_receita_yoy",
            start_date="2025-01-01",
            end_date="2025-01-31"
        )

        assert result["status"] == "success"
        assert "yoy_growth_percent" in result["data"]
        # Growth can be negative, so just check it's a number
        assert isinstance(result["data"]["yoy_growth_percent"], (int, float))
```

### **Data Quality Validation**
```sql
-- Data Quality Checks for KPI Implementation
-- Check 1: Data Completeness
SELECT
    'boleta' as table_name,
    COUNT(*) as total_records,
    COUNT(valor_me) as has_valor_me,
    COUNT(data_operacao) as has_data_operacao,
    COUNT(id_cliente) as has_cliente,
    ROUND((COUNT(valor_me)::float / COUNT(*)) * 100, 2) as completeness_percent
FROM boleta
WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'

UNION ALL

SELECT
    'conta_receber' as table_name,
    COUNT(*) as total_records,
    COUNT(valor_total) as has_valor_total,
    COUNT(data_emissao) as has_data_emissao,
    COUNT(id_cliente) as has_cliente,
    ROUND((COUNT(valor_total)::float / COUNT(*)) * 100, 2) as completeness_percent
FROM conta_receber
WHERE data_emissao >= CURRENT_DATE - INTERVAL '30 days';

-- Check 2: Data Consistency
SELECT
    'Currency Consistency' as check_name,
    COUNT(*) as total_operations,
    COUNT(DISTINCT bm.simbolo) as unique_currencies,
    COUNT(CASE WHEN bm.simbolo IS NULL THEN 1 END) as missing_currency_mappings
FROM boleta b
LEFT JOIN boleta_moeda bm ON b.id_moeda = bm.id
WHERE b.data_operacao >= CURRENT_DATE - INTERVAL '30 days';
```

---

## 📸 **Integration with Existing Snapshot System**

### **Current Snapshot Infrastructure (DO NOT CHANGE)**
```python
# EXISTING: ProfileAwareSnapshotService
# Location: apps/backend/src/services/profile_aware_snapshot_service.py
# Features: Daily snapshots at 3AM, PostgreSQL storage, profile optimization

class ProfileAwareSnapshotService:
    def generate_daily_snapshot(self, client_id: str) -> Dict[str, Any]:
        # EXISTING: Generates 6 critical KPIs daily
        critical_kpis = self._get_critical_kpi_definitions()

    def get_latest_snapshot(self, client_id: str) -> Optional[Dict[str, Any]]:
        # EXISTING: Retrieves from PostgreSQL kpi_snapshots table
```

### **Extending Snapshot Generation**
```python
# EXTEND: Add profile-specific KPI generation to existing service
# NO REFACTORING - just add new KPI calculations

def _get_critical_kpi_definitions_by_profile(self, profile_type: str) -> List[str]:
    """Extend existing method to support profile-specific KPIs."""
    profile_kpis = {
        'CEO': [
            'spread_income_detailed', 'margem_liquida_operacional',  # EXISTING
            'receita_total_mensal', 'concentracao_top10_clientes',   # NEW Phase 1
            'crescimento_receita_yoy', 'ltv_lifetime_value'          # NEW Phase 2
        ],
        'CFO': [
            'margem_liquida_operacional',                             # EXISTING
            'margem_bruta_por_produto', 'ebitda_mensal',             # NEW Phase 1
            'custo_operacional_por_transacao', 'cash_flow_operacional' # NEW Phase 2
        ],
        # ... other profiles
    }
    return profile_kpis.get(profile_type, self.CRITICAL_KPIS)  # Fallback to existing
```

### **Snapshot Storage Strategy (Use Existing)**
```sql
-- EXISTING TABLE: kpi_snapshots (DO NOT MODIFY)
-- Location: PostgreSQL database
-- Usage: ProfileAwareSnapshotService already stores here

-- EXTEND: Add profile_type column for better filtering
ALTER TABLE kpi_snapshots ADD COLUMN profile_type VARCHAR(50);
CREATE INDEX idx_kpi_snapshots_profile ON kpi_snapshots(client_id, profile_type, created_at);
```

---

## 🔄 **Implementation Strategy (Incremental)**

### **Phase 1: Extend Existing Services (4-6 weeks)**
1. **Add KPI calculators to HybridKpiService** (18 new methods)
2. **Extend profile mapping** in existing PersonalizedCacheSystem
3. **Add profile-specific KPIs** to ProfileAwareSnapshotService
4. **Update SmartQueryRouter** routing for new KPIs

### **Phase 2: Database Schema Extensions (2-3 weeks)**
1. **Add profile_type column** to kpi_snapshots table
2. **Create indexes** for performance optimization
3. **Update existing queries** to support profile filtering

### **Phase 3: Testing Integration (2-3 weeks)**
1. **Extend existing test suite** (test_profile_personalization_api.py)
2. **Add KPI validation tests** using MCP Postgres
3. **Performance testing** with existing monitoring system

### **Phase 4: Gradual Rollout (2-4 weeks)**
1. **Deploy Phase 1 KPIs** using existing CI/CD
2. **Monitor performance** with existing observability system
3. **Collect user feedback** and iterate

---

## 🎯 **Success Metrics (Integrate with Existing Monitoring)**

### **Use Existing Monitoring Infrastructure**
```python
# EXISTING: ProductionMonitoringMiddleware
# Location: apps/backend/src/monitoring/production_monitoring.py
# Features: Prometheus metrics, OpenTelemetry tracing

# EXTEND: Add KPI-specific metrics
kpi_calculation_duration = Histogram(
    'datahero_kpi_calculation_duration_seconds',
    'Time spent calculating KPIs',
    ['kpi_id', 'profile_type', 'layer']
)

kpi_cache_hit_rate = Counter(
    'datahero_kpi_cache_hits_total',
    'KPI cache hits by profile',
    ['profile_type', 'kpi_id']
)
```

### **Performance Targets (Existing Infrastructure)**
- **Response Times**: Monitored by existing Prometheus metrics
- **Cache Performance**: Tracked by UnifiedCacheSystem statistics
- **Database Performance**: Monitored by SQLAlchemy instrumentation
- **Error Rates**: Captured by existing error tracking

---

## 📋 **Implementation Checklist**

### **Pre-Implementation (Week 1)**
- [ ] Validate all 72 KPI queries using MCP Postgres
- [ ] Map KPIs to existing profile routing strategies
- [ ] Identify database schema extensions needed
- [ ] Plan integration points with existing services

### **Phase 1 Implementation (Weeks 2-7)**
- [ ] Add 18 KPI calculators to HybridKpiService
- [ ] Extend PersonalizedCacheSystem profile mappings
- [ ] Update ProfileAwareSnapshotService for new KPIs
- [ ] Add profile-specific routing to SmartQueryRouter
- [ ] Extend existing test suite with new KPIs

### **Phase 2 Implementation (Weeks 8-15)**
- [ ] Implement 28 medium-complexity KPIs
- [ ] Add database schema extensions
- [ ] Optimize query performance using existing patterns
- [ ] Integrate with existing monitoring and alerting

### **Phase 3 Implementation (Weeks 16-25)**
- [ ] Implement 12 advanced analytics KPIs
- [ ] Add external data integration points
- [ ] Performance optimization and scaling
- [ ] Full integration testing and validation

---

## 🎯 **Executive Summary & Next Steps**

### **Key Findings from Database Analysis**
- ✅ **89 database tables available** with rich business data beyond just `boleta`
- ✅ **58 of 72 remaining KPIs (81%) can be implemented** with current schema
- ✅ **Existing architecture is robust** and ready for extension without refactoring
- ✅ **All integration points identified** in current services and caching systems

### **Implementation Approach: Extend, Don't Refactor**
1. **Leverage existing HybridKpiService** - add new KPI calculators to existing mapping
2. **Use existing PersonalizedCacheSystem** - profile-aware TTL already implemented
3. **Integrate with SmartQueryRouter** - 3-layer routing already optimized
4. **Extend ProfileAwareSnapshotService** - daily snapshots already working
5. **Use existing monitoring** - Prometheus metrics and observability in place

### **Immediate Next Steps (Week 1)**
1. **Validate remaining queries** using MCP Postgres for all 72 KPIs
2. **Prioritize Phase 1 KPIs** based on business impact and data availability
3. **Create implementation plan** for extending HybridKpiService
4. **Set up development environment** for incremental KPI additions

### **Success Metrics**
- **Coverage Target**: 95% of planned KPIs (68/72) implemented within 6 months
- **Performance Target**: All KPIs within existing response time limits
- **Integration Target**: Zero refactoring of existing services required
- **Quality Target**: >95% test coverage for all new KPI calculations

### **Risk Mitigation**
- **Data Quality**: MCP Postgres validation before each KPI implementation
- **Performance**: Leverage existing caching and routing strategies
- **Integration**: Incremental rollout with existing monitoring
- **Maintenance**: Extend existing test suite and documentation

**Recommendation**: Proceed with Phase 1 implementation immediately. The existing DataHero4 architecture is well-designed and ready for KPI expansion without any refactoring required.

---

**📋 Detailed Implementation Example**: See `docs/KPI_IMPLEMENTATION_EXAMPLE.md` for step-by-step integration guide using existing architecture.
