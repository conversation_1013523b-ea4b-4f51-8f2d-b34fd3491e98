# Task List: Fase 1 - Arquitetura Híbrida DataHero4

## 🎯 Objetivo Principal
Implementar Arquitetura Híbrida 3 camadas aproveitando 70% da infraestrutura existente com roteamento inteligente fail-fast e personalização por perfil.

## 📋 Lista de Tarefas Atual

### ✅ Week 1 - Database Extensions (COMPLETED)
- [x] **Week 1 - Database Extensions**
  - **Status**: ✅ COMPLETED
  - **Descrição**: Created kpi_snapshots_v2 table with partitioning, user_profiles table with 5 default profiles, operational_costs table with sample data, and extended transactions table with processed_at field. All migrations validated successfully.
  - **UUID**: vpmiFFbaguKVR3WvqwUoTu

### 🔧 Week 2 - Smart Query Router (IN PROGRESS)
- [/] **Week 2 - Smart Query Router**
  - **Status**: 🔄 IN PROGRESS
  - **Descrição**: Criar SmartQueryRouter integrando SnapshotService, UnifiedCacheSystem e KpiRepository existentes. Roteamento inteligente entre 3 camadas com fail fast. Sem fallbacks entre camadas.
  - **UUID**: 7ssXwPh3SBKdz5bWKx2bap

#### Subtarefas Week 2:
- [ ] **Estender SnapshotService para Perfis**
  - **Descrição**: Modificar generate_snapshot_cron.py existente para gerar snapshots personalizados por perfil usando kpi_snapshots_v2. Manter sistema 3AM existente funcionando.
  - **UUID**: nfUVBCZ3tFnLhNpZ1r1BnD

- [ ] **Criar PersonalizedCacheSystem**
  - **Descrição**: Estender UnifiedCacheSystem existente com TTL diferenciado por perfil (CEO=1h, Risk=5min, Trader=1min). Cache sempre personalizado por user_id + profile.
  - **UUID**: mHK9AUaabvHeHTJQjPmtnK

- [ ] **Implementar ProfileDetector**
  - **Descrição**: Criar ProfileDetector usando QueryCache existente com embeddings. Análise de padrões de uso para detecção automática. Fail fast se não conseguir detectar com confiança > 30%.
  - **UUID**: 4mGkAnw2jw2vCgkwrcnrkf

### 📊 Week 3 - KPIs via Arquitetura Híbrida
- [ ] **Week 3 - KPIs via Arquitetura Híbrida**
  - **Descrição**: Implementar 4 KPIs fundamentais via SmartQueryRouter: spread_income_detalhado, margem_liquida_operacional, custo_por_transacao, tempo_processamento_medio. Cada KPI usa camada otimizada por perfil.
  - **UUID**: dq9XjozWoESmXoAkEPEo16

### 🔌 Week 4 - LangGraph Integration
- [ ] **Week 4 - LangGraph Integration**
  - **Descrição**: Modificar enhanced_coordinator_agent existente para usar SmartQueryRouter. DataHeroState sempre com user_profile obrigatório. Integração direta com workflow LangGraph existente.
  - **UUID**: c5BMx34zBuJ7Q3Qnue5wdF

#### Subtarefas Week 4:
- [ ] **BCB Integration Fail Fast**
  - **Descrição**: Implementar BancoCentralAPI integrada ao SmartQueryRouter. Cotações oficiais USD/BRL e SELIC. Fail fast se BCB indisponível, sem fallbacks para dados mock.
  - **UUID**: 8gxRJyT4dq1Shad9wX9a6P

- [ ] **Profile API Endpoints**
  - **Descrição**: Criar apps/backend/src/interfaces/profile_api.py com endpoints /detect, /configure, /kpis para gerenciamento de perfis, seguindo padrão existente.
  - **UUID**: 59Sh7afQssSMp8qECcLJUj

- [ ] **Estender Dashboard API**
  - **Descrição**: Adicionar endpoints /personalized-kpis ao apps/backend/src/interfaces/dashboard_api.py existente sem quebrar funcionalidades atuais.
  - **UUID**: cUad6w7UPGPs9qey6cSkQz

### 🎨 Week 5 - Frontend Components
- [ ] **Week 5 - Frontend Components**
  - **Descrição**: Criar ProfileSetup.tsx, estender KpiBentoGrid.tsx para filtro por perfil, implementar usePersonalization.ts hook, e modificar useKpis.tsx para dados personalizados.
  - **UUID**: 7rBaotxPGrsu2CkravQtTY

#### Subtarefas Week 5:
- [ ] **ProfileSetup Component**
  - **Descrição**: Criar apps/frontend/src/components/profile/ProfileSetup.tsx para configuração inicial de perfil do usuário com UX intuitiva.
  - **UUID**: jepAsMCH3SZq8SjRAcE6i5

- [ ] **Estender KpiBentoGrid**
  - **Descrição**: Modificar apps/frontend/src/components/dashboard/KpiBentoGrid.tsx para incluir filtros por perfil sem alterar estrutura existente.
  - **UUID**: xfB8HU5NzAuPqxWXk1YS8G

- [ ] **usePersonalization Hook**
  - **Descrição**: Criar apps/frontend/src/hooks/usePersonalization.ts para gerenciamento de estado de personalização no frontend.
  - **UUID**: 53vNsssWWNcdNgmJsay2SJ

- [ ] **Estender useKpis Hook**
  - **Descrição**: Modificar apps/frontend/src/hooks/useKpis.tsx para incluir lógica de personalização sem quebrar funcionalidade existente.
  - **UUID**: rrkgtG75zPbHeiEXS6Yhpy

### 🧪 Week 6 - Testing & Validation
- [ ] **Week 6 - Testing & Validation**
  - **Descrição**: Testes integração das 3 camadas. Validar roteamento inteligente por perfil. Performance tests com múltiplos usuários simultâneos. Métricas cache hit rate > 80% por perfil.
  - **UUID**: oGWgsGPKAzUrLFt7nQsLZs

#### Subtarefas Week 6:
- [ ] **Testes Unitários Backend**
  - **Descrição**: Implementar testes unitários para SmartQueryRouter, ProfileDetector, PersonalizedCacheSystem com cobertura > 90%.
  - **UUID**: qBu3oMZSaBjAEGWFLw8BYJ

- [ ] **Testes Integração**
  - **Descrição**: Implementar tests/integration/test_hybrid_architecture.py para testar fluxo completo da arquitetura híbrida end-to-end.
  - **UUID**: 37jhdXrGXkHgHaafXM97MD

- [ ] **Testes Performance**
  - **Descrição**: Implementar tests/performance/test_hybrid_performance.py para validar performance com múltiplos usuários e perfis simultâneos.
  - **UUID**: 9y75i3duNqta7EymtYjEqB

### 🚀 Week 7 - Deploy & Monitoring
- [ ] **Week 7 - Deploy & Monitoring**
  - **Descrição**: Deploy em ambiente de staging, execução de testes end-to-end e validação com usuários beta antes do deploy em produção.
  - **UUID**: kSmfQn7BYaEqWk9M3xGpKy

#### Subtarefas Week 7:
- [ ] **Testes Regressão**
  - **Descrição**: Executar suite completa de testes de regressão para garantir que funcionalidades existentes não foram quebradas pela arquitetura híbrida.
  - **UUID**: swAkqAfvHBSzFNw17jiGh6

- [ ] **Logging Estruturado**
  - **Descrição**: Adicionar logs estruturados para todas as operações da arquitetura híbrida (roteamento, cache, perfil) para observabilidade.
  - **UUID**: 8NNckr35RbAGo2wriDsA3b

- [ ] **Monitoramento Híbrido**
  - **Descrição**: Adicionar métricas Prometheus para arquitetura híbrida (cache hit rate por perfil, tempo roteamento, performance por camada).
  - **UUID**: mAynTDaTbLYWTjbJDgFCk2

## 📊 Progresso Atual

### Status por Week:
- **Week 1**: ✅ **100% COMPLETED** (Database Extensions)
- **Week 2**: 🔄 **IN PROGRESS** (Smart Query Router)
- **Week 3**: 📋 **PLANNED** (KPIs via Arquitetura Híbrida)
- **Week 4**: 📋 **PLANNED** (LangGraph Integration)
- **Week 5**: 📋 **PLANNED** (Frontend Components)
- **Week 6**: 📋 **PLANNED** (Testing & Validation)
- **Week 7**: 📋 **PLANNED** (Deploy & Monitoring)

### Métricas de Progresso:
- **Total de Tarefas**: 22 tarefas
- **Tarefas Completadas**: 1 (4.5%)
- **Tarefas em Progresso**: 1 (4.5%)
- **Tarefas Pendentes**: 20 (91%)

## 🏗️ Arquitetura Híbrida - Visão Geral

### Camadas da Arquitetura:
1. **Camada 1 - Snapshots Críticos**: Dados estratégicos pré-calculados
2. **Camada 2 - Cache Quente Personalizado**: TTL diferenciado por perfil
3. **Camada 3 - Query Direta Otimizada**: Consultas em tempo real

### Perfis Suportados:
- **CEO**: Cache 1h, dados estratégicos via snapshots
- **CFO**: Cache 30min, dados financeiros via snapshots + cache
- **Risk Manager**: Cache 5min, dados tempo real via query direta
- **Trader**: Cache 1min, dados operacionais via cache quente
- **Operations**: Cache 15min, dados de eficiência via cache

## 🎯 Próximos Passos Imediatos

1. **Implementar SmartQueryRouter** (Week 2 - IN PROGRESS)
2. **Estender SnapshotService** para perfis
3. **Criar PersonalizedCacheSystem**
4. **Implementar ProfileDetector** com embeddings

---

**Última Atualização**: 2025-01-21  
**Arquivo**: `/Users/<USER>/Coding/datahero4/docs/plan/todo/fase1-newKPI/TASK_LIST_ARQUITETURA_HIBRIDA.md`

*Esta task list é sincronizada com o sistema de gerenciamento de tarefas interno da conversa.*
