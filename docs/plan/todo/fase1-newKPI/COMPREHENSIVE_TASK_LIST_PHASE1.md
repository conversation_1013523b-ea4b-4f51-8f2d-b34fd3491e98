# Comprehensive Implementation Task List - Phase 1: Hybrid Architecture

## 📋 Implementation Overview

**Branch**: `feature/fase1-arquitetura-hibrida`  
**Duration**: 8 weeks  
**Team**: 2 developers + 1 analyst  
**Architecture**: 70% existing infrastructure reuse + 30% hybrid extensions  

## 🗄️ Database Extensions (Week 1)

### Task 1.1: Extend Existing Snapshot System
- [ ] **Create kpi_snapshots_v2 table** extending existing kpi_snapshots
  - **Files**: `apps/backend/alembic/versions/xxx_add_kpi_snapshots_v2.py`
  - **Dependencies**: None
  - **Details**: Add user_profile, period_type, dimensions JSONB fields
  - **Acceptance**: Table created with proper indexes and partitioning
  - **Testing**: Migration runs without breaking existing snapshots

```sql
-- Migration script structure
CREATE TABLE kpi_snapshots_v2 (
    client_id VARCHAR(50) NOT NULL,
    kpi_id VARCHAR(50) NOT NULL,
    user_profile VARCHAR(50) NOT NULL,
    period_type VARCHAR(20) NOT NULL,
    period_date DATE NOT NULL,
    dimensions JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (client_id, kpi_id, user_profile, period_type, period_date)
) PARTITION BY RANGE (period_date);
```

### Task 1.2: Create User Profiles Table
- [ ] **Add user_profiles table** for profile management
  - **Files**: `apps/backend/alembic/versions/xxx_add_user_profiles.py`
  - **Dependencies**: Task 1.1
  - **Details**: Store user profile types, preferences, selected KPIs
  - **Acceptance**: Table supports profile detection and customization
  - **Testing**: CRUD operations work correctly

### Task 1.3: Add Operational Costs Table
- [ ] **Create operational_costs table** for new KPI calculations
  - **Files**: `apps/backend/alembic/versions/xxx_add_operational_costs.py`
  - **Dependencies**: None
  - **Details**: Store daily operational costs by category
  - **Acceptance**: Supports cost per transaction KPI calculation
  - **Testing**: Data insertion and aggregation queries perform well

### Task 1.4: Extend Transactions Table
- [ ] **Add processed_at field** to existing transactions table
  - **Files**: `apps/backend/alembic/versions/xxx_add_processed_at_transactions.py`
  - **Dependencies**: None
  - **Details**: ALTER TABLE ADD COLUMN processed_at TIMESTAMP
  - **Acceptance**: Field added without data loss, indexed properly
  - **Testing**: Processing time KPI calculations work correctly

## 🔧 Backend Services Extensions (Week 2-3)

### Task 2.1: Extend SnapshotService for Profiles
- [ ] **Modify generate_snapshot_cron.py** to support profile-aware snapshots
  - **Files**: `apps/backend/src/services/snapshot_service.py`
  - **Dependencies**: Task 1.1
  - **Details**: Generate snapshots with user_profile dimensions
  - **Acceptance**: Daily snapshots include profile-specific data
  - **Testing**: Snapshots generated for all profile types correctly

### Task 2.2: Create Smart Query Router
- [ ] **Implement SmartQueryRouter** integrating existing services
  - **Files**: `apps/backend/src/services/smart_query_router.py`
  - **Dependencies**: Tasks 1.1-1.4, existing UnifiedCacheSystem, KpiRepository
  - **Details**: Route queries through 3-layer architecture based on profile
  - **Acceptance**: Intelligent routing between snapshots, cache, and direct queries
  - **Testing**: Performance tests show optimal layer selection per profile

```python
# Core router structure
class SmartQueryRouter:
    def __init__(self):
        self.snapshot_service = SnapshotService()  # EXISTING
        self.cache_system = UnifiedCacheSystem()   # EXISTING  
        self.kpi_repository = KpiRepository()      # EXISTING
    
    def get_kpi_data(self, kpi_id: str, timeframe: str, user_profile: str):
        # Layer 1: Snapshots for strategic data
        # Layer 2: Cache for operational data  
        # Layer 3: Direct queries for real-time data
```

### Task 2.3: Extend UnifiedCacheSystem for Personalization
- [ ] **Create PersonalizedCacheSystem** extending existing cache
  - **Files**: `apps/backend/src/caching/personalized_cache_system.py`
  - **Dependencies**: Existing UnifiedCacheSystem
  - **Details**: TTL strategies by profile, user-specific cache keys
  - **Acceptance**: Cache hit rates >80% per profile type
  - **Testing**: Cache performance under concurrent user load

### Task 2.4: Implement Profile Detection Service
- [ ] **Create ProfileDetector** using existing QueryCache embeddings
  - **Files**: `apps/backend/src/services/profile_detector.py`
  - **Dependencies**: Existing learning_models.py QueryCache
  - **Details**: Analyze usage patterns from QueryCache to detect profiles
  - **Acceptance**: >85% accuracy in profile detection
  - **Testing**: Detection works with real usage data from existing system

### Task 2.5: Create Optimized Query Service
- [ ] **Implement OptimizedQueryService** using existing KpiRepository
  - **Files**: `apps/backend/src/services/optimized_query_service.py`
  - **Dependencies**: Existing KpiRepository, Task 2.2
  - **Details**: Profile-specific query strategies and timeouts
  - **Acceptance**: Query performance optimized per profile usage patterns
  - **Testing**: Timeout enforcement and query optimization validation

## 📊 New KPI Implementations (Week 3-4)

### Task 3.1: Implement Spread Income Detailed KPI
- [ ] **Create spread income calculation** via hybrid architecture
  - **Files**: `apps/backend/src/models/kpi_models.py` (extend existing)
  - **Dependencies**: Task 2.2, existing Transaction model
  - **Details**: Calculate spread income by currency using SmartQueryRouter
  - **Acceptance**: Accurate calculations for all currency pairs
  - **Testing**: Validate against existing transaction data

### Task 3.2: Implement Operational Net Margin KPI
- [ ] **Create operational margin calculation** using operational costs
  - **Files**: `apps/backend/src/models/kpi_models.py` (extend existing)
  - **Dependencies**: Task 1.3, Task 3.1
  - **Details**: (Revenue - Operational Costs) / Revenue * 100
  - **Acceptance**: Accurate margin calculations with cost integration
  - **Testing**: Verify calculations with real operational cost data

### Task 3.3: Implement Cost Per Transaction KPI
- [ ] **Create cost per transaction calculation**
  - **Files**: `apps/backend/src/models/kpi_models.py` (extend existing)
  - **Dependencies**: Task 1.3, existing transaction counts
  - **Details**: Total Operational Costs / Number of Transactions
  - **Acceptance**: Real-time cost efficiency tracking
  - **Testing**: Validate cost allocation accuracy

### Task 3.4: Implement Average Processing Time KPI
- [ ] **Create processing time calculation**
  - **Files**: `apps/backend/src/models/kpi_models.py` (extend existing)
  - **Dependencies**: Task 1.4
  - **Details**: AVG(processed_at - created_at) in seconds
  - **Acceptance**: Real-time processing performance tracking
  - **Testing**: Validate timing accuracy with transaction logs

## 🔌 API Endpoints Integration (Week 4-5)

### Task 4.1: Extend Dashboard API with Hybrid Router
- [ ] **Add personalized KPI endpoints** to existing dashboard_api.py
  - **Files**: `apps/backend/src/interfaces/dashboard_api.py` (extend existing)
  - **Dependencies**: Task 2.2, all KPI implementations
  - **Details**: GET /personalized-kpis/{user_id} using SmartQueryRouter
  - **Acceptance**: API returns profile-optimized KPI data
  - **Testing**: API performance tests with multiple concurrent users

### Task 4.2: Create Profile Management API
- [ ] **Implement profile detection and configuration endpoints**
  - **Files**: `apps/backend/src/interfaces/profile_api.py` (new)
  - **Dependencies**: Task 2.4, Task 1.2
  - **Details**: Profile detection, configuration, and KPI recommendations
  - **Acceptance**: Complete profile management via API
  - **Testing**: Profile CRUD operations and detection accuracy

### Task 4.3: Integrate BCB API for External Data
- [ ] **Create BCB integration service** with fail-fast approach
  - **Files**: `apps/backend/src/services/bcb_integration.py` (new)
  - **Dependencies**: Task 2.2
  - **Details**: Official USD/BRL rates and SELIC data, no fallbacks
  - **Acceptance**: Real-time BCB data integration with 30min cache
  - **Testing**: API failure scenarios handled with proper error propagation

### Task 4.4: Extend Enhanced Coordinator for LangGraph Integration
- [ ] **Modify enhanced_coordinator_agent** to use SmartQueryRouter
  - **Files**: `apps/backend/src/graphs/enhanced_coordinator_agent.py` (extend existing)
  - **Dependencies**: Task 2.2, existing LangGraph workflow
  - **Details**: Integrate hybrid architecture into existing agent workflow
  - **Acceptance**: LangGraph workflow uses personalized KPI data
  - **Testing**: Agent workflow tests with profile-specific data

## 🎨 Frontend Components Extensions (Week 5-6)

### Task 5.1: Create Profile Setup Component
- [ ] **Implement ProfileSetup.tsx** for initial profile configuration
  - **Files**: `apps/frontend/src/components/profile/ProfileSetup.tsx` (new)
  - **Dependencies**: Task 4.2
  - **Details**: User-friendly profile detection and manual configuration
  - **Acceptance**: Intuitive profile setup flow
  - **Testing**: Component renders correctly for all profile types

### Task 5.2: Extend KpiBentoGrid for Profile Filtering
- [ ] **Modify existing KpiBentoGrid.tsx** to support profile-based filtering
  - **Files**: `apps/frontend/src/components/dashboard/KpiBentoGrid.tsx` (extend existing)
  - **Dependencies**: Task 4.1, existing KpiBentoGrid
  - **Details**: Filter and prioritize KPIs based on user profile
  - **Acceptance**: Dashboard shows profile-relevant KPIs
  - **Testing**: Grid updates correctly when profile changes

### Task 5.3: Create Personalization Hook
- [ ] **Implement usePersonalization.ts** hook
  - **Files**: `apps/frontend/src/hooks/usePersonalization.ts` (new)
  - **Dependencies**: Task 4.2
  - **Details**: Manage profile state and KPI preferences
  - **Acceptance**: Centralized profile state management
  - **Testing**: Hook state updates propagate correctly

### Task 5.4: Extend Existing KPI Hooks
- [ ] **Modify useKpis.tsx** to support personalized data fetching
  - **Files**: `apps/frontend/src/hooks/useKpis.tsx` (extend existing)
  - **Dependencies**: Task 4.1, existing useKpis hook
  - **Details**: Fetch personalized KPI data via new API endpoints
  - **Acceptance**: Seamless integration with existing KPI display logic
  - **Testing**: KPI data loads correctly for different profiles

## 🧪 Integration & Testing (Week 6-7)

### Task 6.1: Hybrid Architecture Integration Tests
- [ ] **Create comprehensive integration tests** for 3-layer architecture
  - **Files**: `apps/backend/tests/integration/test_hybrid_architecture.py` (new)
  - **Dependencies**: All backend tasks completed
  - **Details**: Test routing logic, cache behavior, and data consistency
  - **Acceptance**: All integration scenarios pass with real data
  - **Testing**: Performance benchmarks meet profile-specific requirements

### Task 6.2: Profile Detection Accuracy Tests
- [ ] **Implement profile detection validation** with real usage data
  - **Files**: `apps/backend/tests/test_profile_detection.py` (new)
  - **Dependencies**: Task 2.4
  - **Details**: Test detection accuracy with historical QueryCache data
  - **Acceptance**: >85% detection accuracy across all profile types
  - **Testing**: Edge cases and ambiguous usage patterns handled correctly

### Task 6.3: KPI Calculation Validation Tests
- [ ] **Create KPI accuracy tests** with real transaction data
  - **Files**: `apps/backend/tests/test_kpi_calculations.py` (new)
  - **Dependencies**: All KPI implementation tasks
  - **Details**: Validate new KPI calculations against known results
  - **Acceptance**: All KPI calculations match expected values
  - **Testing**: Performance tests ensure calculations complete within SLA

### Task 6.4: Frontend-Backend Integration Tests
- [ ] **Implement end-to-end tests** for personalized dashboard flow
  - **Files**: `apps/frontend/tests/e2e/test_personalized_dashboard.py` (new)
  - **Dependencies**: All frontend and backend tasks
  - **Details**: Complete user journey from profile detection to KPI display
  - **Acceptance**: Full personalization flow works seamlessly
  - **Testing**: Multiple user profiles tested simultaneously

## 🚀 Deployment & Validation (Week 7-8)

### Task 7.1: Database Migration Validation
- [ ] **Execute and validate all database migrations** in staging
  - **Files**: All migration files from Week 1
  - **Dependencies**: All database extension tasks
  - **Details**: Run migrations on staging with production data copy
  - **Acceptance**: Zero data loss, all indexes created, performance maintained
  - **Testing**: Migration rollback procedures tested and documented

### Task 7.2: Performance Optimization and Monitoring
- [ ] **Implement monitoring and optimize performance** bottlenecks
  - **Files**: `apps/backend/src/monitoring/hybrid_metrics.py` (new)
  - **Dependencies**: All implementation tasks
  - **Details**: Monitor cache hit rates, query performance, API response times
  - **Acceptance**: All performance targets met per profile type
  - **Testing**: Load testing with realistic user distribution

### Task 7.3: Documentation and Training Materials
- [ ] **Create comprehensive documentation** for hybrid architecture
  - **Files**: `docs/architecture/hybrid-system.md`, `docs/api/personalized-endpoints.md`
  - **Dependencies**: All implementation tasks
  - **Details**: Architecture diagrams, API documentation, troubleshooting guides
  - **Acceptance**: Complete documentation for development and operations teams
  - **Testing**: Documentation accuracy verified through team review

### Task 7.4: Production Deployment Preparation
- [ ] **Prepare production deployment** with rollback procedures
  - **Files**: Deployment scripts and configuration
  - **Dependencies**: All tasks completed and tested
  - **Details**: Blue-green deployment strategy, monitoring alerts, rollback plans
  - **Acceptance**: Production-ready deployment package
  - **Testing**: Deployment procedures tested in staging environment

## ✅ Success Criteria

### Technical Metrics
- [ ] Cache hit rate >80% per profile type
- [ ] API response time <2s for all profile-specific endpoints
- [ ] Profile detection accuracy >85%
- [ ] Zero breaking changes to existing functionality
- [ ] Database query performance maintained or improved

### Business Metrics
- [ ] All 4 new KPIs calculating correctly with real data
- [ ] Profile-based dashboard personalization functional
- [ ] BCB integration providing real-time exchange rates
- [ ] User profile setup completed in <3 minutes
- [ ] System handles concurrent users across all profile types

### Quality Metrics
- [ ] Test coverage >90% for all new components
- [ ] Zero critical bugs in production deployment
- [ ] All fail-fast scenarios properly implemented
- [ ] Documentation complete and accurate
- [ ] Team training completed successfully

## 🔧 Technical Implementation Details

### Dependency Injection Patterns (FastAPI Integration)
```python
# apps/backend/src/dependencies/hybrid_dependencies.py (new)
from fastapi import Depends
from typing import Annotated

# Extend existing dependency patterns
def get_smart_router() -> SmartQueryRouter:
    return SmartQueryRouter()

def get_profile_detector() -> ProfileDetector:
    return ProfileDetector()

# Type aliases for clean injection
SmartRouterDep = Annotated[SmartQueryRouter, Depends(get_smart_router)]
ProfileDetectorDep = Annotated[ProfileDetector, Depends(get_profile_detector)]

# Usage in endpoints
@app.get("/personalized-kpis/{user_id}")
async def get_personalized_kpis(
    user_id: str,
    router: SmartRouterDep,
    profile_detector: ProfileDetectorDep
):
    profile = await profile_detector.detect_profile(user_id)
    return await router.get_kpis_for_profile(user_id, profile)
```

### SQLModel Extension Patterns
```python
# Extend existing models without breaking changes
class KpiSnapshotV2(SQLModel, table=True):
    """Extends existing kpi_snapshots with profile dimensions"""
    __tablename__ = "kpi_snapshots_v2"

    # Inherit base structure, add profile-specific fields
    client_id: str = Field(primary_key=True)
    kpi_id: str = Field(primary_key=True)
    user_profile: str = Field(primary_key=True)  # NEW

    # Use JSONB for flexible dimensions (PostgreSQL native)
    dimensions: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON)  # PostgreSQL JSONB
    )
```

### LangGraph State Extension
```python
# apps/backend/src/graphs/enhanced_coordinator_agent.py (extend existing)
from typing import TypedDict, Optional

class DataHeroState(TypedDict):
    # Existing fields preserved
    messages: List[BaseMessage]
    client_id: str

    # NEW: Profile-aware fields (optional for backward compatibility)
    user_profile: Optional[str]  # NEW - fail fast if None
    personalized_kpis: Optional[List[Dict[str, Any]]]  # NEW
    cache_strategy: Optional[str]  # NEW - per profile
```

### Cache Strategy Implementation
```python
# Profile-specific TTL strategies
PROFILE_CACHE_STRATEGIES = {
    "CEO": {
        "kpi_ttl": 3600,  # 1 hour - strategic data
        "prefetch_critical": True,
        "cache_layer": "snapshots_preferred"
    },
    "Risk_Manager": {
        "kpi_ttl": 300,   # 5 minutes - risk data
        "prefetch_critical": False,
        "cache_layer": "direct_query_preferred"
    },
    "Trader": {
        "kpi_ttl": 60,    # 1 minute - operational data
        "prefetch_critical": False,
        "cache_layer": "hot_cache_preferred"
    }
}
```

### Fail-Fast Validation Patterns
```python
# Strict validation without fallbacks
class HybridArchitectureValidator:
    @staticmethod
    def validate_profile_required(user_profile: Optional[str]) -> str:
        if not user_profile:
            raise HTTPException(
                status_code=400,
                detail="user_profile required for personalized KPIs"
            )
        return user_profile

    @staticmethod
    def validate_snapshot_integrity(snapshot_data: Dict) -> Dict:
        required_fields = ["kpi_id", "value", "created_at", "user_profile"]
        missing = [f for f in required_fields if f not in snapshot_data]
        if missing:
            raise ValueError(f"Snapshot missing required fields: {missing}")
        return snapshot_data
```

## 📊 Performance Benchmarks

### Target Metrics by Profile
```yaml
performance_targets:
  CEO:
    api_response_time: "<500ms"  # Strategic data from snapshots
    cache_hit_rate: ">90%"       # Pre-calculated data
    concurrent_users: "50+"      # Lower concurrency, higher data volume

  Risk_Manager:
    api_response_time: "<2000ms" # Real-time risk calculations
    cache_hit_rate: ">70%"       # Frequently changing data
    concurrent_users: "20+"      # Medium concurrency, complex queries

  Trader:
    api_response_time: "<1000ms" # Operational data from hot cache
    cache_hit_rate: ">85%"       # Frequently accessed operational data
    concurrent_users: "100+"     # High concurrency, simpler queries
```

### Database Performance Optimization
```sql
-- Indexes for hybrid architecture performance
CREATE INDEX CONCURRENTLY idx_kpi_snapshots_v2_profile_kpi
ON kpi_snapshots_v2(user_profile, kpi_id, period_date DESC);

CREATE INDEX CONCURRENTLY idx_kpi_snapshots_v2_dimensions_gin
ON kpi_snapshots_v2 USING GIN(dimensions);

-- Partitioning strategy for time-series data
CREATE TABLE kpi_snapshots_v2_2024_q1 PARTITION OF kpi_snapshots_v2
FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');
```

## 🧪 Testing Strategy Details

### Real Data Integration Tests
```python
# apps/backend/tests/integration/test_real_data_integration.py
class TestRealDataIntegration:
    """Test hybrid architecture with actual production data patterns"""

    async def test_ceo_profile_snapshot_performance(self):
        """CEO profile should get data from snapshots within 500ms"""
        start_time = time.time()

        router = SmartQueryRouter()
        result = await router.get_kpi_data(
            kpi_id="spread_income_detailed",
            timeframe="monthly",
            user_profile="CEO"
        )

        response_time = (time.time() - start_time) * 1000
        assert response_time < 500, f"CEO response time {response_time}ms exceeds 500ms"
        assert result["source_layer"] == "snapshot"

    async def test_trader_profile_cache_performance(self):
        """Trader profile should get operational data from cache within 1s"""
        # Similar test for trader profile with cache layer validation
```

### Profile Detection Accuracy Tests
```python
# Test profile detection with real QueryCache data
class TestProfileDetection:
    async def test_detection_with_historical_data(self):
        """Use existing QueryCache embeddings to validate detection accuracy"""

        # Get historical query patterns from existing QueryCache
        historical_queries = await self.get_historical_query_patterns()

        detector = ProfileDetector()
        correct_predictions = 0
        total_predictions = 0

        for query_pattern in historical_queries:
            predicted_profile = await detector.detect_profile_from_pattern(query_pattern)
            actual_profile = query_pattern.get("known_profile")  # From test data

            if predicted_profile == actual_profile:
                correct_predictions += 1
            total_predictions += 1

        accuracy = correct_predictions / total_predictions
        assert accuracy > 0.85, f"Detection accuracy {accuracy} below 85% threshold"
```

---

*This comprehensive task list ensures systematic implementation of the hybrid architecture while maintaining full compatibility with existing DataHero4 functionality and achieving all Phase 1 objectives.*
