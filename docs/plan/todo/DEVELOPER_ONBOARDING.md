# DataHero4 Developer Onboarding Guide

## Table of Contents
- [Project Overview & Differentiation](#project-overview--differentiation)
- [Technical Architecture](#technical-architecture)
- [Development Pipeline](#development-pipeline)
- [Codebase Structure](#codebase-structure)
- [Implementation Details](#implementation-details)
- [Getting Started Guide](#getting-started-guide)

## Project Overview & Differentiation

### Core Value Proposition

DataHero4 is a **next-generation Business Intelligence platform** that revolutionizes how organizations interact with their data through:

- **Conversational AI Interface**: Natural language queries that translate to precise SQL
- **Ultra-Fast Performance**: 99.9% improvement in dashboard loading (19ms vs 5-60s)
- **Real-Time KPI Generation**: Dynamic calculation and visualization of business metrics
- **No Mock Data Philosophy**: 100% real client data integration with fail-fast approach
- **Multi-Agent Architecture**: LangGraph-powered intelligent query processing

### Key Differentiating Features

#### 1. **Snapshot-First Architecture**
- Pre-calculated KPIs updated daily at 3 AM
- Instant dashboard loading with automatic fallback
- 6 critical KPIs always available in <20ms

#### 2. **Conversational Intelligence**
- Natural language to SQL translation
- Context-aware query refinement
- Multi-turn conversation support with memory

#### 3. **Real-Time Data Processing**
- Direct PostgreSQL integration (AWS RDS)
- No data warehousing delays
- Live KPI calculations with smart caching

#### 4. **Enterprise-Grade Reliability**
- 99.9% uptime with comprehensive monitoring
- Hierarchical caching system (Redis + PostgreSQL)
- Automatic error recovery and alerting

### Target Audience & Use Cases

**Primary Users:**
- Financial institutions (currency exchange, trading)
- Business analysts requiring real-time insights
- Executive teams needing instant KPI access
- Compliance teams monitoring regulatory metrics

**Key Use Cases:**
- Real-time trading volume monitoring
- Regulatory compliance tracking (COAF, PEP screening)
- Client risk assessment and exposure limits
- Performance analytics and trend analysis

## Technical Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Dashboard]
        Chat[Conversational Interface]
        Drawer[KPI Detail Drawer]
    end
    
    subgraph "API Gateway"
        FastAPI[FastAPI Server]
        Auth[Authentication]
        Router[API Router]
    end
    
    subgraph "Processing Layer"
        LG[LangGraph Pipeline]
        Agents[Multi-Agent System]
        Cache[Redis Cache]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        Snapshot[Daily Snapshots]
        RealTime[Real-time Queries]
    end
    
    UI --> FastAPI
    Chat --> FastAPI
    Drawer --> FastAPI
    FastAPI --> LG
    LG --> Agents
    Agents --> Cache
    Agents --> PG
    Cache --> PG
    PG --> Snapshot
    PG --> RealTime
```

### Frontend Architecture

**Technology Stack:**
- **React 18** with TypeScript
- **Vite** for build tooling and HMR
- **TailwindCSS** + **shadcn/ui** for styling
- **Framer Motion** for animations
- **React Query** for server state management
- **Zustand** for client state management

**Component Structure:**
```
src/
├── components/
│   ├── dashboard/          # Dashboard-specific components
│   │   ├── KpiBentoCard.tsx    # Individual KPI cards
│   │   ├── KpiBentoGrid.tsx    # Grid layout manager
│   │   └── AddKpiModal.tsx     # KPI selection modal
│   ├── kpi-drawer/         # Detailed KPI view
│   │   ├── KPIDrawer.tsx       # Main drawer component
│   │   └── KPIDrawerContent.tsx # Drawer content
│   ├── chat/               # Conversational interface
│   └── ui/                 # Reusable UI components
├── hooks/                  # Custom React hooks
├── lib/                    # Utilities and API clients
├── pages/                  # Route components
└── types/                  # TypeScript definitions
```

### Backend Architecture

**Technology Stack:**
- **Python 3.9+** with **Poetry** dependency management
- **FastAPI** for API framework
- **LangGraph** for multi-agent workflows
- **SQLAlchemy** for database ORM
- **Redis** for caching layer
- **PostgreSQL** for data persistence

**Service Architecture:**
```
src/
├── interfaces/
│   ├── api.py              # Main FastAPI application
│   ├── dashboard_api.py    # Dashboard endpoints
│   └── chat_api.py         # Conversational endpoints
├── services/
│   ├── kpi_service.py      # KPI calculation logic
│   ├── query_service.py    # SQL generation service
│   └── cache_service.py    # Caching strategies
├── repositories/
│   └── kpi_repository.py   # Data access layer
├── models/                 # SQLAlchemy models
├── utils/                  # Utility functions
└── config/                 # Configuration files
```

### Data Pipeline Flow

1. **Query Input** → Natural language or direct API call
2. **LangGraph Processing** → Multi-agent analysis and SQL generation
3. **Cache Check** → Redis lookup for existing results
4. **Database Query** → PostgreSQL execution if cache miss
5. **Result Processing** → Data formatting and enrichment
6. **Response Delivery** → JSON API response with metadata

## Development Pipeline

### Tech Stack Summary

**Frontend:**
- React 18 + TypeScript + Vite
- TailwindCSS + shadcn/ui + Framer Motion
- React Query + Zustand

**Backend:**
- Python 3.9+ + Poetry + FastAPI
- LangGraph + SQLAlchemy + Redis
- PostgreSQL + APScheduler

**Infrastructure:**
- Railway for deployment
- GitHub for version control
- Turbo for monorepo management

### Development Workflow

1. **Feature Development**
   ```bash
   # Create feature branch
   git checkout -b feature/new-kpi-calculation
   
   # Develop with hot reload
   npm run dev  # Starts both frontend and backend
   
   # Run tests
   npm run test
   ```

2. **Code Quality**
   ```bash
   # Linting and type checking
   npm run lint
   npm run typecheck
   
   # Backend formatting
   cd apps/backend && poetry run ruff check
   ```

3. **Deployment**
   ```bash
   # Build for production
   npm run build
   
   # Deploy to Railway (automatic on main branch push)
   git push origin main
   ```

### Testing Strategy

**Frontend Testing:**
- **Unit Tests**: Vitest + Testing Library
- **Integration Tests**: Component interaction testing
- **E2E Tests**: Playwright for user workflows

**Backend Testing:**
- **Unit Tests**: pytest for service logic
- **Integration Tests**: FastAPI TestClient
- **Performance Tests**: Load testing for KPI calculations

### Deployment Process

**Railway Platform Integration:**
- **Automatic Deployment**: Push to main triggers deployment
- **Environment Variables**: Managed through Railway dashboard
- **Health Checks**: Built-in monitoring and alerting
- **Scaling**: Automatic based on traffic patterns

**Environment Configuration:**
```bash
# Backend (.env)
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
OPENAI_API_KEY=...
ANTHROPIC_API_KEY=...

# Frontend (.env)
VITE_API_BASE_URL=http://localhost:8000
```

## Codebase Structure

### Monorepo Organization

```
datahero4/
├── apps/
│   ├── backend/            # Python FastAPI backend
│   │   ├── src/
│   │   │   ├── interfaces/ # API endpoints
│   │   │   ├── services/   # Business logic
│   │   │   ├── models/     # Data models
│   │   │   └── utils/      # Utilities
│   │   ├── docs/           # Backend documentation
│   │   └── pyproject.toml  # Poetry configuration
│   └── frontend/           # React TypeScript frontend
│       ├── src/
│       │   ├── components/ # React components
│       │   ├── hooks/      # Custom hooks
│       │   ├── lib/        # Utilities
│       │   └── pages/      # Route components
│       └── package.json    # NPM configuration
├── docs/                   # Project documentation
├── scripts/                # Development scripts
└── package.json           # Monorepo configuration
```

### Key Components & Responsibilities

#### Frontend Components

**Dashboard Components:**
- `KpiBentoCard`: Individual KPI visualization with charts
- `KpiBentoGrid`: Responsive grid layout with priority sorting
- `KPIDrawer`: Detailed KPI view with historical data
- `AddKpiModal`: KPI selection and configuration interface

**State Management:**
- `useKpis`: KPI data fetching and caching
- `useDashboardFilters`: Filter state management
- `useKPIDrawer`: Drawer state and animations

#### Backend Services

**Core Services:**
- `KpiService`: KPI calculation and caching logic
- `QueryService`: SQL generation and validation
- `CacheService`: Multi-layer caching strategies
- `LearningDBManager`: PostgreSQL operations

**API Endpoints:**
- `/api/dashboard/snapshot`: Ultra-fast KPI delivery
- `/api/dashboard/kpis`: Dynamic KPI calculations
- `/ask`: Conversational query processing
- `/health`: System health monitoring

### Database Schema

**Core Tables:**
```sql
-- KPI snapshots for ultra-fast loading
CREATE TABLE kpi_snapshots (
    id SERIAL PRIMARY KEY,
    kpi_id VARCHAR(50) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    value DECIMAL(15,2),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Query cache for performance optimization
CREATE TABLE query_cache (
    cache_key VARCHAR(255) PRIMARY KEY,
    result JSONB NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Learning system for query improvement
CREATE TABLE feedback_corrections (
    id SERIAL PRIMARY KEY,
    original_query TEXT,
    corrected_query TEXT,
    feedback_type VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints & Data Flow

**Dashboard API:**
```typescript
// GET /api/dashboard/snapshot - Ultra-fast KPI delivery
interface SnapshotResponse {
  success: boolean;
  data: KpiData[];
  metadata: {
    generated_at: string;
    cache_hit: boolean;
    response_time_ms: number;
  };
}

// GET /api/dashboard/kpis - Dynamic KPI calculations
interface KpiResponse {
  id: string;
  name: string;
  value: number;
  change_percent: number;
  chart_data: ChartPoint[];
  metadata: KpiMetadata;
}
```

**Conversational API:**
```typescript
// POST /ask - Natural language query processing
interface AskRequest {
  question: string;
  client_id: string;
  sector: string;
  conversation_id?: string;
}

interface AskResponse {
  answer: string;
  sql_query?: string;
  visualization?: VisualizationData;
  suggestions: string[];
  confidence: number;
}
```

## Implementation Details

### KPI Calculation System

**Real Data Approach:**
- **No Mock Data**: All calculations use live client data
- **Fail-Fast Philosophy**: Errors are exposed immediately, not masked
- **Cache Strategy**: Multi-layer caching (Redis → PostgreSQL → Snapshots)

**Calculation Flow:**
```python
def calculate_kpi(kpi_id: str, timeframe: str, currency: str) -> KpiResult:
    # 1. Check snapshot cache (fastest)
    if snapshot := get_snapshot(kpi_id, timeframe):
        return snapshot
    
    # 2. Check Redis cache (fast)
    if cached := redis_client.get(cache_key):
        return cached
    
    # 3. Calculate from database (slower but accurate)
    result = execute_kpi_query(kpi_id, timeframe, currency)
    
    # 4. Cache result for future requests
    redis_client.setex(cache_key, ttl=300, value=result)
    
    return result
```

### Dashboard Filtering & Caching

**Smart Filtering:**
- **Timeframe Filters**: 7 days, 30 days, 3 months, 1 year
- **Currency Filters**: All currencies or specific currency focus
- **Category Filters**: Volume, Performance, Risk, Compliance

**Caching Strategy:**
```typescript
// Frontend caching with React Query
const { data: kpis } = useQuery({
  queryKey: ['kpis', filters],
  queryFn: () => fetchKpis(filters),
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
});
```

### Drawer Functionality

**Card Management:**
- **Consistent Display**: Drawer cards match dashboard appearance
- **Priority System**: Star-based favoriting with visual feedback
- **Real-time Updates**: Changes sync between drawer and dashboard

**Implementation:**
```typescript
// Drawer state management
const useKPIDrawer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentKPI, setCurrentKPI] = useState<string | null>(null);
  
  const openDrawer = (kpiId: string, element: HTMLElement) => {
    setCurrentKPI(kpiId);
    setIsOpen(true);
    // Animate card transition
    element.style.opacity = '0.3';
  };
  
  return { isOpen, currentKPI, openDrawer, closeDrawer };
};
```

### Priority/Favoriting System

**Implementation:**
- **Visual Feedback**: Star icon with amber color scheme
- **State Persistence**: Priority status saved to backend
- **Grid Reordering**: Priority KPIs appear first in grid
- **Consistent Behavior**: Same logic in dashboard and drawer

```typescript
const togglePriority = async (kpiId: string) => {
  // Optimistic update
  setKpis(prev => prev.map(kpi => 
    kpi.id === kpiId 
      ? { ...kpi, is_priority: !kpi.is_priority }
      : kpi
  ));
  
  // Sync with backend
  await api.post(`/api/kpis/${kpiId}/toggle-priority`);
};
```

## Getting Started Guide

### Prerequisites

**System Requirements:**
- Node.js 18+ and npm 9+
- Python 3.9+ and Poetry
- PostgreSQL 13+
- Redis 6+

**Development Tools:**
- VS Code with recommended extensions
- Git for version control
- Docker (optional, for local databases)

### Local Development Setup

1. **Clone Repository:**
   ```bash
   git clone https://github.com/daniribeiroBR/datahero4.git
   cd datahero4
   ```

2. **Install Dependencies:**
   ```bash
   # Install monorepo dependencies
   npm install
   
   # Install backend dependencies
   cd apps/backend
   poetry install
   
   # Install frontend dependencies
   cd ../frontend
   npm install
   ```

3. **Environment Configuration:**
   ```bash
   # Backend environment
   cp apps/backend/.env.example apps/backend/.env
   # Edit with your database and API keys
   
   # Frontend environment
   cp apps/frontend/.env.example apps/frontend/.env
   # Configure API base URL
   ```

4. **Database Setup:**
   ```bash
   # Run migrations (if available)
   cd apps/backend
   poetry run alembic upgrade head
   
   # Or create tables manually using provided SQL
   ```

5. **Start Development Servers:**
   ```bash
   # Start both frontend and backend
   npm run dev
   
   # Or start individually
   npm run dev:backend  # Backend on :8000
   npm run dev:frontend # Frontend on :3000
   ```

### Required Environment Variables

**Backend (.env):**
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/datahero4
REDIS_URL=redis://localhost:6379

# LLM APIs
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
TOGETHER_API_KEY=your_together_key

# Application
DEBUG=true
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3000
```

**Frontend (.env):**
```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=DataHero4 Dashboard
```

### Common Development Tasks

**Running Tests:**
```bash
# All tests
npm run test

# Backend tests only
npm run test:backend

# Frontend tests only
npm run test:frontend
```

**Building for Production:**
```bash
# Build all apps
npm run build

# Build specific app
npm run build:backend
npm run build:frontend
```

**Database Operations:**
```bash
# Backend database utilities
cd apps/backend
poetry run python -m src.utils.db_setup  # Initialize database
poetry run python -m src.cli.snapshot_generator  # Generate KPI snapshots
```

**Debugging:**
```bash
# Backend debugging endpoints
curl http://localhost:8000/health  # Health check
curl http://localhost:8000/debug-pipeline  # Pipeline debugging

# Frontend debugging
npm run dev  # Includes React DevTools integration
```

### Development Best Practices

1. **Code Organization:**
   - Follow established folder structure
   - Use TypeScript for type safety
   - Implement proper error handling

2. **Performance:**
   - Leverage caching at all levels
   - Optimize database queries
   - Use React Query for server state

3. **Testing:**
   - Write tests for new features
   - Maintain test coverage above 80%
   - Use integration tests for critical paths

4. **Documentation:**
   - Update this guide for architectural changes
   - Document new API endpoints
   - Include code examples in comments

---

**Welcome to DataHero4! This guide should get you up and running quickly. For specific questions, refer to the detailed documentation in the `/docs` folder or reach out to the development team.**
