# KPIs Essenciais para Operadoras de Câmbio - DataHero4

## 🎯 Análise dos KPIs Atuais vs. KPIs Críticos do Setor

### **KPIs Atuais do DataHero4**
```yaml
atuais:
  - volume_total_negociado: "Volume financeiro total"
  - ticket_medio: "Valor médio por transação"
  - spread_medio: "Margem média de câmbio"
  - clientes_ativos: "Número de clientes ativos"
  - alertas_risco: "Quantidade de alertas de risco"
  - taxa_conversao: "Taxa de conversão de leads"
```

### **Avaliação Crítica dos KPIs Atuais**
- ✅ **Volume Total**: Crítico - indica saúde do negócio
- ✅ **Spread Médio**: Crítico - principal fonte de receita
- ⚠️ **Ticket Médio**: Importante mas não crítico
- ⚠️ **Clientes Ativos**: Importante mas pode ser mais específico
- ❌ **Taxa de Conversão**: Menos crítico para operação diária
- ❌ **Alertas de Risco**: <PERSON><PERSON> g<PERSON>, precisa ser específico

## 🏆 KPIs Críticos Essenciais (Baseado na Pesquisa do Setor)

### **Categoria 1: KPIs de Receita e Rentabilidade (Críticos)**
```yaml
receita_rentabilidade:
  spread_income:
    formula: "Spread Médio (%) × Volume Total Negociado"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Principal fonte de receita em operações de câmbio"
    
  margem_liquida:
    formula: "(Receita Total - Custos Operacionais) / Receita Total"
    criticidade: "CRÍTICO"
    frequencia: "Diário"
    descricao: "Rentabilidade real após custos operacionais"
    
  receita_por_cliente:
    formula: "Receita Total / Número de Clientes Ativos"
    criticidade: "CRÍTICO"
    frequencia: "Semanal"
    descricao: "ARPU - Average Revenue Per User"
    
  custo_por_transacao:
    formula: "Custos Operacionais Totais / Número de Transações"
    criticidade: "ALTO"
    frequencia: "Diário"
    descricao: "Eficiência operacional"
```

### **Categoria 2: KPIs de Risco e Exposição (Críticos)**
```yaml
risco_exposicao:
  exposicao_cambial:
    formula: "Posição Líquida por Moeda (Ativos - Passivos)"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Exposição ao risco cambial por moeda"
    
  var_diario:
    formula: "Value at Risk - Perda potencial em 24h com 95% confiança"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Quantifica risco de perdas adversas"
    
  utilizacao_limites:
    formula: "Exposição Atual / Limite Aprovado × 100"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Controle de limites de risco"
    
  concentracao_clientes:
    formula: "Volume Top 10 Clientes / Volume Total"
    criticidade: "ALTO"
    frequencia: "Diário"
    descricao: "Risco de concentração de clientes"
```

### **Categoria 3: KPIs Operacionais (Críticos)**
```yaml
operacionais:
  tempo_processamento:
    formula: "Tempo médio entre início e conclusão da transação"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Impacta satisfação e custos operacionais"
    
  uptime_sistema:
    formula: "(Tempo Total - Tempo Inativo) / Tempo Total × 100"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Disponibilidade do sistema"
    
  taxa_erro_transacoes:
    formula: "Transações com Erro / Total de Transações × 100"
    criticidade: "CRÍTICO"
    frequencia: "Tempo real"
    descricao: "Qualidade operacional"
    
  liquidez_disponivel:
    formula: "Saldo Disponível por Moeda / Volume Médio Diário"
    criticidade: "ALTO"
    frequencia: "Tempo real"
    descricao: "Capacidade de atender demanda"
```

### **Categoria 4: KPIs de Compliance (Críticos)**
```yaml
compliance:
  taxa_kyc_completo:
    formula: "Clientes com KYC Completo / Total de Clientes × 100"
    criticidade: "CRÍTICO"
    frequencia: "Diário"
    descricao: "Conformidade regulatória obrigatória"
    
  relatorios_coaf:
    formula: "Relatórios de Atividade Suspeita / Total de Transações"
    criticidade: "CRÍTICO"
    frequencia: "Diário"
    descricao: "Compliance anti-lavagem de dinheiro"
    
  tempo_resolucao_aml:
    formula: "Tempo médio para resolver alertas AML"
    criticidade: "ALTO"
    frequencia: "Diário"
    descricao: "Eficiência em compliance"
    
  adequacao_capital:
    formula: "Capital Próprio / Ativos Ponderados pelo Risco"
    criticidade: "CRÍTICO"
    frequencia: "Mensal"
    descricao: "Conformidade com Basileia/Banco Central"
```

### **Categoria 5: KPIs de Cliente e Mercado (Importantes)**
```yaml
cliente_mercado:
  nps_score:
    formula: "% Promotores - % Detratores"
    criticidade: "ALTO"
    frequencia: "Mensal"
    descricao: "Satisfação e lealdade do cliente"
    
  taxa_retencao:
    formula: "Clientes Retidos / Clientes Período Anterior × 100"
    criticidade: "ALTO"
    frequencia: "Mensal"
    descricao: "Retenção de clientes"
    
  market_share:
    formula: "Volume da Empresa / Volume Total do Mercado × 100"
    criticidade: "MÉDIO"
    frequencia: "Mensal"
    descricao: "Posição competitiva"
    
  cac_payback:
    formula: "Custo de Aquisição / Receita Mensal por Cliente"
    criticidade: "MÉDIO"
    frequencia: "Mensal"
    descricao: "Eficiência de aquisição"
```

## 🔄 Correlações Críticas Entre KPIs

### **Correlações de Alto Impacto**
```yaml
correlacoes_criticas:
  risco_rentabilidade:
    kpis: ["exposicao_cambial", "var_diario", "margem_liquida"]
    analise: "Maior exposição pode gerar maior rentabilidade mas aumenta risco"
    
  operacional_satisfacao:
    kpis: ["tempo_processamento", "uptime_sistema", "nps_score"]
    analise: "Performance operacional impacta diretamente satisfação"
    
  volume_eficiencia:
    kpis: ["volume_total", "custo_por_transacao", "spread_income"]
    analise: "Maior volume reduz custo unitário e aumenta receita"
    
  compliance_operacao:
    kpis: ["taxa_kyc_completo", "relatorios_coaf", "volume_total"]
    analise: "Compliance adequado permite crescimento sustentável"
```

## 🚨 Alertas e Thresholds Críticos

### **Alertas de Tempo Real (Ação Imediata)**
```yaml
alertas_tempo_real:
  exposicao_cambial:
    threshold: "> 80% do limite aprovado"
    acao: "Notificar mesa de operações + gerente de risco"
    
  var_diario:
    threshold: "> Limite de VaR estabelecido"
    acao: "Parar operações + escalar para diretoria"
    
  uptime_sistema:
    threshold: "< 99.9%"
    acao: "Alerta TI + comunicação clientes"
    
  taxa_erro:
    threshold: "> 0.1% das transações"
    acao: "Investigação imediata + possível parada"
```

### **Alertas Diários (Monitoramento)**
```yaml
alertas_diarios:
  margem_liquida:
    threshold: "< Meta estabelecida (-10%)"
    acao: "Revisão de pricing + análise de custos"
    
  kyc_compliance:
    threshold: "< 95% dos clientes"
    acao: "Intensificar processo KYC"
    
  concentracao_clientes:
    threshold: "> 30% do volume em top 10"
    acao: "Estratégia de diversificação"
```

## 🎛️ Dashboard por Perfil de Usuário

### **CEO/Diretoria (Visão Executiva)**
```yaml
dashboard_executivo:
  kpis_principais:
    - margem_liquida
    - volume_total_negociado
    - receita_por_cliente
    - nps_score
    - adequacao_capital
  
  frequencia_atualizacao: "Diário"
  formato: "Cards grandes + gráficos de tendência"
  alertas: "Apenas críticos"
```

### **Gerente de Risco (Visão de Risco)**
```yaml
dashboard_risco:
  kpis_principais:
    - exposicao_cambial
    - var_diario
    - utilizacao_limites
    - concentracao_clientes
    - liquidez_disponivel
  
  frequencia_atualizacao: "Tempo real"
  formato: "Tabelas detalhadas + heatmaps"
  alertas: "Todos os níveis de risco"
```

### **Gerente de Operações (Visão Operacional)**
```yaml
dashboard_operacional:
  kpis_principais:
    - tempo_processamento
    - uptime_sistema
    - taxa_erro_transacoes
    - custo_por_transacao
    - volume_por_hora
  
  frequencia_atualizacao: "Tempo real"
  formato: "Gráficos de linha + métricas detalhadas"
  alertas: "Operacionais e de qualidade"
```

### **Compliance (Visão Regulatória)**
```yaml
dashboard_compliance:
  kpis_principais:
    - taxa_kyc_completo
    - relatorios_coaf
    - tempo_resolucao_aml
    - adequacao_capital
    - transacoes_suspeitas
  
  frequencia_atualizacao: "Diário"
  formato: "Relatórios + status de conformidade"
  alertas: "Regulatórios e de compliance"
```

## 🔍 Integração com Pesquisa Externa

### **APIs de Dados Externos Essenciais**
```yaml
apis_essenciais:
  banco_central_brasil:
    url: "https://api.bcb.gov.br/dados/serie"
    dados: "Cotações oficiais, SELIC, IPCA, PIB"
    frequencia: "Diário"
    custo: "Gratuito"
    
  financial_modeling_prep:
    url: "https://financialmodelingprep.com/api"
    dados: "Cotações tempo real, notícias FX"
    frequencia: "Tempo real"
    custo: "Freemium ($15-50/mês)"
    
  eodhd:
    url: "https://eodhd.com/api"
    dados: "Dados históricos, notícias, sentimento"
    frequencia: "Tempo real"
    custo: "$19.99/mês"
    
  brave_search:
    url: "https://api.search.brave.com"
    dados: "Pesquisa web para análise contextual"
    frequencia: "On-demand"
    custo: "$3/1000 queries"
    
  perplexity_api:
    url: "https://api.perplexity.ai"
    dados: "Pesquisa e análise profunda"
    frequencia: "On-demand"
    custo: "$5/1000 queries"
```

### **Dados de Contexto para Super Agentes**
```yaml
contexto_super_agentes:
  mercado_cambial:
    - cotacoes_tempo_real
    - volatilidade_historica
    - volume_mercado_global
    - eventos_economicos
    - decisoes_banco_central
    
  indicadores_economicos:
    - taxa_selic
    - ipca_inflacao
    - pib_crescimento
    - balanca_comercial
    - reservas_internacionais
    
  noticias_sentimento:
    - noticias_economia
    - sentimento_mercado
    - analises_especialistas
    - eventos_geopoliticos
    - mudancas_regulatorias
    
  benchmarking:
    - spreads_concorrentes
    - volumes_mercado
    - performance_setor
    - melhores_praticas
    - tendencias_tecnologicas
```

## 📊 Roadmap de Implementação dos Novos KPIs

### **Fase 1: Arquitetura Híbrida + KPIs Fundamentais (8 semanas)**
```yaml
fase_1_hibrida:
  objetivo: "Arquitetura híbrida 3 camadas + KPIs personalizados"
  aproveitamento_existente: "70% infraestrutura já implementada"

  arquitetura_3_camadas:
    camada_1: "Snapshots críticos (estender sistema existente)"
    camada_2: "Cache quente personalizado (estender UnifiedCache)"
    camada_3: "Query direta otimizada (usar KpiRepository)"

  kpis_implementar_via_hibrida:
    - spread_income_detalhado (via snapshot para CEO, cache para Trader)
    - margem_liquida_operacional (via snapshot para CEO/CFO)
    - custo_por_transacao (via cache para CFO/Operações)
    - tempo_processamento_medio (via query direta para Operações/Risk)

  integracao_bcb_fail_fast:
    - banco_central_brasil (cotações oficiais, sem fallbacks)
    - fail_fast_se_bcb_indisponivel

  roteamento_inteligente:
    - smart_query_router_entre_3_camadas
    - ttl_personalizado_por_perfil
    - fail_fast_sem_fallbacks_entre_camadas
```

### **Fase 2: KPIs de Rentabilidade (3 semanas)**
```yaml
fase_2:
  objetivo: "Otimizar KPIs de receita e rentabilidade"
  kpis_implementar:
    - spread_income
    - margem_liquida
    - receita_por_cliente
    - custo_por_transacao
  
  melhorias_existentes:
    - refinar_volume_total
    - detalhar_spread_medio
    - segmentar_por_moeda
```

### **Fase 3: KPIs de Compliance (4 semanas)**
```yaml
fase_3:
  objetivo: "Fortalecer compliance e regulatório"
  kpis_implementar:
    - taxa_kyc_completo
    - relatorios_coaf
    - tempo_resolucao_aml
    - adequacao_capital
  
  integracao_regulatoria:
    - apis_banco_central
    - sistemas_coaf
    - bases_pep_sancionados
```

### **Fase 4: Super Agente com Pesquisa Externa (5 semanas)**
```yaml
fase_4:
  objetivo: "Implementar capacidade de pesquisa profunda"
  componentes:
    - brave_search_integration
    - perplexity_api_integration
    - contexto_mercado_automatico
    - correlacao_kpis_mercado
  
  funcionalidades:
    - analise_contextual_automatica
    - benchmarking_tempo_real
    - alertas_inteligentes_mercado
    - recomendacoes_baseadas_contexto
```

## 🏗️ Arquitetura Híbrida para KPIs de Câmbio

### **Integração com Infraestrutura Existente DataHero4**
```python
# apps/backend/src/services/hybrid_kpi_system.py
from src.services.snapshot_service import SnapshotService  # JÁ EXISTE
from src.caching.unified_cache_system import UnifiedCacheSystem  # JÁ EXISTE
from src.models.kpi_models import KpiRepository  # JÁ EXISTE

class HybridKpiSystem:
    """Sistema híbrido aproveitando infraestrutura existente"""

    def __init__(self):
        # Aproveitando componentes já implementados
        self.snapshot_service = SnapshotService()  # Sistema 3AM existente
        self.cache_system = UnifiedCacheSystem()   # Cache LRU existente
        self.kpi_repository = KpiRepository()      # Repository existente

        # Extensões para personalização
        self.smart_router = SmartQueryRouter()
        self.profile_detector = ProfileDetector()

        # APIs externas (Fase 2)
        self.bcb_api = BancoCentralAPI()
        self.market_apis = MarketDataAPIs()

    async def deep_kpi_analysis(self, context: ResearchContext) -> Dict[str, Any]:
        """Análise profunda de KPI com contexto externo"""

        # 1. Coleta paralela de dados externos
        external_data = await self._gather_external_data(context)

        # 2. Análise de correlações com mercado
        market_correlations = await self._analyze_market_correlations(
            context, external_data
        )

        # 3. Benchmarking com setor
        industry_benchmark = await self._get_industry_benchmark(context)

        # 4. Análise de sentimento e notícias
        sentiment_analysis = await self._analyze_market_sentiment(context)

        # 5. Previsões e recomendações
        predictions = await self._generate_predictions(
            context, external_data, market_correlations
        )

        return {
            "deep_analysis": {
                "external_data": external_data,
                "market_correlations": market_correlations,
                "industry_benchmark": industry_benchmark,
                "sentiment_analysis": sentiment_analysis,
                "predictions": predictions,
                "confidence_score": self._calculate_confidence(external_data),
                "generated_at": datetime.now().isoformat(),
                "data_sources": self._get_data_sources_used()
            }
        }

    async def _gather_external_data(self, context: ResearchContext) -> Dict[str, Any]:
        """Coleta paralela de dados de múltiplas fontes"""

        tasks = []

        # Dados do Banco Central
        if context.currency_pair:
            tasks.append(self._get_bcb_data(context))

        # Dados de mercado em tempo real
        tasks.append(self._get_market_data(context))

        # Indicadores econômicos
        tasks.append(self._get_economic_indicators(context))

        # Notícias e eventos
        tasks.append(self._get_news_and_events(context))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        return {
            "bcb_data": results[0] if len(results) > 0 else {},
            "market_data": results[1] if len(results) > 1 else {},
            "economic_indicators": results[2] if len(results) > 2 else {},
            "news_events": results[3] if len(results) > 3 else {}
        }

    async def _get_bcb_data(self, context: ResearchContext) -> Dict[str, Any]:
        """Dados do Banco Central do Brasil - FAIL FAST"""

        data = {}

        # Taxa SELIC - fail fast se não conseguir
        try:
            selic_data = await self.bcb_api.get_selic_rate()
            data["selic_rate"] = selic_data
        except Exception as e:
            logger.error(f"CRITICAL: Failed to get SELIC rate: {e}")
            raise Exception(f"BCB SELIC integration failed: {e}")

        # Cotação oficial USD/BRL - fail fast se não conseguir
        if "USD" in context.currency_pair:
            try:
                usd_rate = await self.bcb_api.get_official_rate("USD")
                data["official_usd_rate"] = usd_rate
            except Exception as e:
                logger.error(f"CRITICAL: Failed to get USD rate: {e}")
                raise Exception(f"BCB USD rate integration failed: {e}")

        # IPCA (inflação) - fail fast se não conseguir
        try:
            ipca_data = await self.bcb_api.get_ipca()
            data["inflation_ipca"] = ipca_data
        except Exception as e:
            logger.error(f"CRITICAL: Failed to get IPCA: {e}")
            raise Exception(f"BCB IPCA integration failed: {e}")

        return data

    async def _get_market_data(self, context: ResearchContext) -> Dict[str, Any]:
        """Dados de mercado em tempo real"""

        data = {}

        # Cotações em tempo real
        if context.currency_pair:
            real_time_rate = await self.fmp_api.get_fx_rate(context.currency_pair)
            data["real_time_rate"] = real_time_rate

            # Volatilidade histórica
            volatility = await self.fmp_api.get_volatility(
                context.currency_pair, context.timeframe
            )
            data["volatility"] = volatility

        # Volume global do par
        global_volume = await self.fmp_api.get_global_volume(context.currency_pair)
        data["global_volume"] = global_volume

        return data

    async def _analyze_market_correlations(
        self,
        context: ResearchContext,
        external_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Análise de correlações com dados de mercado"""

        correlations = {}

        # Correlação com SELIC
        if "selic_rate" in external_data.get("bcb_data", {}):
            selic_correlation = self._calculate_correlation(
                context.current_value,
                external_data["bcb_data"]["selic_rate"]["current"]
            )
            correlations["selic_correlation"] = selic_correlation

        # Correlação com volatilidade
        if "volatility" in external_data.get("market_data", {}):
            vol_correlation = self._calculate_correlation(
                context.current_value,
                external_data["market_data"]["volatility"]["current"]
            )
            correlations["volatility_correlation"] = vol_correlation

        # Correlação com volume global
        if "global_volume" in external_data.get("market_data", {}):
            volume_correlation = self._calculate_correlation(
                context.current_value,
                external_data["market_data"]["global_volume"]["current"]
            )
            correlations["global_volume_correlation"] = volume_correlation

        return correlations

    async def _analyze_market_sentiment(self, context: ResearchContext) -> Dict[str, Any]:
        """Análise de sentimento do mercado"""

        # Pesquisa de notícias relevantes
        search_query = f"{context.currency_pair} exchange rate market analysis"
        news_results = await self.brave_search.search(search_query, count=10)

        # Análise de sentimento via Perplexity
        sentiment_query = f"""
        Analyze the current market sentiment for {context.currency_pair}
        based on recent news and economic indicators.
        Consider factors like central bank policies, economic data,
        and geopolitical events. Provide a sentiment score from -1 to 1.
        """

        sentiment_analysis = await self.perplexity_api.research(sentiment_query)

        return {
            "news_articles": news_results,
            "sentiment_score": self._extract_sentiment_score(sentiment_analysis),
            "key_factors": self._extract_key_factors(sentiment_analysis),
            "market_outlook": sentiment_analysis.get("summary", "")
        }

    async def _get_industry_benchmark(self, context: ResearchContext) -> Dict[str, Any]:
        """Benchmarking com dados do setor"""

        # Pesquisa de benchmarks via Perplexity
        benchmark_query = f"""
        What are the industry benchmarks for {context.kpi_id}
        in currency exchange operations?
        Provide specific numbers and ranges for:
        - Small currency exchange operators
        - Medium-sized FX companies
        - Large institutional players
        Include recent market data and industry reports.
        """

        benchmark_data = await self.perplexity_api.research(benchmark_query)

        return {
            "industry_average": self._extract_benchmark_value(benchmark_data, "average"),
            "top_quartile": self._extract_benchmark_value(benchmark_data, "top_quartile"),
            "market_leaders": self._extract_benchmark_value(benchmark_data, "leaders"),
            "company_position": self._calculate_position(context.current_value, benchmark_data),
            "improvement_potential": self._calculate_improvement_potential(
                context.current_value, benchmark_data
            )
        }
```

### **Integração com APIs Externas**
```python
# apps/backend/src/services/external_apis.py
import aiohttp
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class BancoCentralAPI:
    """API do Banco Central do Brasil"""

    BASE_URL = "https://api.bcb.gov.br/dados/serie"

    async def get_selic_rate(self) -> Dict[str, Any]:
        """Taxa SELIC atual e histórica"""
        url = f"{self.BASE_URL}/bcdata.sgs.432/dados/ultimos/30"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params={"formato": "json"}) as response:
                data = await response.json()

                return {
                    "current": float(data[-1]["valor"]),
                    "previous": float(data[-2]["valor"]) if len(data) > 1 else None,
                    "trend": self._calculate_trend(data),
                    "last_update": data[-1]["data"]
                }

    async def get_official_rate(self, currency: str) -> Dict[str, Any]:
        """Cotação oficial do Banco Central"""
        # Série 1 = USD/BRL
        series_map = {"USD": "1", "EUR": "21619", "GBP": "21620"}
        series_id = series_map.get(currency, "1")

        url = f"{self.BASE_URL}/bcdata.sgs.{series_id}/dados/ultimos/30"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params={"formato": "json"}) as response:
                data = await response.json()

                return {
                    "current": float(data[-1]["valor"]),
                    "previous": float(data[-2]["valor"]) if len(data) > 1 else None,
                    "volatility": self._calculate_volatility(data),
                    "last_update": data[-1]["data"]
                }

class FinancialModelingPrepAPI:
    """Financial Modeling Prep API"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://financialmodelingprep.com/api/v3"

    async def get_fx_rate(self, currency_pair: str) -> Dict[str, Any]:
        """Cotação em tempo real"""
        url = f"{self.base_url}/fx/{currency_pair}"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params={"apikey": self.api_key}) as response:
                data = await response.json()

                if data:
                    return {
                        "rate": data[0]["bid"],
                        "ask": data[0]["ask"],
                        "spread": data[0]["ask"] - data[0]["bid"],
                        "timestamp": datetime.now().isoformat()
                    }
                return {}

    async def get_forex_news(self, currency: str) -> List[Dict[str, Any]]:
        """Notícias de FX"""
        url = f"{self.base_url}/stock_news"

        params = {
            "apikey": self.api_key,
            "tickers": currency,
            "limit": 20
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                news_data = await response.json()

                return [
                    {
                        "title": article["title"],
                        "summary": article["text"][:200],
                        "url": article["url"],
                        "published": article["publishedDate"],
                        "sentiment": self._analyze_article_sentiment(article["text"])
                    }
                    for article in news_data[:10]
                ]

class BraveSearchAPI:
    """Brave Search API para pesquisa web"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.search.brave.com/res/v1/web/search"

    async def search(self, query: str, count: int = 10) -> List[Dict[str, Any]]:
        """Pesquisa web"""
        headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": self.api_key
        }

        params = {
            "q": query,
            "count": count,
            "search_lang": "pt",
            "country": "BR"
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(
                self.base_url,
                headers=headers,
                params=params
            ) as response:
                data = await response.json()

                return [
                    {
                        "title": result["title"],
                        "description": result["description"],
                        "url": result["url"],
                        "relevance_score": self._calculate_relevance(result, query)
                    }
                    for result in data.get("web", {}).get("results", [])
                ]

class PerplexityAPI:
    """Perplexity API para pesquisa e análise profunda"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"

    async def research(self, query: str) -> Dict[str, Any]:
        """Pesquisa profunda com análise"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": "llama-3.1-sonar-large-128k-online",
            "messages": [
                {
                    "role": "system",
                    "content": "You are a financial market analyst specializing in currency exchange and FX markets. Provide detailed, data-driven analysis with specific numbers and sources."
                },
                {
                    "role": "user",
                    "content": query
                }
            ],
            "temperature": 0.2,
            "max_tokens": 1000
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url,
                headers=headers,
                json=payload
            ) as response:
                data = await response.json()

                content = data["choices"][0]["message"]["content"]

                return {
                    "analysis": content,
                    "summary": self._extract_summary(content),
                    "key_points": self._extract_key_points(content),
                    "data_sources": self._extract_sources(content),
                    "confidence": self._assess_confidence(content)
                }
```

### **Sistema de Cache Inteligente para APIs Externas**
```python
# apps/backend/src/services/external_data_cache.py
import redis
import json
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class ExternalDataCache:
    """Cache inteligente para dados externos com TTL diferenciado"""

    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.cache_ttl = {
            "bcb_selic": 3600,  # 1 hora
            "bcb_official_rates": 1800,  # 30 minutos
            "fx_real_time": 60,  # 1 minuto
            "news_sentiment": 900,  # 15 minutos
            "industry_benchmark": 86400,  # 24 horas
            "economic_indicators": 3600,  # 1 hora
        }

    async def get_cached_data(
        self,
        data_type: str,
        key_params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Recupera dados do cache"""

        cache_key = self._build_cache_key(data_type, key_params)
        cached_data = self.redis.get(cache_key)

        if cached_data:
            data = json.loads(cached_data)

            # Verificar se ainda está válido
            cached_at = datetime.fromisoformat(data["cached_at"])
            ttl = self.cache_ttl.get(data_type, 3600)

            if datetime.now() - cached_at < timedelta(seconds=ttl):
                return data["content"]

        return None

    async def cache_data(
        self,
        data_type: str,
        key_params: Dict[str, Any],
        data: Dict[str, Any]
    ) -> None:
        """Armazena dados no cache"""

        cache_key = self._build_cache_key(data_type, key_params)
        ttl = self.cache_ttl.get(data_type, 3600)

        cache_entry = {
            "content": data,
            "cached_at": datetime.now().isoformat(),
            "data_type": data_type,
            "key_params": key_params
        }

        self.redis.setex(
            cache_key,
            ttl,
            json.dumps(cache_entry, default=str)
        )

    def _build_cache_key(self, data_type: str, key_params: Dict[str, Any]) -> str:
        """Constrói chave de cache"""
        param_str = "_".join([f"{k}:{v}" for k, v in sorted(key_params.items())])
        return f"external_data:{data_type}:{param_str}"
```

### **Integração com o LangGraph Existente**
```python
# apps/backend/src/agents/enhanced_coordinator.py (modificado)
from src.agents.external_research_agent import ExternalResearchAgent, ResearchContext

class EnhancedCoordinator:
    """Coordinator expandido com pesquisa externa"""

    def __init__(self):
        # Agentes existentes
        self.query_generator = QueryGeneratorAgent()
        self.validator = UnifiedValidatorAgent()
        self.executor = SQLExecutorAgent()
        self.business_analyst = BusinessAnalystAgent()

        # Novo agente de pesquisa externa
        self.external_research = ExternalResearchAgent()

    async def _determine_next_agent_enhanced(self, state: DataHeroState) -> str:
        """Roteamento expandido com pesquisa externa"""

        # Lógica existente mantida...

        # Nova lógica para pesquisa externa
        if state.get("request_deep_analysis") or state.get("request_market_context"):
            return "external_research"

        # Se temos análise de negócio mas falta contexto de mercado
        if (state.get("business_analysis") and
            not state.get("market_context") and
            self._should_add_market_context(state)):
            return "external_research"

        return "__end__"

    def _should_add_market_context(self, state: DataHeroState) -> bool:
        """Determina se deve adicionar contexto de mercado"""

        # KPIs que se beneficiam de contexto externo
        context_beneficial_kpis = [
            "exposicao_cambial",
            "var_diario",
            "spread_income",
            "volume_total_negociado"
        ]

        current_kpi = state.get("current_kpi_id", "")
        return any(kpi in current_kpi for kpi in context_beneficial_kpis)
```

---

*Esta arquitetura integra pesquisa externa profunda ao DataHero4, permitindo que os Super Agentes tenham acesso a dados de mercado, indicadores econômicos e análises contextuais em tempo real para fornecer insights muito mais ricos e acionáveis.*
