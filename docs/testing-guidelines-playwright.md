# 🎯 Regras para Testes Frontend com MCP Playwright

## 📋 **OBJETIVO: EVITAR FALSOS POSITIVOS**

Esta documentação estabelece regras rigorosas para testes com MCP Playwright, garantindo que os resultados sejam confiáveis e não mascarados por interpretações superficiais.

---

## 🚨 **REGRA PRINCIPAL: DOCUMENTAÇÃO SISTEMÁTICA**

### **ANTES DE DECLARAR SUCESSO, SEMPRE:**

1. **📊 DOCUMENTE VALORES EXATOS** - Anote valores específicos antes e depois de cada ação
2. **🔍 VERIFIQUE MÚLTIPLOS CENÁRIOS** - Teste todos os casos relevantes, não apenas um
3. **📝 CONFIRME COM LOGS** - Sempre correlacione resultados visuais com logs do backend
4. **⚠️ IDENTIFIQUE PROBLEMAS** - Liste explicitamente o que NÃO está funcionando

---

## 📋 **CHECKLIST OBRIGATÓRIO PARA TESTES**

### **✅ ANTES DE INICIAR O TESTE:**

- [ ] **Definir objetivo específico** do teste (ex: "verificar se filtros de timeframe alteram valores dos KPIs")
- [ ] **Listar cenários a testar** (ex: todos os perfis, todos os timeframes)
- [ ] **Estabelecer critérios de sucesso** (ex: "valores devem mudar em pelo menos 5%")

### **✅ DURANTE O TESTE:**

- [ ] **Documentar estado inicial** com valores exatos
- [ ] **Executar uma ação por vez** e verificar resultado
- [ ] **Anotar valores intermediários** após cada mudança
- [ ] **Verificar logs do backend** para confirmar processamento
- [ ] **Testar cenários negativos** (casos que devem falhar)

### **✅ APÓS O TESTE:**

- [ ] **Comparar valores documentados** e calcular diferenças percentuais
- [ ] **Listar problemas encontrados** explicitamente
- [ ] **Confirmar com evidências** (screenshots, logs, valores)
- [ ] **Declarar status real** (funcionando/parcial/não funcionando)

---

## 🔍 **TEMPLATE DE TESTE SISTEMÁTICO**

### **1. DEFINIÇÃO DO TESTE:**
```
🎯 OBJETIVO: [Descrever o que está sendo testado]
📋 CENÁRIOS: [Listar todos os casos a testar]
✅ CRITÉRIO DE SUCESSO: [Definir quando considerar sucesso]
```

### **2. EXECUÇÃO DOCUMENTADA:**
```
📊 ESTADO INICIAL:
- Perfil: [nome]
- Timeframe: [período]
- KPI 1: [valor exato]
- KPI 2: [valor exato]
- ...

🔄 AÇÃO EXECUTADA: [Descrever mudança feita]

📊 ESTADO FINAL:
- Perfil: [nome]
- Timeframe: [período]
- KPI 1: [valor exato] (Δ: [diferença])
- KPI 2: [valor exato] (Δ: [diferença])
- ...

📝 LOGS VERIFICADOS: [Confirmar se logs mostram processamento correto]
```

### **3. ANÁLISE DE RESULTADOS:**
```
✅ FUNCIONANDO:
- [Listar o que funciona corretamente]

⚠️ PROBLEMAS IDENTIFICADOS:
- [Listar problemas específicos]

❌ NÃO FUNCIONANDO:
- [Listar o que não funciona]

🎯 CONCLUSÃO: [Status real baseado em evidências]
```

---

## 🚫 **ANTI-PADRÕES A EVITAR**

### **❌ NUNCA FAÇA:**

1. **Declarar sucesso baseado em um único teste**
2. **Ignorar valores zerados ou estranhos**
3. **Assumir que "apareceu na tela" = "funcionando"**
4. **Testar apenas o "caminho feliz"**
5. **Não verificar logs do backend**
6. **Não documentar valores específicos**
7. **Não testar todos os cenários relevantes**

### **❌ FRASES PROIBIDAS:**

- "Tudo está funcionando!" (sem evidências)
- "Os valores mudaram!" (sem especificar quanto)
- "Parece estar OK!" (sem verificação sistemática)
- "Funcionando perfeitamente!" (sem testar todos os casos)

---

## ✅ **PADRÕES CORRETOS**

### **✅ SEMPRE FAÇA:**

1. **Teste sistemático de TODOS os cenários relevantes**
2. **Documentação precisa de valores antes/depois**
3. **Verificação cruzada com logs do backend**
4. **Identificação explícita de problemas**
5. **Conclusões baseadas em evidências concretas**

### **✅ FRASES RECOMENDADAS:**

- "Testei X cenários, Y funcionam, Z têm problemas"
- "Valores mudaram de A para B (diferença de C%)"
- "Logs confirmam processamento correto para casos X, Y, Z"
- "Identificados problemas específicos em: [lista]"

---

## 📊 **EXEMPLO DE TESTE CORRETO**

### **🎯 OBJETIVO:** Verificar se filtros de timeframe alteram valores dos KPIs

### **📋 CENÁRIOS:** Todos os 5 perfis × 4 timeframes = 20 combinações

### **📊 RESULTADOS DOCUMENTADOS:**

| Perfil | Timeframe | KPIs Visíveis | Valores Reais | Status |
|--------|-----------|---------------|---------------|--------|
| Operations | 30 dias | 4 | Sim (R$ 4.607, 2.27s, 19.64, 11.37) | ✅ |
| Operations | 3 meses | 4 | Sim (R$ 4.895, 2.28s, 20.98, 11.29) | ✅ |
| CEO | 30 dias | 2 | Não (R$ 0, 0.00%) | ❌ |
| CFO | 30 dias | 1 | Não (0.00%) | ❌ |
| Trader | 30 dias | 1 | Não (R$ 0) | ❌ |
| Risk Manager | 30 dias | 1 | Sim (2.35s) | ⚠️ |

### **🎯 CONCLUSÃO BASEADA EM EVIDÊNCIAS:**
- ✅ **Filtros funcionam** para perfil Operations (valores mudam 5-6%)
- ❌ **Dados inexistentes** para CEO, CFO, Trader (valores zerados)
- ⚠️ **Funcionalidade parcial** para Risk Manager (poucos KPIs)
- **Status geral: PARCIALMENTE FUNCIONANDO** (20% dos casos)

---

## 🔧 **IMPLEMENTAÇÃO DA REGRA**

### **PARA CADA TESTE COM MCP PLAYWRIGHT:**

1. **Copie o template** de teste sistemático
2. **Preencha todos os campos** obrigatórios
3. **Execute o checklist** completo
4. **Documente evidências** concretas
5. **Declare status real** baseado em dados

### **ANTES DE DECLARAR SUCESSO:**

- [ ] Testei TODOS os cenários relevantes?
- [ ] Documentei valores específicos?
- [ ] Verifiquei logs do backend?
- [ ] Identifiquei problemas explicitamente?
- [ ] Tenho evidências concretas?

---

## 🏆 **BENEFÍCIOS DESTA ABORDAGEM**

1. **Elimina falsos positivos** através de verificação sistemática
2. **Identifica problemas reais** que precisam ser corrigidos
3. **Fornece evidências concretas** para tomada de decisão
4. **Melhora qualidade** dos testes e do produto final
5. **Evita retrabalho** causado por problemas não identificados

---

**📝 LEMBRE-SE: Um teste rigoroso que identifica problemas é infinitamente mais valioso que um teste superficial que mascara falhas!**
