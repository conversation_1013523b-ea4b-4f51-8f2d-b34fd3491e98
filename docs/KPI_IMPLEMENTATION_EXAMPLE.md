# KPI Implementation Example: Revenue Total Monthly
## Integrating with Existing DataHero4 Architecture

### 📋 **Overview**

This document demonstrates how to implement a new KPI (`receita_total_mensal`) by extending the existing DataHero4 architecture **without refactoring** any current systems.

**Target KPI**: Monthly Total Revenue (CFO Profile - High Priority)  
**Data Source**: `conta_receber` table (validated with MCP Postgres)  
**Integration Points**: HybridKpiService, PersonalizedCacheSystem, SmartQueryRouter

---

## 🔧 **Step 1: Extend HybridKpiService**

### **File**: `apps/backend/src/services/hybrid_kpi_service.py`

```python
# ADD to existing kpi_calculators mapping (line ~48)
self.kpi_calculators = {
    # EXISTING KPIs (DO NOT CHANGE)
    'spread_income_detailed': self._calculate_spread_income_detailed,
    'margem_liquida_operacional': self._calculate_margem_liquida_operacional,
    'custo_por_transacao': self._calculate_custo_por_transacao,
    'tempo_processamento_medio': self._calculate_tempo_processamento_medio,
    
    # NEW KPI - ADD THIS LINE
    'receita_total_mensal': self._calculate_receita_total_mensal,
}

# ADD new method at end of class (after line ~683)
def _calculate_receita_total_mensal(
    self,
    client_id: str,
    timeframe: str = "month",
    currency: str = "all"
) -> Dict[str, Any]:
    """
    Calculate monthly total revenue from conta_receber table.
    
    Validated query using MCP Postgres - 5,454 records available.
    """
    logger.info(f"💰 Calculating monthly revenue for timeframe: {timeframe}")
    
    try:
        # Build date filter based on timeframe
        if timeframe == "month":
            date_filter = "data_emissao >= DATE_TRUNC('month', CURRENT_DATE)"
        elif timeframe == "quarter":
            date_filter = "data_emissao >= DATE_TRUNC('quarter', CURRENT_DATE)"
        elif timeframe == "year":
            date_filter = "data_emissao >= DATE_TRUNC('year', CURRENT_DATE)"
        else:
            date_filter = "data_emissao >= CURRENT_DATE - INTERVAL '30 days'"
        
        # SQL query validated with MCP Postgres
        query = f"""
        WITH monthly_revenue AS (
            SELECT 
                DATE_TRUNC('month', data_emissao) as month,
                SUM(valor_total) as monthly_revenue,
                COUNT(*) as invoice_count,
                AVG(valor_total) as avg_invoice_value,
                SUM(valor_iss + valor_pis + valor_cofins + valor_csll + valor_irpj) as total_taxes,
                SUM(valor_total - (valor_iss + valor_pis + valor_cofins + valor_csll + valor_irpj)) as net_revenue
            FROM conta_receber 
            WHERE {date_filter}
            GROUP BY DATE_TRUNC('month', data_emissao)
            ORDER BY month DESC
        ),
        growth_analysis AS (
            SELECT 
                month,
                monthly_revenue,
                net_revenue,
                invoice_count,
                avg_invoice_value,
                total_taxes,
                LAG(monthly_revenue) OVER (ORDER BY month) as prev_month_revenue,
                CASE 
                    WHEN LAG(monthly_revenue) OVER (ORDER BY month) > 0 
                    THEN ROUND(((monthly_revenue - LAG(monthly_revenue) OVER (ORDER BY month)) / LAG(monthly_revenue) OVER (ORDER BY month)) * 100, 2)
                    ELSE NULL 
                END as mom_growth_percent
            FROM monthly_revenue
        )
        SELECT 
            month,
            monthly_revenue,
            net_revenue,
            invoice_count,
            avg_invoice_value,
            total_taxes,
            prev_month_revenue,
            mom_growth_percent,
            CASE 
                WHEN mom_growth_percent > 10 THEN 'EXCELLENT'
                WHEN mom_growth_percent > 5 THEN 'GOOD'
                WHEN mom_growth_percent > 0 THEN 'POSITIVE'
                WHEN mom_growth_percent < -5 THEN 'CONCERNING'
                ELSE 'STABLE'
            END as growth_category
        FROM growth_analysis
        ORDER BY month DESC
        LIMIT 12;
        """
        
        # Execute query using existing database connection
        with self.db_manager.get_session() as session:
            result = session.execute(text(query))
            rows = result.fetchall()
            
            if not rows:
                return {
                    'error': 'no_data',
                    'message': 'No revenue data found for specified timeframe',
                    'kpi_id': 'receita_total_mensal',
                    'timeframe': timeframe
                }
            
            # Format results
            monthly_data = []
            current_month_revenue = 0
            total_revenue_ytd = 0
            
            for row in rows:
                month_data = {
                    'month': row.month.strftime('%Y-%m') if row.month else None,
                    'monthly_revenue': float(row.monthly_revenue or 0),
                    'net_revenue': float(row.net_revenue or 0),
                    'invoice_count': int(row.invoice_count or 0),
                    'avg_invoice_value': float(row.avg_invoice_value or 0),
                    'total_taxes': float(row.total_taxes or 0),
                    'mom_growth_percent': float(row.mom_growth_percent or 0),
                    'growth_category': row.growth_category
                }
                monthly_data.append(month_data)
                
                # Current month is first row (most recent)
                if len(monthly_data) == 1:
                    current_month_revenue = month_data['monthly_revenue']
                
                total_revenue_ytd += month_data['monthly_revenue']
            
            # Calculate additional metrics
            avg_monthly_revenue = total_revenue_ytd / len(monthly_data) if monthly_data else 0
            
            return {
                'kpi_id': 'receita_total_mensal',
                'title': 'Receita Total Mensal',
                'currentValue': current_month_revenue,
                'formattedValue': f"R$ {current_month_revenue:,.2f}",
                'format': 'currency',
                'source': 'hybrid_calculation',
                'metadata': {
                    'calculation_method': 'real_schema_conta_receber',
                    'total_revenue_ytd': total_revenue_ytd,
                    'avg_monthly_revenue': avg_monthly_revenue,
                    'months_analyzed': len(monthly_data),
                    'current_month_growth': monthly_data[0]['mom_growth_percent'] if monthly_data else 0,
                    'growth_trend': monthly_data[0]['growth_category'] if monthly_data else 'UNKNOWN'
                },
                'chartData': {
                    'type': 'line',
                    'data': monthly_data,
                    'xAxis': 'month',
                    'yAxis': 'monthly_revenue',
                    'title': 'Evolução da Receita Mensal'
                },
                'timeframe': timeframe,
                'currency': currency,
                'calculation_time': time.time() - start_time
            }
            
    except Exception as e:
        logger.error(f"❌ Error calculating monthly revenue: {e}")
        return {
            'error': 'calculation_failed',
            'message': f'Failed to calculate monthly revenue: {str(e)}',
            'kpi_id': 'receita_total_mensal',
            'timeframe': timeframe
        }
```

---

## 🎯 **Step 2: Add to Profile Configuration**

### **File**: `apps/backend/src/config/profile_kpi_mapping.py` (NEW FILE)

```python
"""
Profile-specific KPI mappings for DataHero4.
Extends existing architecture without refactoring.
"""

# CFO Profile KPIs (extend existing)
CFO_PROFILE_KPIS = [
    # EXISTING
    'margem_liquida_operacional',
    
    # NEW Phase 1 additions
    'receita_total_mensal',
    'margem_bruta_por_produto',
    'ebitda_mensal',
    'custo_operacional_por_transacao',
    'auditoria_compliance_score',
]

# Add to SmartQueryRouter critical KPIs
CFO_CRITICAL_KPIS = [
    'margem_liquida_operacional',
    'receita_total_mensal',  # NEW - high priority for CFO
]
```

---

## 🧪 **Step 3: Add Test Validation**

### **File**: `apps/backend/tests/test_profile_personalization_api.py` (EXTEND EXISTING)

```python
# ADD to existing test file (after line ~170)

def test_cfo_monthly_revenue_kpi(self, kpi_calculator):
    """Test CFO monthly revenue KPI calculation."""
    
    payload = {
        "user_id": "test_cfo_revenue",
        "profile_type": "CFO", 
        "timeframe": "month",
        "currency": "all",
        "priority_only": False
    }
    
    response = requests.post(API_ENDPOINT, json=payload, timeout=30)
    assert response.status_code == 200
    
    data = response.json()
    kpis = data["kpis"]
    
    # Find monthly revenue KPI
    revenue_kpi = next((kpi for kpi in kpis if kpi["id"] == "receita_total_mensal"), None)
    assert revenue_kpi is not None, "CFO should have monthly revenue KPI"
    
    # Validate KPI structure
    assert revenue_kpi["currentValue"] is not None, "Revenue should have a value"
    assert revenue_kpi["format"] == "currency", "Revenue should be currency format"
    assert "metadata" in revenue_kpi, "Should include metadata"
    
    # Validate metadata
    metadata = revenue_kpi["metadata"]
    assert "total_revenue_ytd" in metadata, "Should include YTD revenue"
    assert "months_analyzed" in metadata, "Should include analysis period"
    assert "growth_trend" in metadata, "Should include growth analysis"
    
    # Validate chart data
    assert "chartData" in revenue_kpi, "Should include chart data"
    chart_data = revenue_kpi["chartData"]
    assert chart_data["type"] == "line", "Should be line chart"
    assert len(chart_data["data"]) > 0, "Should have monthly data points"
```

---

## 🚀 **Step 4: Integration Testing**

### **MCP Postgres Validation**

```sql
-- Test the exact query that will be used
WITH monthly_revenue AS (
    SELECT 
        DATE_TRUNC('month', data_emissao) as month,
        SUM(valor_total) as monthly_revenue,
        COUNT(*) as invoice_count
    FROM conta_receber 
    WHERE data_emissao >= DATE_TRUNC('year', CURRENT_DATE)
    GROUP BY DATE_TRUNC('month', data_emissao)
    ORDER BY month DESC
)
SELECT 
    month,
    monthly_revenue,
    invoice_count
FROM monthly_revenue
LIMIT 5;

-- Expected result: Monthly revenue data with real values
-- Validates: Query syntax, data availability, performance
```

---

## ✅ **Implementation Checklist**

- [ ] Add `_calculate_receita_total_mensal` method to HybridKpiService
- [ ] Update `kpi_calculators` mapping with new KPI
- [ ] Create profile mapping configuration
- [ ] Add test validation to existing test suite
- [ ] Validate query performance with MCP Postgres
- [ ] Test integration with PersonalizedCacheSystem TTL
- [ ] Verify SmartQueryRouter routing for CFO profile
- [ ] Deploy and monitor with existing observability system

**Result**: New KPI fully integrated with existing architecture, no refactoring required, leverages all existing caching, routing, and monitoring infrastructure.
