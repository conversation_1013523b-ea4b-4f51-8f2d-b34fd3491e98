const { chromium } = require('playwright');

async function testFrontend() {
  console.log('🚀 Starting Playwright test of DataHero4 frontend...');
  
  const browser = await chromium.launch({ headless: false }); // headless: false para ver o que acontece
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Interceptar requisições de rede para debug
    page.on('request', request => {
      if (request.url().includes('ask')) {
        console.log('📡 API Request:', request.url(), request.method());
        console.log('📦 Request body:', request.postData());
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('ask')) {
        console.log('📨 API Response:', response.status(), response.url());
      }
    });
    
    // Interceptar console logs da página
    page.on('console', msg => {
      console.log('🖥️ Browser console:', msg.text());
    });
    
    // Interceptar erros
    page.on('pageerror', error => {
      console.error('❌ Page error:', error.message);
    });
    
    console.log('📱 Navigating to frontend...');
    await page.goto('https://frontend-production-324f.up.railway.app');
    
    // Aguardar a página carregar
    console.log('⏳ Waiting for page to load...');
    await page.waitForLoadState('networkidle');
    
    // Verificar se a página carregou corretamente
    try {
      const title = await page.textContent('h2', { timeout: 5000 });
      console.log('📄 Page title found:', title);
    } catch (e) {
      console.log('⚠️ No h2 found, trying different selectors...');
      try {
        const h1 = await page.textContent('h1', { timeout: 3000 });
        console.log('📄 H1 found:', h1);
      } catch (e2) {
        console.log('⚠️ No h1 found either, checking page content...');
        const bodyText = await page.textContent('body');
        console.log('📄 Body text (first 200 chars):', bodyText.substring(0, 200));
      }
    }
    
    // Procurar pelo input field
    console.log('🔍 Looking for input field...');
    const inputSelector = 'input[type="text"]';
    await page.waitForSelector(inputSelector, { timeout: 10000 });
    console.log('✅ Input field found!');
    
    // Verificar placeholder
    const placeholder = await page.getAttribute(inputSelector, 'placeholder');
    console.log('📝 Input placeholder:', placeholder);
    
    // Digitar a pergunta
    console.log('⌨️ Typing question...');
    await page.fill(inputSelector, 'vendas em 2023');
    
    // Aguardar um pouco para ver o que acontece
    await page.waitForTimeout(1000);
    
    // Verificar se o botão está habilitado
    const buttonSelector = 'button[type="submit"]';
    const isButtonEnabled = await page.isEnabled(buttonSelector);
    console.log('🔘 Submit button enabled:', isButtonEnabled);
    
    // Fazer screenshot antes de submeter
    await page.screenshot({ path: 'frontend_before_submit.png' });
    console.log('📸 Screenshot taken: frontend_before_submit.png');
    
    // Submeter o form
    console.log('🚀 Submitting form...');
    await page.click(buttonSelector);
    
    // Aguardar por possível navegação ou mudança
    console.log('⏳ Waiting for navigation or changes...');
    
    try {
      // Aguardar navegação para chat ou mudança na página
      await page.waitForURL('**/chat*', { timeout: 5000 });
      console.log('✅ Navigated to chat page!');
    } catch (e) {
      console.log('⚠️ No navigation detected, checking current page...');
    }
    
    // Aguardar um pouco para ver se algo acontece
    await page.waitForTimeout(3000);
    
    // Fazer screenshot final
    await page.screenshot({ path: 'frontend_after_submit.png' });
    console.log('📸 Screenshot taken: frontend_after_submit.png');
    
    // Verificar URL atual
    const currentURL = page.url();
    console.log('📍 Current URL:', currentURL);
    
    // Verificar se há mensagens de chat ou erro
    try {
      await page.waitForSelector('.text-gray-600', { timeout: 3000 });
      const loadingText = await page.textContent('.text-gray-600');
      console.log('📱 Loading text found:', loadingText);
    } catch (e) {
      console.log('⚠️ No loading text found');
    }
    
    // Aguardar mais um pouco para ver o resultado
    await page.waitForTimeout(5000);
    
    // Screenshot final
    await page.screenshot({ path: 'frontend_final.png' });
    console.log('📸 Final screenshot taken: frontend_final.png');
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'frontend_error.png' });
    console.log('📸 Error screenshot taken: frontend_error.png');
  } finally {
    await browser.close();
  }
}

testFrontend().catch(console.error);