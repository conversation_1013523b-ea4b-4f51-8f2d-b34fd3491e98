# DataHero4 Backend Chat Pipeline - Comprehensive Test Plan

## Problem Statement

**Issue**: Valid business questions like "Teste final: quantas operações temos no total?" are returning empty responses instead of actual data analysis.

**Evidence from Logs**:
```
INFO (query_generator): Pergunta vazia recebida. Pulando geração de SQL.
ERROR: Query generation failed: Query generation failed
WARNING: Sem resultados para análise de negócio
```

**Root Cause Hypothesis**: The question is not being properly passed through the LangGraph workflow, causing the query generator to receive an empty question.

## Test Environment Setup

### Prerequisites
- Backend running on localhost:8000
- Frontend running on localhost:3000  
- Supabase databases connected (client + learning)
- LangGraph workflow initialized
- Real-time log monitoring active

### Test Data Requirements
- Valid business questions in Portuguese
- Known database schema (L2M cambio operations)
- Expected SQL queries for comparison
- Expected business analysis responses

## 1. LangGraph Workflow State Analysis

### 1.1 Initial State Creation Testing
**Objective**: Verify that user questions are properly captured in initial state

**Test Cases**:
```python
# Test 1: Basic question capture
question = "Quantas operações temos no total?"
initial_state = create_initial_state(question)
assert initial_state["question"] == question
assert initial_state["user_question"] == question

# Test 2: Complex business question
question = "Qual foi o volume total de vendas em EUR no primeiro trimestre de 2023?"
initial_state = create_initial_state(question)
verify_question_preservation(initial_state, question)

# Test 3: Special characters and accents
question = "Análise de operações com símbolos: €, $, £"
initial_state = create_initial_state(question)
verify_encoding_preservation(initial_state, question)
```

**Expected Results**:
- Question properly stored in state
- No encoding issues with Portuguese characters
- All required state fields initialized

**Failure Indicators**:
- Empty question field in state
- Encoding corruption
- Missing required state fields

### 1.2 State Propagation Through Workflow
**Objective**: Ensure question data flows correctly through all workflow nodes

**Test Method**:
1. Add debug logging to each workflow node
2. Track question field through entire pipeline
3. Identify where question gets lost or corrupted

**Nodes to Monitor**:
- `parallel_context_preparation_node`
- `feedback_analysis_node` 
- `context_enhancement_node`
- `query_generation_node`
- `business_analysis_node`

**Debug Points**:
```python
# Add to each node
logger.info(f"🔍 NODE DEBUG - Question: '{state.get('question', 'MISSING')}'")
logger.info(f"🔍 NODE DEBUG - User Question: '{state.get('user_question', 'MISSING')}'")
logger.info(f"🔍 NODE DEBUG - State keys: {list(state.keys())}")
```

## 2. Query Generation Node Deep Testing

### 2.1 Input Validation Testing
**Objective**: Verify query generator receives valid input

**Test Cases**:
```python
# Test 1: Direct node testing
from src.nodes.query_generation_node import query_generation_node

test_state = {
    "question": "Quantas operações temos no total?",
    "user_question": "Quantas operações temos no total?",
    "enhanced_context": "Valid context...",
    "client_id": "L2M",
    "sector": "cambio"
}

result = query_generation_node(test_state)
assert result["sql_query"] is not None
assert "SELECT" in result["sql_query"]
```

**Expected Results**:
- SQL query generated successfully
- Query contains valid SQL syntax
- Query targets correct tables

**Failure Indicators**:
- "Pergunta vazia recebida" message
- No SQL query generated
- Invalid SQL syntax

### 2.2 Query Generator Agent Testing
**Objective**: Test the underlying query generator agent directly

**Test Method**:
```python
# Direct agent testing
from src.agents.query_generator import QueryGeneratorAgent

agent = QueryGeneratorAgent()
result = agent.generate_query(
    question="Quantas operações temos no total?",
    context="Database schema and business context...",
    client_id="L2M"
)

assert result.sql_query is not None
assert result.confidence > 0.5
```

### 2.3 Context Enhancement Validation
**Objective**: Ensure query generator receives proper context

**Test Cases**:
- Verify enhanced_context contains schema information
- Verify business context includes L2M sector details
- Verify context token count is reasonable (>1000 tokens)

## 3. Database Connectivity and Query Execution Testing

### 3.1 Direct Database Testing
**Objective**: Verify database connections and query execution

**Test Cases**:
```python
# Test 1: Client database connection
from src.utils.supabase_client import get_supabase_client

client = get_supabase_client()
result = client.table("operations").select("count").execute()
assert result.data is not None

# Test 2: Learning database connection  
from src.utils.learning_db_utils import LearningDBManager

learning_db = LearningDBManager()
result = learning_db.execute_query("SELECT COUNT(*) FROM query_history")
assert result is not None

# Test 3: Schema validation
tables = client.get_schema()
assert "operations" in tables
assert "clients" in tables
```

### 3.2 Query Execution Testing
**Objective**: Test actual SQL query execution against client data

**Test Cases**:
```sql
-- Test 1: Basic count query
SELECT COUNT(*) as total_operations FROM operations;

-- Test 2: Volume aggregation
SELECT SUM(volume_usd) as total_volume FROM operations;

-- Test 3: Date filtering
SELECT COUNT(*) FROM operations 
WHERE created_at >= '2023-01-01' AND created_at < '2024-01-01';
```

**Expected Results**:
- Queries execute without errors
- Results contain actual data
- Performance is acceptable (<5s)

## 4. Context Enhancement Node Testing

### 4.1 Schema Context Building
**Objective**: Verify schema information is properly built

**Test Method**:
```python
from src.nodes.context_enhancement_node import enhance_context_node

test_state = {
    "client_id": "L2M",
    "sector": "cambio",
    "question": "Quantas operações temos no total?"
}

result = enhance_context_node(test_state)
context = result["enhanced_context"]

# Validations
assert "operations" in context
assert "clients" in context  
assert "volume" in context
assert len(context) > 1000  # Minimum context size
```

### 4.2 Business Context Integration
**Objective**: Ensure business-specific context is included

**Validations**:
- L2M sector information present
- Cambio business rules included
- Relevant table relationships described
- Sample queries provided

## 5. Business Analysis Node Testing

### 5.1 Response Generation Testing
**Objective**: Verify business analysis generates proper responses

**Test Cases**:
```python
from src.nodes.business_analysis_node import business_analysis_node

test_state = {
    "sql_query": "SELECT COUNT(*) as total FROM operations",
    "query_results": [{"total": 1250}],
    "question": "Quantas operações temos no total?",
    "client_id": "L2M"
}

result = business_analysis_node(test_state)
assert result["final_answer"] is not None
assert "1250" in result["final_answer"]
assert len(result["final_answer"]) > 50  # Meaningful response
```

### 5.2 Empty Results Handling
**Objective**: Test behavior when query returns no results

**Test Cases**:
- Empty result set
- Null values
- Query execution errors

## 6. End-to-End Pipeline Testing

### 6.1 Complete Workflow Testing
**Objective**: Test entire pipeline with known good inputs

**Test Method**:
```python
# Complete pipeline test
from src.services.gemini_chat_engine import GeminiChatEngine

engine = GeminiChatEngine()
result = await engine._execute_workflow(initial_state)

# Validations
assert result["final_answer"] is not None
assert result["sql_query"] is not None
assert result["processing_time"] > 0
assert result["tokens_used"] > 0
```

### 6.2 Performance Benchmarking
**Objective**: Measure pipeline performance

**Metrics to Track**:
- Total processing time
- Time per workflow node
- Database query execution time
- LLM API call latency
- Memory usage

**Performance Targets**:
- Total pipeline: <10s
- Query generation: <3s
- Database execution: <2s
- Business analysis: <5s

## 7. Error Handling and Edge Cases

### 7.1 Invalid Question Handling
**Test Cases**:
- Empty strings
- Non-business questions
- Questions in other languages
- Very long questions (>1000 chars)

### 7.2 Database Error Handling
**Test Cases**:
- Connection timeouts
- Invalid SQL queries
- Permission errors
- Large result sets

### 7.3 LLM API Error Handling
**Test Cases**:
- API rate limits
- Network timeouts
- Invalid responses
- Token limit exceeded

## 8. Debugging and Monitoring

### 8.1 Enhanced Logging
**Implementation**:
```python
# Add comprehensive logging to each node
import logging
import json

def log_state_debug(node_name: str, state: dict, stage: str = "input"):
    logger.info(f"🔍 {node_name.upper()} {stage.upper()} DEBUG")
    logger.info(f"   Question: '{state.get('question', 'MISSING')}'")
    logger.info(f"   User Question: '{state.get('user_question', 'MISSING')}'")
    logger.info(f"   State Keys: {list(state.keys())}")
    logger.info(f"   State Size: {len(json.dumps(state, default=str))} chars")
```

### 8.2 Performance Monitoring
**Metrics Collection**:
- Node execution times
- Memory usage per node
- Database query performance
- LLM token usage

### 8.3 Error Tracking
**Error Categories**:
- Workflow execution errors
- Database connectivity errors
- LLM API errors
- Data validation errors

## 9. Test Execution Strategy

### Phase 1: Individual Node Testing (Priority 1)
1. Test query generation node in isolation
2. Test context enhancement node
3. Test business analysis node
4. Verify database connectivity

### Phase 2: Workflow Integration Testing (Priority 2)
1. Test state propagation through workflow
2. Test complete pipeline execution
3. Performance benchmarking
4. Error handling validation

### Phase 3: Production Scenario Testing (Priority 3)
1. Real business questions testing
2. Load testing with multiple concurrent requests
3. Edge case and error scenario testing
4. Performance optimization

## 10. Success Criteria

### Functional Requirements
- ✅ Valid business questions generate SQL queries
- ✅ SQL queries execute successfully against database
- ✅ Query results generate meaningful business analysis
- ✅ Complete pipeline executes without errors

### Performance Requirements
- ✅ Total pipeline execution <10 seconds
- ✅ Query generation <3 seconds
- ✅ Database query execution <2 seconds
- ✅ Business analysis generation <5 seconds

### Quality Requirements
- ✅ Generated SQL is syntactically correct
- ✅ Business analysis is relevant and accurate
- ✅ Error handling is graceful and informative
- ✅ Logging provides sufficient debugging information

## 11. Expected Findings

Based on the current logs, we expect to find:

1. **Question Loss**: The user question is being lost somewhere in the workflow state
2. **Context Issues**: Enhanced context may not be properly built
3. **Agent Communication**: Query generator agent may not be receiving proper inputs
4. **State Management**: LangGraph state management may have issues with TypedDict

## 12. Immediate Action Items

1. Add comprehensive debug logging to all workflow nodes
2. Test query generation node in isolation
3. Verify initial state creation and question preservation
4. Test database connectivity and schema access
5. Validate context enhancement process
