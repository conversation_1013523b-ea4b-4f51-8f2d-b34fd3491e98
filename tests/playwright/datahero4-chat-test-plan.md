# DataHero4 Chat Interface - Comprehensive Playwright Test Plan

## Overview
This comprehensive test plan covers the entire DataHero4 chat interface pipeline using Playwright MCP, including UI components, chat functionality, backend monitoring, performance testing, and integration testing.

## Test Environment Setup
- **Frontend URL**: http://localhost:3000/chat
- **Backend URL**: http://localhost:8000
- **Development Script**: ./scripts/dev.sh (runs both frontend and backend)
- **Browser**: Playwright (Chromium/Firefox/Safari)
- **Test Framework**: Playwright MCP

## 1. UI Component Testing Suite

### 1.1 Navigation and Layout
- [x] **Header Navigation**
  - Verify "DataHero4 Chat" title is displayed
  - Test "Perguntas" button functionality
  - Test "Dashboard" button functionality
  - Verify responsive layout on different screen sizes

- [x] **Sidebar Components**
  - Test "Nova Conversa" button (enabled/disabled states)
  - Test conversation search functionality
  - Verify conversation list display
  - Test conversation selection and switching
  - Test conversation deletion functionality

### 1.2 Chat Interface Elements
- [x] **Welcome Screen**
  - Verify welcome message display
  - Test example suggestion cards
  - Verify feature highlights section
  - Test quick action buttons

- [x] **Chat Input Area**
  - Test text input field functionality
  - Verify placeholder text
  - Test character limit handling
  - Test input validation
  - Test send button states (enabled/disabled)
  - Test keyboard shortcuts (Enter to send, Shift+Enter for new line)

### 1.3 Message Display
- [ ] **Message Bubbles**
  - Test user message display
  - Test assistant message display
  - Verify timestamp formatting
  - Test message status indicators

- [ ] **Content Cards**
  - Test chart visualization cards
  - Test SQL query display cards
  - Test table data cards
  - Test card expansion/collapse functionality

### 1.4 Interactive Elements
- [ ] **Buttons and Controls**
  - Test all button hover states
  - Test button click feedback
  - Test loading states
  - Test disabled states
  - Verify accessibility attributes

- [ ] **Feedback System**
  - Test thumbs up/down buttons
  - Test feedback modal functionality
  - Test feedback submission
  - Verify feedback confirmation

### 1.5 Responsive Design
- [ ] **Mobile Responsiveness**
  - Test layout on mobile devices (320px-768px)
  - Verify touch interactions
  - Test sidebar collapse on mobile
  - Test input field behavior on mobile keyboards

- [ ] **Tablet Responsiveness**
  - Test layout on tablet devices (768px-1024px)
  - Verify touch and mouse interactions
  - Test sidebar behavior

- [ ] **Desktop Responsiveness**
  - Test layout on desktop (1024px+)
  - Verify keyboard navigation
  - Test window resizing behavior

### 1.6 Accessibility Testing
- [ ] **Keyboard Navigation**
  - Test tab order through interface
  - Test keyboard shortcuts
  - Verify focus indicators
  - Test screen reader compatibility

- [ ] **ARIA Attributes**
  - Verify proper ARIA labels
  - Test role attributes
  - Verify live regions for dynamic content
  - Test high contrast mode compatibility

## 2. Chat Functionality Testing Suite

### 2.1 Basic Chat Operations
- [ ] **Message Sending**
  - Test simple text message sending
  - Test message with special characters
  - Test very long messages (>2000 characters)
  - Test empty message handling
  - Test rapid message sending

### 2.2 Business Question Types
- [ ] **Simple Questions (Cache Hit Scenarios)**
  - "Quantas operações tivemos em 2023?"
  - "Qual o maior cliente em USD?"
  - "Mostre as operações de janeiro de 2023"
  - "Total de vendas em reais no último ano"

- [ ] **Complex Questions (Cache Miss Scenarios)**
  - "Compare vendas de EUR vs USD por trimestre em 2023"
  - "Quais clientes operaram tanto EUR quanto USD e qual a diferença de volume?"
  - "Análise de sazonalidade: em que meses vendemos mais EUR?"
  - "Correlação entre volume de operações e volatilidade do mercado"

### 2.3 Edge Cases
- [ ] **Invalid Queries**
  - Test queries with no data available
  - Test malformed business questions
  - Test queries outside business domain
  - Test SQL injection attempts

- [ ] **Special Characters and Formats**
  - Test queries with emojis
  - Test queries with numbers and currencies
  - Test queries with dates in different formats
  - Test queries with special symbols

### 2.4 Two-Phase Response System
- [ ] **Immediate Response (Phase 1)**
  - Verify response time <3 seconds
  - Test direct answer display
  - Verify loading indicators
  - Test response formatting

- [ ] **Detailed Analysis (Phase 2)**
  - Test delayed detailed analysis
  - Verify business intelligence insights
  - Test chart generation
  - Test table data display

## 3. Backend Pipeline Monitoring Tests

### 3.1 LangGraph Workflow Monitoring
- [ ] **Node Execution Tracking**
  - Monitor domain extraction node
  - Monitor cache lookup node
  - Monitor context enhancement node
  - Monitor query generation node
  - Monitor validation node
  - Monitor execution node
  - Monitor learning node
  - Monitor presentation node

### 3.2 Backend Log Analysis
- [ ] **Real-time Log Monitoring**
  - Monitor backend terminal output during frontend interactions
  - Track API request/response cycles
  - Monitor database query execution
  - Track error handling and recovery

### 3.3 Cache Behavior Verification
- [ ] **Cache Hit/Miss Patterns**
  - Verify semantic similarity matching
  - Test cache invalidation scenarios
  - Monitor cache performance metrics
  - Test cache storage in learning database

## 4. Performance Testing Suite

### 4.1 Response Time Measurements
- [ ] **Cached Query Performance**
  - Measure response time for cache hits (<3s target)
  - Test multiple cached queries in sequence
  - Monitor memory usage during cached responses

- [ ] **Fresh Query Performance**
  - Measure response time for cache misses
  - Test complex analytical queries
  - Monitor CPU usage during processing

### 4.2 Concurrent User Testing
- [ ] **Multiple Browser Sessions**
  - Test 5 concurrent users
  - Test 10 concurrent users
  - Monitor system performance degradation
  - Test session isolation

### 4.3 Load Testing
- [ ] **High Volume Scenarios**
  - Test rapid message sending
  - Test large result set handling
  - Test memory leak detection
  - Test system recovery after load

## 5. Integration Testing Suite

### 5.1 End-to-End Workflow
- [ ] **Complete User Journey**
  - User login → Chat interface → Question → Response → Feedback
  - Test data flow through entire pipeline
  - Verify data persistence
  - Test session management

### 5.2 Database Integration
- [ ] **Supabase Connectivity**
  - Test client database queries
  - Test learning database operations
  - Verify data consistency
  - Test connection recovery

### 5.3 API Integration
- [ ] **Backend API Testing**
  - Test chat API endpoints
  - Test streaming response handling
  - Test error response handling
  - Test API rate limiting

## 6. Test Data Requirements

### 6.1 Existing Data Utilization
- [ ] **Query History Analysis**
  - Use existing cached queries from query_history table
  - Test with real client data patterns
  - Verify semantic caching with historical data

### 6.2 Test Scenarios by Business Sector
- [ ] **Cambio Sector (L2M)**
  - Currency exchange operations
  - Volume analysis queries
  - Client performance metrics
  - Regulatory compliance queries

### 6.3 Synthetic Test Data
- [ ] **Generated Test Cases**
  - Edge case scenarios
  - Performance stress tests
  - Error condition simulations
  - Boundary value testing

## Test Execution Strategy

### Phase 1: Development Mode Testing
1. Start environment with `./scripts/dev.sh`
2. Monitor backend logs in real-time
3. Execute UI component tests
4. Execute basic chat functionality tests
5. Monitor performance metrics

### Phase 2: Integration Testing
1. Execute end-to-end workflows
2. Test database connectivity
3. Verify semantic caching
4. Test error handling scenarios

### Phase 3: Performance and Load Testing
1. Execute performance benchmarks
2. Run concurrent user tests
3. Monitor system resources
4. Validate response time targets

## Success Criteria
- All UI components function correctly across devices
- Response times meet <3s target for immediate responses
- Cache hit rate >70% for similar queries
- Zero data loss during normal operations
- Graceful error handling for all edge cases
- Accessibility compliance (WCAG 2.1 AA)
- Cross-browser compatibility (Chrome, Firefox, Safari)
