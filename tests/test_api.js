const https = require('https');

function testAPI() {
  console.log('🚀 Testing API call exactly like frontend...');
  
  const postData = JSON.stringify({
    question: 'vendas em 2023',
    client_id: 'L2M',
    sector: 'cambio'
  });

  const options = {
    hostname: 'backend-production-9857.up.railway.app',
    port: 443,
    path: '/ask',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = https.request(options, (res) => {
    console.log('📡 Response status:', res.statusCode);
    console.log('📡 Response headers:', res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const jsonData = JSON.parse(data);
          console.log('✅ Success response:', JSON.stringify(jsonData, null, 2));
        } catch (e) {
          console.log('✅ Raw response:', data);
        }
      } else {
        console.error('❌ Error response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.write(postData);
  req.end();
}

testAPI();