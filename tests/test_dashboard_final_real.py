#!/usr/bin/env python3
"""
Teste final do dashboard com dados reais.
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.append('/Users/<USER>/Coding/datahero4/apps/backend/src')

from services.kpi_calculator import KPICalculator
from services.dashboard_service import DashboardService

async def test_dashboard_complete():
    """Teste completo do dashboard com dados reais."""
    print("🚀 Iniciando teste do dashboard com dados reais...")
    
    # Initialize services
    kpi_calculator = KPICalculator()
    dashboard_service = DashboardService()
    
    # Test parameters
    client_id = "L2M"  # Will be mapped to 334
    sector = "cambio"
    period = "current_month"
    
    print(f"📊 Testando KPIs para cliente: {client_id} ({sector})")
    
    # Test individual priority KPIs
    priority_kpis = ["total_volume", "average_spread", "average_ticket", "growth_percentage"]
    
    print("\n🔍 Testando KPIs individuais:")
    kpi_results = {}
    
    for kpi_id in priority_kpis:
        try:
            print(f"  📈 Calculando {kpi_id}...")
            result = await kpi_calculator.calculate_kpi(
                kpi_id=kpi_id,
                client_id=client_id,
                sector=sector,
                period=period
            )
            
            if result:
                kpi_results[kpi_id] = {
                    "name": result.name,
                    "current_value": result.current_value,
                    "formatted_value": result.formatted_value,
                    "unit": result.unit,
                    "change_percentage": result.change_percentage,
                    "change_direction": result.change_direction
                }
                print(f"     ✅ {result.name}: {result.formatted_value}")
            else:
                print(f"     ❌ Falha ao calcular {kpi_id}")
                kpi_results[kpi_id] = None
                
        except Exception as e:
            print(f"     ❌ Erro no KPI {kpi_id}: {e}")
            kpi_results[kpi_id] = {"error": str(e)}
    
    # Test full dashboard build
    print("\n🏗️ Testando construção completa do dashboard...")
    try:
        dashboard = await dashboard_service.build_dashboard(
            client_id=client_id,
            sector=sector,
            period=period,
            include_priority_only=False,
            include_alerts=True
        )
        
        print(f"     ✅ Dashboard construído com sucesso!")
        print(f"     📊 KPIs prioritários: {len(dashboard.priority_kpis)}")
        print(f"     📈 KPIs regulares: {len(dashboard.regular_kpis)}")
        print(f"     🚨 Alertas: {len(dashboard.alerts)}")
        print(f"     📋 Score de saúde: {dashboard.summary['health_score']}")
        
        dashboard_summary = {
            "priority_kpis_count": len(dashboard.priority_kpis),
            "regular_kpis_count": len(dashboard.regular_kpis),
            "alerts_count": len(dashboard.alerts),
            "health_score": dashboard.summary.get('health_score'),
            "health_status": dashboard.summary.get('health_status')
        }
        
    except Exception as e:
        print(f"     ❌ Erro na construção do dashboard: {e}")
        dashboard_summary = {"error": str(e)}
    
    # Test natural language editing
    print("\n🗣️ Testando edição por linguagem natural...")
    try:
        edit_result = await dashboard_service.process_natural_language_edit(
            command="Change the total volume chart to a bar chart",
            client_id=client_id,
            sector=sector
        )
        
        if edit_result.success:
            print(f"     ✅ Comando processado: {edit_result.message}")
            print(f"     🔧 Mudanças aplicadas: {len(edit_result.changes_applied)}")
        else:
            print(f"     ⚠️ Comando não processado: {edit_result.message}")
            
        natural_language_test = {
            "success": edit_result.success,
            "message": edit_result.message,
            "changes_count": len(edit_result.changes_applied)
        }
        
    except Exception as e:
        print(f"     ❌ Erro no teste de linguagem natural: {e}")
        natural_language_test = {"error": str(e)}
    
    # Compile final results
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "test_parameters": {
            "client_id": client_id,
            "sector": sector,
            "period": period
        },
        "individual_kpis": kpi_results,
        "dashboard_build": dashboard_summary,
        "natural_language_edit": natural_language_test,
        "overall_status": "SUCCESS" if all([
            any(kpi_results.values()),
            "error" not in dashboard_summary,
            "error" not in natural_language_test
        ]) else "PARTIAL_SUCCESS"
    }
    
    # Save results
    with open("/Users/<USER>/Coding/datahero4/dashboard_test_results.json", "w") as f:
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\n🎯 Teste concluído! Status: {test_results['overall_status']}")
    print(f"📄 Resultados salvos em: dashboard_test_results.json")
    
    return test_results

if __name__ == "__main__":
    results = asyncio.run(test_dashboard_complete())
    
    print("\n" + "="*60)
    print("📋 RESUMO FINAL DOS TESTES")
    print("="*60)
    
    # Print summary
    successful_kpis = sum(1 for kpi in results['individual_kpis'].values() 
                         if kpi and 'error' not in kpi)
    total_kpis = len(results['individual_kpis'])
    
    print(f"KPIs individuais: {successful_kpis}/{total_kpis} sucessos")
    print(f"Dashboard completo: {'✅ OK' if 'error' not in results['dashboard_build'] else '❌ ERRO'}")
    print(f"Linguagem natural: {'✅ OK' if 'error' not in results['natural_language_edit'] else '❌ ERRO'}")
    print(f"Status geral: {results['overall_status']}")
    
    # Exit with appropriate code
    sys.exit(0 if results['overall_status'] == 'SUCCESS' else 1)