"""
Dashboard Filter Test Complete
=============================

Testa a performance do dashboard e verifica se valores
e gráficos são atualizados corretamente com filtros.
"""

import pytest
import time
import httpx
import asyncio
from typing import Dict, List, Any


API_BASE_URL = "http://localhost:8000"


class DashboardFilterTester:
    """Testa filtros do dashboard e performance."""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.results = []
    
    async def close(self):
        await self.client.aclose()
    
    async def test_filter_sync(self, timeframe: str, currency: str) -> Dict[str, Any]:
        """Testa se valores e gráficos estão sincronizados com filtros."""
        start_time = time.time()
        
        # Fazer chamada API
        response = await self.client.get(
            f"{API_BASE_URL}/api/dashboard/kpis",
            params={
                "timeframe": timeframe,
                "currency": currency,
                "priority_only": True
            }
        )
        
        elapsed = (time.time() - start_time) * 1000
        
        if response.status_code != 200:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}",
                "elapsed_ms": elapsed
            }
        
        data = response.json()
        kpis = data.get("kpis", [])
        
        # Verificar cada KPI
        issues = []
        for kpi in kpis:
            kpi_id = kpi.get("id")
            
            # Verificar se tem valor atual
            if kpi.get("currentValue") is None:
                issues.append(f"{kpi_id}: currentValue is None")
            
            # Verificar se tem changePercent (pode ser None se não há dados anteriores)
            if "changePercent" not in kpi:
                issues.append(f"{kpi_id}: changePercent missing")
            
            # Verificar se tem dados do gráfico
            chart_data = kpi.get("chartData", [])
            if not chart_data:
                issues.append(f"{kpi_id}: chartData is empty")
            
            # Verificar se gráfico corresponde ao timeframe
            if chart_data and timeframe == "1d" and len(chart_data) > 24:
                issues.append(f"{kpi_id}: too many data points for 1d")
            elif chart_data and timeframe == "week" and len(chart_data) > 7:
                issues.append(f"{kpi_id}: too many data points for week")
        
        return {
            "success": len(issues) == 0,
            "timeframe": timeframe,
            "currency": currency,
            "elapsed_ms": elapsed,
            "kpi_count": len(kpis),
            "issues": issues,
            "cache_source": data.get("cache_source", "unknown"),
            "processing_time_ms": data.get("processing_time_ms", 0)
        }
    
    async def test_performance_targets(self) -> Dict[str, Any]:
        """Testa se o dashboard atende aos targets de performance."""
        targets = {
            "cache_hit": 500,    # Cache hit deve ser < 500ms
            "cache_miss": 3000,  # Cache miss deve ser < 3s
            "max_allowed": 5000  # Máximo permitido é 5s
        }
        
        results = []
        
        # Testar diferentes combinações
        test_cases = [
            ("week", "all"),     # Caso padrão
            ("month", "usd"),    # Filtro comum
            ("1d", "eur"),       # Timeframe curto
            ("quarter", "gbp"),  # Timeframe longo
        ]
        
        for timeframe, currency in test_cases:
            # Primeira chamada (cache miss)
            result1 = await self.test_filter_sync(timeframe, currency)
            results.append({**result1, "call": "first"})
            
            # Aguardar um pouco
            await asyncio.sleep(0.5)
            
            # Segunda chamada (cache hit)
            result2 = await self.test_filter_sync(timeframe, currency)
            results.append({**result2, "call": "second"})
        
        # Análise de performance
        cache_hits = [r for r in results if r["call"] == "second" and r["success"]]
        cache_misses = [r for r in results if r["call"] == "first" and r["success"]]
        
        avg_cache_hit = sum(r["elapsed_ms"] for r in cache_hits) / len(cache_hits) if cache_hits else 0
        avg_cache_miss = sum(r["elapsed_ms"] for r in cache_misses) / len(cache_misses) if cache_misses else 0
        max_time = max(r["elapsed_ms"] for r in results if r["success"])
        
        performance_ok = (
            avg_cache_hit < targets["cache_hit"] and
            avg_cache_miss < targets["cache_miss"] and
            max_time < targets["max_allowed"]
        )
        
        return {
            "performance_ok": performance_ok,
            "avg_cache_hit_ms": round(avg_cache_hit, 2),
            "avg_cache_miss_ms": round(avg_cache_miss, 2),
            "max_time_ms": round(max_time, 2),
            "targets": targets,
            "detailed_results": results
        }


async def main():
    """Executa os testes."""
    tester = DashboardFilterTester()
    
    try:
        print("\n🧪 TESTANDO DASHBOARD - FILTROS E PERFORMANCE\n")
        print("=" * 60)
        
        # 1. Teste de sincronização de filtros
        print("\n1️⃣ TESTE DE SINCRONIZAÇÃO DE FILTROS")
        print("-" * 40)
        
        for timeframe in ["1d", "week", "month"]:
            for currency in ["all", "usd"]:
                result = await tester.test_filter_sync(timeframe, currency)
                
                status = "✅ OK" if result["success"] else "❌ FALHOU"
                print(f"\n{status} Filtros: timeframe={timeframe}, currency={currency}")
                print(f"   Tempo: {result['elapsed_ms']:.2f}ms (Processing: {result['processing_time_ms']}ms)")
                print(f"   Cache: {result['cache_source']}")
                print(f"   KPIs: {result['kpi_count']}")
                
                if result["issues"]:
                    print("   ⚠️  Problemas encontrados:")
                    for issue in result["issues"]:
                        print(f"      - {issue}")
        
        # 2. Teste de performance
        print("\n\n2️⃣ TESTE DE PERFORMANCE")
        print("-" * 40)
        
        perf_result = await tester.test_performance_targets()
        
        print(f"\n📊 Resultados de Performance:")
        print(f"   Cache Hit médio: {perf_result['avg_cache_hit_ms']}ms (target < {perf_result['targets']['cache_hit']}ms)")
        print(f"   Cache Miss médio: {perf_result['avg_cache_miss_ms']}ms (target < {perf_result['targets']['cache_miss']}ms)")
        print(f"   Tempo máximo: {perf_result['max_time_ms']}ms (target < {perf_result['targets']['max_allowed']}ms)")
        
        if perf_result['performance_ok']:
            print("\n✅ Performance está dentro dos targets!")
        else:
            print("\n❌ Performance está FORA dos targets!")
        
        # 3. Análise detalhada
        print("\n\n3️⃣ ANÁLISE DETALHADA")
        print("-" * 40)
        
        # Agrupar por problema
        all_issues = []
        for result in perf_result['detailed_results']:
            if result.get('issues'):
                for issue in result['issues']:
                    all_issues.append(issue)
        
        if all_issues:
            print("\n⚠️  Resumo de problemas encontrados:")
            issue_counts = {}
            for issue in all_issues:
                kpi_id = issue.split(":")[0]
                problem = issue.split(":")[1].strip()
                key = f"{kpi_id} - {problem}"
                issue_counts[key] = issue_counts.get(key, 0) + 1
            
            for issue, count in sorted(issue_counts.items()):
                print(f"   - {issue}: {count}x")
        else:
            print("\n✅ Nenhum problema de sincronização encontrado!")
        
        # 4. Recomendações
        print("\n\n4️⃣ RECOMENDAÇÕES")
        print("-" * 40)
        
        if perf_result['avg_cache_miss_ms'] > 2000:
            print("⚡ Otimizar queries SQL - tempo de cálculo muito alto")
        
        if perf_result['avg_cache_hit_ms'] > 300:
            print("💾 Revisar sistema de cache - cache hits ainda lentos")
        
        if all_issues:
            if any("currentValue is None" in issue for issue in all_issues):
                print("🔧 Corrigir cálculo de valores atuais nos KPIs")
            
            if any("chartData is empty" in issue for issue in all_issues):
                print("📊 Implementar geração de dados de gráfico para todos os timeframes")
        
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main()) 