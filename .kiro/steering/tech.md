# Technology Stack & Build System

## Build System
- **Monorepo**: Turborepo for coordinated builds across apps
- **Package Manager**: npm (root) + Poetry (backend) + npm (frontend)
- **Node.js**: >=18.0.0 required

## Backend Stack (apps/backend/)
- **Framework**: FastAPI 0.115+ with async/await
- **AI/ML**: LangGraph + <PERSON><PERSON><PERSON><PERSON> for workflow orchestration
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Caching**: Redis with hierarchical L1/L2/L3 strategy
- **LLM Providers**: Together AI, Anthropic Claude, OpenAI, Google
- **Task Scheduling**: APScheduler for internal jobs
- **Dependency Management**: Poetry with Python 3.9-3.12

### Key Backend Dependencies
```toml
langgraph = "^0.2.0"
fastapi = "^0.100.0"
sqlalchemy = "^2.0.0"
redis = {extras = ["hiredis"], version = "^5.0.0"}
psycopg2-binary = "^2.9.9"
together = "^0.2.10"
anthropic = "^0.25.0"
```

## Frontend Stack (apps/frontend/)
- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite for ultra-fast development
- **UI Library**: shadcn/ui + Radix UI components
- **Styling**: Tailwind CSS with custom design system
- **Animations**: Motion.dev for fluid transitions
- **State Management**: Zustand + TanStack Query
- **Charts**: Recharts for KPI visualizations

### Key Frontend Dependencies
```json
"react": "^18.3.1"
"vite": "^5.4.1"
"motion": "^12.17.0"
"@tanstack/react-query": "^5.83.0"
"recharts": "^2.12.7"
"zustand": "^4.5.7"
```

## Common Commands

### Development
```bash
# Start full development environment
npm run dev

# Start individual services
npm run dev:backend    # FastAPI on :8000
npm run dev:frontend   # React on :3000

# Install dependencies
npm install            # Root dependencies
cd apps/backend && poetry install  # Backend deps
cd apps/frontend && npm install    # Frontend deps
```

### Building
```bash
# Build all apps
npm run build

# Build specific apps
npm run build:backend
npm run build:frontend
```

### Testing
```bash
# Run all tests
npm run test

# Backend tests (pytest)
npm run test:backend
cd apps/backend && poetry run pytest

# Frontend tests (vitest)
npm run test:frontend
```

### Production Deployment
```bash
# Railway deployment (auto from git push)
git push origin main

# Manual Railway deploy
railway up --service backend

# Local production preview
npm run deploy:backend   # FastAPI production
npm run deploy:frontend  # Vite preview
```

## Environment Configuration

### Backend (.env)
```env
# Database
DATABASE_URL=postgresql://...
LEARNING_DATABASE_URL=postgresql://...

# LLM APIs
TOGETHER_API_KEY=...
ANTHROPIC_API_KEY=...
GOOGLE_API_KEY=...

# Redis Cache
REDIS_URL=redis://...

# Snapshot System
SNAPSHOT_SCHEDULER_ENABLED=false  # Railway: false, Local: true
SNAPSHOT_SCHEDULE=0 2 * * *       # Daily at 2 AM
```

### Frontend (.env)
```env
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development
```

## Architecture Patterns

### Backend Patterns
- **LangGraph State Machines**: Workflow orchestration with specialized agents
- **Repository Pattern**: Data access abstraction
- **Dependency Injection**: FastAPI's built-in DI system
- **Async/Await**: Non-blocking I/O throughout
- **Hierarchical Caching**: Memory → Redis → Database

### Frontend Patterns
- **Component Composition**: Reusable UI components with shadcn/ui
- **Custom Hooks**: Shared logic extraction
- **State Management**: Zustand for global state, TanStack Query for server state
- **Error Boundaries**: Graceful error handling
- **Responsive Design**: Mobile-first with Tailwind CSS

## Performance Optimizations
- **Snapshot System**: Pre-calculated KPIs for 19ms dashboard loading
- **Connection Pooling**: Database and LLM API connections
- **Parallel Execution**: Independent workflow nodes run simultaneously
- **Smart Caching**: 85% cache hit rate with intelligent invalidation
- **Bundle Optimization**: Vite code splitting and tree shaking