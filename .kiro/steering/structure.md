# Project Structure & Organization

## Monorepo Layout

```
datahero4/
├── apps/                    # Application packages
│   ├── backend/            # FastAPI + LangGraph backend
│   └── frontend/           # React + Vite frontend
├── docs/                   # Project documentation
├── scripts/                # Development and deployment scripts
├── tests/                  # Cross-app integration tests
└── tools/                  # Development utilities
```

## Backend Structure (apps/backend/)

### Core Architecture
```
apps/backend/src/
├── adapters/              # External service integrations
├── agents/                # LangGraph specialized agents
├── api/                   # FastAPI route handlers
├── caching/               # Hierarchical cache system
├── config/                # Configuration management
│   └── setores/           # Sector-specific configs
│       └── cambio/L2M/    # L2M client configuration
├── graphs/                # LangGraph workflow definitions
├── interfaces/            # API models and contracts
├── knowledge/             # Domain knowledge and patterns
├── learning/              # Feedback and improvement system
├── models/                # Data models and schemas
├── nodes/                 # Individual workflow nodes
├── repositories/          # Data access layer
├── services/              # Business logic services
├── tools/                 # Utility functions
├── utils/                 # Common utilities
└── validation/            # Query and data validation
```

### Key Backend Files
- `main.py` - CLI entry point for development
- `app.py` - FastAPI application factory
- `webapp_unified.py` - Production WSGI application
- `pyproject.toml` - Poetry dependencies and scripts

### Configuration Structure
```
apps/backend/src/config/setores/cambio/L2M/
├── kpis-exchange-json.json     # KPI definitions
├── L2M_schema_relevance.json   # Database schema
├── llm.yaml                    # LLM provider config
├── prompts/                    # Agent prompts
│   ├── query_generator.txt
│   ├── business_analyst.txt
│   └── insight_generator.txt
├── nivel2/                     # Performance optimizations
│   ├── patterns.json
│   ├── domain_config.json
│   └── column_aliases.json
└── colunas_categoricas.json    # Categorical values
```

## Frontend Structure (apps/frontend/)

### Component Organization
```
apps/frontend/src/
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui base components
│   ├── chat/             # Chat interface components
│   ├── dashboard/        # Dashboard-specific components
│   └── common/           # Shared components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── pages/                # Route components
├── services/             # API client services
├── types/                # TypeScript type definitions
└── testing/              # Test utilities
```

### Key Frontend Files
- `main.tsx` - React application entry point
- `App.tsx` - Main application component
- `vite.config.ts` - Vite build configuration
- `tailwind.config.ts` - Tailwind CSS configuration

## Documentation Structure (docs/)

```
docs/
├── api/                  # API documentation
├── architecture/         # System architecture docs
├── chat/                 # Chat system documentation
├── deployment/           # Deployment guides
├── guides/               # User and developer guides
├── implementation/       # Implementation reports
└── reports/              # Status and analysis reports
```

## Scripts & Tools

### Development Scripts (scripts/)
- `dev.sh` - Development environment setup
- `test-all.sh` - Comprehensive test runner
- `validate-deps.sh` - Dependency validation
- `setup_*.py` - Database and system setup scripts

### Backend Scripts (apps/backend/scripts/)
- `generate_daily_snapshot.py` - KPI snapshot generation
- `cache_warming_script.py` - Cache preloading
- `debug_*.py` - Debugging utilities
- `validate_*.py` - System validation scripts

## Naming Conventions

### Files & Directories
- **Python**: `snake_case` for files, modules, functions
- **TypeScript/React**: `PascalCase` for components, `camelCase` for functions
- **Configuration**: `kebab-case` for config files
- **Documentation**: `UPPERCASE.md` for important docs, `lowercase.md` for others

### Code Structure
- **Backend Services**: `*Service` classes in `src/services/`
- **API Routes**: Grouped by domain in `src/api/`
- **LangGraph Nodes**: `*Node` classes in `src/nodes/`
- **React Components**: `*.tsx` files with matching component names
- **Custom Hooks**: `use*` prefix in `src/hooks/`

## Configuration Management

### Environment-Specific Configs
- **Development**: `.env.local` files (gitignored)
- **Production**: Railway environment variables
- **Testing**: `.env.test` files

### Client-Specific Configs
- **Path Pattern**: `src/config/setores/{sector}/{client_id}/`
- **Required Files**: `schema.json`, `llm.yaml`, `prompts/`
- **Optional Files**: `nivel2/`, `colunas_categoricas.json`

## Testing Structure

### Backend Tests (apps/backend/tests/)
```
tests/
├── adapters/             # Adapter integration tests
├── fixtures/             # Test data and fixtures
├── graphs/               # Workflow integration tests
├── integration/          # End-to-end tests
├── performance/          # Performance benchmarks
└── validation/           # Validation system tests
```

### Frontend Tests (apps/frontend/tests/)
```
tests/
├── dashboard-*.spec.ts   # Dashboard functionality tests
├── gemini-*.spec.ts      # AI integration tests
└── test-results/         # Playwright test results
```

## Import Conventions

### Backend Imports
```python
# Absolute imports from src/
from src.services.kpi_service import KpiService
from src.models.api_models import QueryRequest
from src.utils.database import get_db_connection
```

### Frontend Imports
```typescript
// Relative imports for local files
import { ChatInterface } from './components/chat/ChatInterface'
// Absolute imports for shared utilities
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
```

## Data Flow Patterns

### Backend Request Flow
1. **API Route** (`src/api/`) receives request
2. **Service Layer** (`src/services/`) processes business logic
3. **Repository Layer** (`src/repositories/`) handles data access
4. **LangGraph Workflow** (`src/graphs/`) orchestrates AI pipeline
5. **Response** formatted and returned

### Frontend State Flow
1. **User Action** triggers component event
2. **Custom Hook** (`src/hooks/`) manages state logic
3. **API Service** (`src/services/`) calls backend
4. **TanStack Query** caches and manages server state
5. **Component** re-renders with updated data