# Guia das Regras do Cursor - DataHero4

## Visão Geral
Este documento consolida todas as regras do Cursor criadas para o projeto DataHero4, garantindo consistência, qualidade e documentação adequada.

## Regras Disponíveis

### 1. 📋 Documentação de Implementações (`implementation-documentation.mdc`)
**Aplicação**: Sempre aplicada (`alwaysApply: true`)
**Propósito**: Garantir documentação completa de implementações importantes

**Quando usar**:
- Novas funcionalidades críticas
- Integrações complexas (frontend-backend)
- Sistemas de cache, monitoramento, performance
- Correções de bugs que afetam funcionalidade principal

**Template obrigatório**: Relatório em `docs/implementation/` com 10 seções estruturadas

### 2. 🐍 Padrões Python (`python-documentation.mdc`)
**Aplicação**: Arquivos `*.py`
**Propósito**: Padrões de código Python de alta qualidade

**Inclui**:
- Docstrings no formato Google Style
- Type hints obrigatórias
- Logging estruturado
- Error handling robusto
- Formatação com Black
- Testes unitários

### 3. ⚛️ Padrões TypeScript/React (`typescript-react-documentation.mdc`)
**Aplicação**: Arquivos `*.ts`, `*.tsx`
**Propósito**: Padrões de código frontend de alta qualidade

**Inclui**:
- Interfaces bem documentadas
- Hooks customizados com JSDoc
- Componentes React com props tipadas
- Funções de API com tratamento de erro
- Otimizações de performance
- Testes com Jest/Testing Library

### 4. 🏗️ Estrutura do Projeto (`project-structure.mdc`)
**Aplicação**: Sempre aplicada (`alwaysApply: true`)
**Propósito**: Organização consistente do projeto

**Inclui**:
- Estrutura de diretórios
- Convenções de nomenclatura
- Localização correta de arquivos
- Padrões de importação
- Gerenciamento de dependências
- Padrões de commit e branches

## Como as Regras Funcionam

### Aplicação Automática
Regras com `alwaysApply: true` são aplicadas em todas as interações:
- `implementation-documentation.mdc`
- `project-structure.mdc`

### Aplicação por Tipo de Arquivo
Regras com `globs` são aplicadas automaticamente aos arquivos correspondentes:
- `python-documentation.mdc` → `*.py`
- `typescript-react-documentation.mdc` → `*.ts`, `*.tsx`

### Aplicação Manual
Regras com `description` podem ser invocadas manualmente quando necessário:
- `cursor-rules-guide.mdc` (este arquivo)

## Workflow Recomendado

### 1. Antes de Implementar
1. **Consultar** `project-structure.mdc` para localização correta de arquivos
2. **Verificar** padrões de nomenclatura
3. **Planejar** estrutura seguindo as convenções

### 2. Durante a Implementação
1. **Seguir** padrões de código Python ou TypeScript conforme aplicável
2. **Usar** logging estruturado e tratamento de erros
3. **Aplicar** type hints e documentação adequada

### 3. Após a Implementação
1. **Criar** relatório de implementação usando template obrigatório
2. **Incluir** testes unitários
3. **Formatar** código com Black (Python) ou Prettier (TypeScript)
4. **Commit** seguindo padrões definidos

## Exemplos Práticos

### Implementação de Nova Feature
```bash
# 1. Criar branch seguindo convenção
git checkout -b feature/dashboard-real-time-updates

# 2. Implementar seguindo padrões de código
# - Python: docstrings, type hints, logging
# - TypeScript: interfaces, hooks, components

# 3. Criar documentação obrigatória
# docs/implementation/relatorio_dashboard_realtime_20250713.md

# 4. Commit seguindo padrão
git commit -m "feat(dashboard): adicionar atualizações em tempo real

Implementa sistema de WebSocket para updates automáticos:
- Hook useRealTimeUpdates com reconnection
- Componente RealTimeIndicator
- Backend WebSocket endpoint
- Cache invalidation em tempo real

Referências: #456"
```

### Correção de Bug Crítico
```bash
# 1. Branch específica
git checkout -b fix/kpi-calculation-overflow

# 2. Implementar correção com logs adequados
# Python: try/catch com logging estruturado
# Testes unitários para edge cases

# 3. Documentar se relevante (bugs críticos)
# docs/implementation/relatorio_fix_overflow_20250713.md

# 4. Commit descritivo
git commit -m "fix(kpi): corrigir overflow em cálculos de grandes volumes"
```

## Qualidade Garantida

### Checklist de Qualidade
- [ ] **Documentação**: Relatório de implementação criado?
- [ ] **Código Python**: Docstrings, type hints, logging?
- [ ] **Código TypeScript**: Interfaces, JSDoc, tratamento de erro?
- [ ] **Testes**: Unitários criados e passando?
- [ ] **Estrutura**: Arquivos na localização correta?
- [ ] **Commit**: Mensagem seguindo padrão?

### Métricas de Sucesso
- **100%** das implementações importantes documentadas
- **Zero** arquivos no root desnecessários
- **Consistência** na nomenclatura e estrutura
- **Qualidade** de código mantida com padrões definidos

## Evolução das Regras

### Como Atualizar
1. **Identificar** necessidade de nova regra ou atualização
2. **Editar** arquivo `.mdc` correspondente
3. **Testar** aplicação da regra
4. **Documentar** mudança neste guia

### Quando Criar Nova Regra
- Padrões específicos para novo tipo de arquivo
- Convenções para nova área do projeto
- Regras de qualidade específicas

## Referências Rápidas

### Localização das Regras
```
.cursor/rules/
├── implementation-documentation.mdc    # Documentação obrigatória
├── python-documentation.mdc           # Padrões Python
├── typescript-react-documentation.mdc # Padrões TypeScript/React
├── project-structure.mdc              # Estrutura do projeto
└── cursor-rules-guide.mdc            # Este guia
```

### Comandos Úteis
```bash
# Verificar estrutura do projeto
find . -name "*.py" -o -name "*.ts" -o -name "*.tsx" | head -20

# Formatar código Python
black apps/backend/src/

# Verificar tipos TypeScript
cd apps/frontend && npm run type-check

# Executar testes
cd apps/backend && poetry run pytest
cd apps/frontend && npm run test
```

---

**Lembre-se**: As regras existem para garantir qualidade e consistência. Quando em dúvida, consulte este guia ou as regras específicas.
description:
globs:
alwaysApply: false
---
