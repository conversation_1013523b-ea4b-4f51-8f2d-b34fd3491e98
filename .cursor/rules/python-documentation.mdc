# Padrões de Documentação Python - DataHero4

## Docstrings Obrigatórias
Toda função, classe e módulo deve ter docstring no formato Google Style:

### Funções
```python
def calculate_kpi_value(data: Dict[str, Any], kpi_config: Dict) -> float:
    """Calcula o valor de um KPI baseado nos dados fornecidos.
    
    Args:
        data: Dicionário com os dados brutos para cálculo
        kpi_config: Configuração do KPI incluindo fórmula e parâmetros
        
    Returns:
        Valor calculado do KPI como float
        
    Raises:
        ValueError: Se os dados estão incompletos ou inválidos
        ZeroDivisionError: Se há divisão por zero no cálculo
        
    Example:
        >>> data = {"revenue": 1000, "customers": 50}
        >>> config = {"formula": "revenue/customers"}
        >>> calculate_kpi_value(data, config)
        20.0
    """
```

### Classes
```python
class CacheWarmingService:
    """Serviço responsável por pré-carregar cache de KPIs prioritários.
    
    Este serviço implementa estratégias inteligentes de cache warming,
    priorizando KPIs críticos e otimizando performance do dashboard.
    
    Attributes:
        client_id: ID do cliente para cache específico
        priority_kpis: Lista de KPIs prioritários para warming
        cache_manager: Instância do gerenciador de cache
        
    Example:
        >>> service = CacheWarmingService("L2M")
        >>> await service.warm_priority_kpis()
    """
```

## Type Hints Obrigatórias
- Sempre usar type hints em parâmetros e retornos
- Importar tipos do módulo `typing` quando necessário
- Para APIs, usar modelos Pydantic

```python
from typing import Dict, List, Optional, Union
from pydantic import BaseModel

def process_dashboard_data(
    client_id: str,
    timeframe: str,
    currency: Optional[str] = None
) -> Dict[str, Union[float, List[Dict]]]:
    """Processa dados do dashboard com filtros aplicados."""
```

## Logging Estruturado
Usar sempre logging estruturado com contexto:

```python
import logging
logger = logging.getLogger(__name__)

# ✅ Bom
logger.info(
    "KPI calculado com sucesso",
    extra={
        "kpi_id": kpi_id,
        "value": result,
        "client_id": client_id,
        "execution_time_ms": execution_time
    }
)

# ❌ Evitar
print(f"KPI {kpi_id} = {result}")
```

## Error Handling
Sempre implementar tratamento de erro robusto:

```python
try:
    result = calculate_complex_kpi(data)
    logger.info("✅ KPI calculado", extra={"kpi": kpi_id, "value": result})
    return result
except ZeroDivisionError as e:
    logger.warning("⚠️ Divisão por zero detectada", extra={"kpi": kpi_id, "error": str(e)})
    return 0.0
except Exception as e:
    logger.error("❌ Erro no cálculo do KPI", extra={"kpi": kpi_id, "error": str(e)})
    raise
```

## Formatação com Black
- Sempre formatar código com `black`
- Linha máxima: 88 caracteres (padrão black)
- Usar aspas duplas para strings

## Testes Unitários
Para cada função/classe nova, criar teste correspondente:

```python
# Em tests/test_kpi_service.py
def test_calculate_kpi_value_success():
    """Testa cálculo bem-sucedido de KPI."""
    data = {"revenue": 1000, "customers": 50}
    config = {"formula": "revenue/customers"}
    
    result = calculate_kpi_value(data, config)
    
    assert result == 20.0

def test_calculate_kpi_value_zero_division():
    """Testa tratamento de divisão por zero."""
    data = {"revenue": 1000, "customers": 0}
    config = {"formula": "revenue/customers"}
    
    result = calculate_kpi_value(data, config)
    
    assert result == 0.0  # Deve retornar 0 em vez de erro
```

## Imports Organizados
```python
# 1. Imports da biblioteca padrão
import logging
from typing import Dict, List, Optional

# 2. Imports de terceiros
import pandas as pd
from fastapi import HTTPException
from pydantic import BaseModel

# 3. Imports locais
from src.models.kpi_models import KPIDefinition
from src.services.cache_service import CacheManager
from src.utils.database import get_database_connection
```

## Constantes e Configurações
```python
# No topo do arquivo, após imports
DEFAULT_CACHE_TTL = 3600  # 1 hora em segundos
MAX_RETRY_ATTEMPTS = 3
PRIORITY_KPIS = ["total_volume", "average_ticket", "retention_rate"]

# Para configurações complexas, usar arquivo separado
from src.config.kpi_config import KPI_DEFINITIONS
```

## Performance e Cache
- Sempre considerar cache para operações custosas
- Documentar complexidade temporal quando relevante
- Usar async/await para operações I/O

```python
async def get_cached_kpi_value(
    client_id: str, 
    kpi_id: str,
    ttl: int = DEFAULT_CACHE_TTL
) -> Optional[float]:
    """Busca valor de KPI no cache com fallback para cálculo.
    
    Complexidade: O(1) para hit de cache, O(n) para miss onde n é o tamanho dos dados.
    """
```
description:
globs:
alwaysApply: false
---
