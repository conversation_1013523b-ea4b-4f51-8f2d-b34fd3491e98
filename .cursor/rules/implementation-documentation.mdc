# Regra de Documentação de Implementações

## Quando Aplicar
Esta regra deve ser aplicada ao final de qualquer implementação considerada de **alta relevância** para o projeto DataHero4, incluindo:

- Novos sistemas ou módulos principais
- Integrações críticas (frontend-backend, APIs externas)
- Sistemas de cache, monitoramento ou performance
- Correções de bugs críticos que afetam funcionalidade principal
- Implementações que envolvem múltiplos arquivos/componentes
- Mudanças na arquitetura do sistema

## Estrutura Obrigatória do Documento

### Localização
- **Pasta**: `docs/implementation/`
- **Nome**: `[tipo]_[descrição]_[YYYYMMDD].md`
- **Exemplo**: `relatorio_implementacao_dashboard_integration_20250713.md`

### Template Obrigatório

```markdown
# Relatório de Implementação: [T<PERSON><PERSON><PERSON> da Implementação]

**Data**: [DD/MM/YYYY]  
**Autor**: [Nome/Sistema]  
**Tipo**: [Feature/Bugfix/Integration/Performance/etc]  
**Relevância**: Alta  

## 1. Resumo Executivo
- **Objetivo**: Descrição clara do que foi implementado
- **Problema Resolvido**: Contexto do problema original
- **Impacto**: Benefícios e melhorias alcançadas

## 2. Arquivos Modificados/Criados
### Frontend
- `path/to/file.tsx` - Descrição da mudança
- `path/to/file.ts` - Descrição da mudança

### Backend
- `path/to/file.py` - Descrição da mudança
- `path/to/file.py` - Descrição da mudança

### Configuração/Scripts
- `path/to/config.yaml` - Descrição da mudança

## 3. Implementação Técnica Detalhada

### 3.1 [Componente/Sistema 1]
**Arquivo**: `path/to/file`
**Função**: Descrição do que faz
**Implementação**:
```language
// Código exemplo ou trechos relevantes
```
**Como Funciona**: Explicação detalhada

### 3.2 [Componente/Sistema 2]
[Repetir estrutura acima]

## 4. Fluxo de Dados
Descrição detalhada de como os dados fluem entre componentes:
1. Usuário faz X
2. Frontend chama Y
3. Backend processa Z
4. Resultado retorna W

## 5. Testes e Validação
### Testes Realizados
- [ ] Teste funcional básico
- [ ] Teste de integração
- [ ] Teste de performance
- [ ] Teste de edge cases

### Resultados
- **Antes**: Métricas/comportamento anterior
- **Depois**: Métricas/comportamento atual
- **Melhoria**: Percentual/valores de melhoria

## 6. Configuração e Deploy
### Dependências Adicionadas
```bash
# Comandos de instalação se necessário
```

### Variáveis de Ambiente
```bash
# Novas variáveis se necessário
```

### Scripts de Deploy
```bash
# Comandos específicos para deploy
```

## 7. Monitoramento e Logs
- **Logs Relevantes**: Onde encontrar logs desta implementação
- **Métricas**: Como monitorar o funcionamento
- **Alertas**: Configurações de alerta se aplicável

## 8. Troubleshooting
### Problemas Conhecidos
- **Problema**: Descrição
- **Solução**: Como resolver

### Debugging
- **Como debuggar**: Passos para investigar problemas
- **Logs importantes**: Quais logs verificar

## 9. Próximos Passos
- [ ] Melhorias futuras planejadas
- [ ] Otimizações pendentes
- [ ] Refatorações necessárias

## 10. Referências
- Links para PRs relevantes
- Documentação técnica consultada
- Issues relacionadas
```

## Responsabilidades

### Do Desenvolvedor/AI Assistant
1. **SEMPRE** criar o documento ao final de implementações relevantes
2. Usar o template completo, não versões resumidas
3. Incluir código de exemplo quando relevante
4. Documentar o "como funciona", não apenas "o que foi feito"
5. Incluir métricas de before/after quando possível

### Critérios de Qualidade
- **Completude**: Todas as seções preenchidas adequadamente
- **Clareza**: Linguagem clara e objetiva em português
- **Técnico**: Detalhes suficientes para replicação
- **Prático**: Informações úteis para troubleshooting

## Integração com Git
- Sempre incluir o documento no commit da implementação
- Referenciar o documento na mensagem de commit
- Adicionar ao git com `git add docs/implementation/[arquivo].md`

## Exemplos de Referência
Consulte documentos existentes em [docs/implementation/](mdc:docs/implementation/) para exemplos de implementações bem documentadas.
description:
globs:
alwaysApply: false
---
