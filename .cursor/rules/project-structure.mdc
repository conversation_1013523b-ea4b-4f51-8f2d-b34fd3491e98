# Organização e Estrutura do Projeto DataHero4

## Estrutura de Diretórios

### Backend (`apps/backend/`)
```
src/
├── api/              # Endpoints e rotas da API
├── services/         # Lógica de negócio e serviços
├── models/           # Modelos de dados e schemas
├── utils/            # Utilitários e helpers
├── config/           # Configurações e constantes
├── middleware/       # Middlewares do FastAPI
├── tools/            # Ferramentas específicas de domínio
├── graphs/           # Grafos LangGraph
├── nodes/            # Nós individuais do pipeline
├── interfaces/       # Interfaces e protocolos
├── caching/          # Sistema de cache
├── monitoring/       # Observabilidade e monitoramento
├── onboarding/       # Sistema de onboarding
├── rag/              # Retrieval Augmented Generation
└── tests/            # Testes unitários e integração
```

### Frontend (`apps/frontend/`)
```
src/
├── components/       # Componentes React reutilizáveis
│   ├── ui/          # Componentes base (shadcn/ui)
│   ├── chat/        # Componentes específicos do chat
│   └── dashboard/   # Componentes do dashboard
├── hooks/           # Hooks customizados
├── pages/           # Páginas da aplicação
├── lib/             # Utilitários e configurações
├── utils/           # Funções auxiliares
└── types/           # Definições de tipos TypeScript
```

### Documentação (`docs/`)
```
docs/
├── implementation/   # Relatórios de implementação
├── architecture/     # Documentação de arquitetura
├── api/             # Documentação da API
├── guides/          # Guias de uso e desenvolvimento
├── reports/         # Relatórios técnicos
└── deployment/      # Documentação de deploy
```

## Convenções de Nomenclatura

### Arquivos Python
- **Módulos**: `snake_case.py` (ex: `kpi_service.py`)
- **Classes**: `PascalCase` (ex: `CacheWarmingService`)
- **Funções**: `snake_case` (ex: `calculate_kpi_value`)
- **Constantes**: `UPPER_SNAKE_CASE` (ex: `DEFAULT_CACHE_TTL`)

### Arquivos TypeScript/React
- **Componentes**: `PascalCase.tsx` (ex: `DashboardControls.tsx`)
- **Hooks**: `camelCase.ts` com prefixo `use` (ex: `useDashboardFilters.ts`)
- **Utilitários**: `camelCase.ts` (ex: `apiClient.ts`)
- **Types**: `camelCase.ts` ou em `types/` (ex: `dashboard.ts`)

### Arquivos de Documentação
- **Implementações**: `relatorio_[tipo]_[descrição]_[YYYYMMDD].md`
- **Guias**: `[TOPIC]_GUIDE.md` (ex: `DASHBOARD_GUIDE.md`)
- **Planos**: `[projeto]_plan.md` ou `PLAN_[PROJETO].md`

## Localização de Arquivos

### ❌ NÃO criar no root
- Scripts de desenvolvimento
- Arquivos de configuração específicos
- Documentação técnica
- Testes

### ✅ Localizações Corretas

#### Scripts
- **Backend**: `apps/backend/scripts/`
- **Projeto**: `scripts/` (apenas scripts globais)
- **Frontend**: `apps/frontend/scripts/`

#### Configurações
- **Backend**: `apps/backend/src/config/`
- **Frontend**: `apps/frontend/src/lib/config/`
- **Projeto**: Apenas `package.json`, `turbo.json`, etc.

#### Testes
- **Backend**: `apps/backend/tests/` ou `apps/backend/src/tests/`
- **Frontend**: `apps/frontend/src/__tests__/` ou junto aos componentes
- **E2E**: `tests/` (root apenas para testes end-to-end)

#### Documentação
- **Implementações**: `docs/implementation/`
- **Arquitetura**: `docs/architecture/`
- **API**: `docs/api/`
- **Guias**: `docs/guides/`

## Padrões de Importação

### Backend Python
```python
# 1. Imports padrão
import logging
from typing import Dict, List, Optional

# 2. Imports de terceiros
from fastapi import HTTPException
from pydantic import BaseModel

# 3. Imports do projeto (absolutos)
from src.services.kpi_service import KPIService
from src.models.dashboard_models import DashboardFilters
from src.utils.database import get_connection
```

### Frontend TypeScript
```typescript
// 1. React
import React from 'react';

// 2. Bibliotecas externas
import { useQuery } from '@tanstack/react-query';

// 3. Componentes UI
import { Card, CardContent } from '@/components/ui/card';

// 4. Componentes locais
import { DashboardControls } from '@/components/dashboard/DashboardControls';

// 5. Hooks
import { useDashboardFilters } from '@/hooks/useDashboardFilters';

// 6. Types
import type { DashboardFilters } from '@/types/dashboard';

// 7. Utils
import { formatCurrency } from '@/utils/format';
```

## Gerenciamento de Dependências

### Backend - Poetry
```toml
# pyproject.toml
[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
pydantic = "^2.5.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
black = "^23.11.0"
```

### Frontend - Bun
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "@tanstack/react-query": "^5.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "typescript": "^5.0.0"
  }
}
```

## Padrões de Commit

### Formato
```
tipo(escopo): descrição curta

Descrição mais detalhada se necessário.

- Item específico alterado
- Outro item alterado

Referências: #123, #456
```

### Tipos
- `feat`: Nova funcionalidade
- `fix`: Correção de bug
- `docs`: Documentação
- `refactor`: Refatoração
- `perf`: Melhoria de performance
- `test`: Testes
- `chore`: Tarefas de manutenção

### Exemplos
```bash
feat(dashboard): adicionar filtros de timeframe e moeda

Implementa sistema completo de filtros para o dashboard:
- Hook useDashboardFilters com debounce
- Componente DashboardControls
- Integração com API backend
- Cache aware de filtros

Referências: #123
```

## Estrutura de Branches

### Principais
- `main`: Produção estável
- `develop`: Desenvolvimento ativo

### Features
- `feature/[nome-da-feature]`
- `fix/[nome-do-bug]`
- `docs/[tipo-de-doc]`

### Exemplo
```bash
# Feature branch
git checkout -b feature/dashboard-filters

# Bug fix
git checkout -b fix/kpi-division-zero

# Documentação
git checkout -b docs/implementation-guide
```

## Logs e Debugging

### Backend - Logging Estruturado
```python
import logging
logger = logging.getLogger(__name__)

# Sempre usar extra para contexto
logger.info(
    "KPI calculado com sucesso",
    extra={
        "kpi_id": kpi_id,
        "client_id": client_id,
        "value": result,
        "execution_time_ms": execution_time
    }
)
```

### Frontend - Console Debugging
```typescript
// Desenvolvimento: console.log com contexto
console.log('Dashboard filters updated:', { timeframe, currency });

// Produção: usar serviço de logging
if (process.env.NODE_ENV === 'production') {
  logService.info('Dashboard filters updated', { timeframe, currency });
}
```

## Performance e Otimização

### Backend
- Cache em Redis para operações custosas
- Async/await para I/O operations
- Paginação para grandes datasets
- Índices adequados no PostgreSQL

### Frontend
- React.memo para componentes puros
- useCallback/useMemo para otimizações
- Code splitting com lazy loading
- Debounce para inputs de usuário

## Monitoramento

### Métricas Importantes
- Tempo de resposta da API
- Taxa de cache hit/miss
- Erros por endpoint
- Performance de queries SQL

### Logs Estruturados
- Sempre incluir `client_id`, `user_id` quando disponível
- Timestamp automático
- Level apropriado (DEBUG, INFO, WARNING, ERROR)
- Contexto suficiente para debugging
description:
globs:
alwaysApply: false
---
