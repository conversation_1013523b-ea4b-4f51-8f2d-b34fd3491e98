# Padrões de Documentação TypeScript/React - DataHero4

## Interfaces e Types
Sempre documentar interfaces complexas com JSDoc:

```typescript
/**
 * Configuração de filtros para o dashboard de KPIs
 */
interface DashboardFilters {
  /** Período de tempo para análise (1d, week, month, quarter) */
  timeframe: TimeframeOption;
  /** Moeda para filtrar transações (all, usd, eur, gbp) */
  currency: CurrencyOption;
}

/**
 * Dados de um KPI individual retornados pela API
 */
interface KpiData {
  /** Identificador único do KPI */
  id: string;
  /** Nome de exibição do KPI */
  name: string;
  /** Valor atual calculado */
  value: number;
  /** Variação percentual em relação ao período anterior */
  change: number;
  /** Dados históricos para gráfico */
  chartData: ChartDataPoint[];
  /** Indica se o KPI está sendo carregado */
  loading?: boolean;
  /** Mensagem de erro se houver falha no carregamento */
  error?: string;
}
```

## Hooks Customizados
Documentar hooks com propósito, parâmetros e retorno:

```typescript
/**
 * Hook para gerenciar filtros do dashboard com debounce automático
 * 
 * @param initialTimeframe - Período inicial selecionado
 * @param initialCurrency - Moeda inicial selecionada
 * @returns Objeto com filtros atuais e funções para atualizá-los
 * 
 * @example
 * ```tsx
 * const { filters, setTimeframe, setCurrency } = useDashboardFilters('week', 'all');
 * ```
 */
export const useDashboardFilters = (
  initialTimeframe: TimeframeOption = 'week',
  initialCurrency: CurrencyOption = 'all'
): {
  filters: DashboardFilters;
  setTimeframe: (timeframe: TimeframeOption) => void;
  setCurrency: (currency: CurrencyOption) => void;
} => {
  // Implementação...
}
```

## Componentes React
Documentar props e comportamento dos componentes:

```typescript
interface DashboardControlsProps {
  /** Filtros atuais aplicados */
  filters: DashboardFilters;
  /** Callback chamado quando timeframe é alterado */
  onTimeframeChange: (timeframe: TimeframeOption) => void;
  /** Callback chamado quando moeda é alterada */
  onCurrencyChange: (currency: CurrencyOption) => void;
  /** Se true, desabilita todos os controles */
  disabled?: boolean;
}

/**
 * Componente de controles para filtros do dashboard
 * 
 * Permite ao usuário selecionar período de tempo e moeda para
 * filtrar os dados exibidos no dashboard de KPIs.
 * 
 * @param props - Propriedades do componente
 * @returns Elemento JSX com os controles de filtro
 * 
 * @example
 * ```tsx
 * <DashboardControls
 *   filters={filters}
 *   onTimeframeChange={setTimeframe}
 *   onCurrencyChange={setCurrency}
 * />
 * ```
 */
export const DashboardControls: React.FC<DashboardControlsProps> = ({
  filters,
  onTimeframeChange,
  onCurrencyChange,
  disabled = false
}) => {
  // Implementação...
}
```

## Funções de API
Documentar chamadas de API com tipos e tratamento de erro:

```typescript
/**
 * Busca dados de KPIs do dashboard com filtros aplicados
 * 
 * @param timeframe - Período para análise dos dados
 * @param currency - Moeda para filtrar transações
 * @returns Promise com array de dados de KPIs
 * 
 * @throws {Error} Quando a API retorna erro ou dados inválidos
 * 
 * @example
 * ```typescript
 * const kpis = await getDashboardKpis('week', 'usd');
 * console.log(`Carregados ${kpis.length} KPIs`);
 * ```
 */
export const getDashboardKpis = async (
  timeframe: TimeframeOption = 'week',
  currency: CurrencyOption = 'all'
): Promise<KpiData[]> => {
  try {
    const response = await fetch(`/api/dashboard/kpis?timeframe=${timeframe}&currency=${currency}`);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.kpis || [];
  } catch (error) {
    console.error('Erro ao buscar KPIs:', error);
    throw error;
  }
}
```

## Constantes e Enums
Documentar constantes com seu propósito:

```typescript
/**
 * Opções disponíveis para período de análise
 */
export const TIMEFRAME_OPTIONS = [
  { value: '1d', label: '24 horas' },
  { value: 'week', label: 'Última semana' },
  { value: 'month', label: 'Último mês' },
  { value: 'quarter', label: 'Último trimestre' }
] as const;

/**
 * Mapeamento de moedas para exibição
 */
export const CURRENCY_LABELS: Record<CurrencyOption, string> = {
  all: 'Todas as moedas',
  usd: 'USD ($)',
  eur: 'EUR (€)',
  gbp: 'GBP (£)'
};

/**
 * Configuração padrão para debounce em filtros
 */
export const DEFAULT_DEBOUNCE_DELAY = 300; // ms
```

## Error Boundaries
Documentar tratamento de erros:

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

/**
 * Error Boundary para capturar erros em componentes do dashboard
 * 
 * Captura erros JavaScript em qualquer lugar da árvore de componentes
 * filhos e exibe uma UI de fallback em vez de quebrar a aplicação.
 */
export class DashboardErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error:', error, errorInfo);
    // Aqui poderia enviar erro para serviço de monitoramento
  }
  
  // Resto da implementação...
}
```

## Testes com Jest/Testing Library
Documentar testes com cenários cobertos:

```typescript
/**
 * Testes para o hook useDashboardFilters
 */
describe('useDashboardFilters', () => {
  /**
   * Testa inicialização com valores padrão
   */
  it('should initialize with default values', () => {
    const { result } = renderHook(() => useDashboardFilters());
    
    expect(result.current.filters.timeframe).toBe('week');
    expect(result.current.filters.currency).toBe('all');
  });

  /**
   * Testa atualização de timeframe com debounce
   */
  it('should update timeframe with debounce', async () => {
    const { result } = renderHook(() => useDashboardFilters());
    
    act(() => {
      result.current.setTimeframe('month');
    });
    
    // Aguarda debounce
    await waitFor(() => {
      expect(result.current.filters.timeframe).toBe('month');
    });
  });
});
```

## Imports Organizados
```typescript
// 1. React e hooks
import React, { useState, useEffect, useCallback } from 'react';

// 2. Bibliotecas externas
import { debounce } from 'lodash';
import { useQuery } from '@tanstack/react-query';

// 3. Componentes UI
import { Select, SelectContent, SelectItem } from '@/components/ui/select';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

// 4. Hooks e utilities locais
import { useDashboardFilters } from '@/hooks/useDashboardFilters';
import { useKpiData } from '@/hooks/useKpiData';

// 5. Types e interfaces
import type { DashboardFilters, KpiData } from '@/types/dashboard';

// 6. Constantes
import { TIMEFRAME_OPTIONS, CURRENCY_OPTIONS } from '@/constants/dashboard';
```

## Performance
Documentar otimizações de performance:

```typescript
/**
 * Componente otimizado com React.memo para evitar re-renders desnecessários
 * 
 * Re-renderiza apenas quando props.kpiData ou props.loading mudam
 */
export const KpiCard = React.memo<KpiCardProps>(({ kpiData, loading }) => {
  // Implementação...
}, (prevProps, nextProps) => {
  return prevProps.kpiData.value === nextProps.kpiData.value &&
         prevProps.loading === nextProps.loading;
});

/**
 * Hook com useCallback para evitar recriação de funções
 */
export const useOptimizedFilters = () => {
  const [filters, setFilters] = useState<DashboardFilters>({
    timeframe: 'week',
    currency: 'all'
  });

  // Memoiza callback para evitar re-renders em componentes filhos
  const updateTimeframe = useCallback((timeframe: TimeframeOption) => {
    setFilters(prev => ({ ...prev, timeframe }));
  }, []);

  return { filters, updateTimeframe };
};
```
description:
globs:
alwaysApply: false
---
