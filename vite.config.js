const { defineConfig } = require("vite");
const react = require("@vitejs/plugin-react-swc");
const path = require("path");

// Simplified Vite config for Render deployment
module.exports = defineConfig(() => ({
  // Set root to frontend directory
  root: path.resolve(__dirname, 'apps/frontend'),
  
  // Build configuration
  build: {
    outDir: path.resolve(__dirname, 'apps/frontend/dist'),
    emptyOutDir: true,
    sourcemap: false,
    minify: 'esbuild',
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
  
  // Server configuration for development
  server: {
    port: 3000,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: process.env.VITE_API_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/ws': {
        target: process.env.VITE_WS_URL || 'ws://localhost:8000',
        ws: true,
        changeOrigin: true,
      }
    },
  },
  
  // Preview configuration for production
  preview: {
    port: process.env.PORT || 3000,
    host: '0.0.0.0',
  },
  
  // Plugins
  plugins: [react()],
  
  // Path resolution
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "apps/frontend/src"),
    },
  },
  
  // Environment variables
  envDir: path.resolve(__dirname, 'apps/frontend'),
  
  // Dependency optimization
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },
}));
