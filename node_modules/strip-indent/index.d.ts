/**
Strip leading whitespace from each line in a string.

The line with the least number of leading whitespace, ignoring empty lines, determines the number to remove.

@example
```
import stripIndent = require('strip-indent');

const string = '\tunicorn\n\t\tcake';
//	unicorn
//		cake

stripIndent(string);
//unicorn
//	cake
```
*/
declare function stripIndent(string: string): string;

export = stripIndent;
