# CSSStyleDeclaration

A Node.js implementation of the CSS Object Model [`CSSStyleDeclaration` class](https://drafts.csswg.org/cssom/#the-cssstyledeclaration-interface).

## Background

This package is an extension of the `CSSStyleDeclaration` class in <PERSON><PERSON>'s [CSSOM](https://github.com/NV/CSSOM), with added support for modern specifications. The primary use case is for testing browser code in a Node environment.

It was originally created by <PERSON>, it is now maintained by the jsdom community.

Bug reports and pull requests are welcome.
