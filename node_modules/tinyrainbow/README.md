# tinyrainbow

Output your colorful messages in the terminal or browser console that support ANSI colors (Chrome engines).

A small (`~ 6 kB` unpacked) fork of [picocolors](https://www.npmjs.com/package/picocolors) with support for `exports` field.

Supports only ESM.

## Installing

```bash
# with npm
$ npm install -D tinyrainbow

# with pnpm
$ pnpm add -D tinyrainbow

# with yarn
$ yarn add -D tinyrainbow
```

## Usage

```js
import c from 'tinyrainbow'

console.log(c.red(c.bold('Hello World!')))
```
