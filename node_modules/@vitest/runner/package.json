{"name": "@vitest/runner", "type": "module", "version": "3.2.4", "description": "V<PERSON>t test runner", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/runner#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/runner"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": true, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}, "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"pathe": "^2.0.3", "strip-literal": "^3.0.0", "@vitest/utils": "3.2.4"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}