{"name": "strip-literal", "type": "module", "version": "3.0.0", "description": "Strip comments and string literals from JavaScript code", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/strip-literal#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/strip-literal.git"}, "bugs": {"url": "https://github.com/antfu/strip-literal/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"js-tokens": "^9.0.1"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@antfu/ni": "^23.2.0", "@types/node": "^22.10.6", "bumpp": "^9.10.1", "eslint": "^9.18.0", "esmo": "^4.8.0", "pnpm": "^9.15.4", "rimraf": "^6.0.1", "three": "^0.172.0", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vite": "^6.0.7", "vitest": "^2.1.8", "vue": "^3.5.13"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "start": "esmo src/index.ts", "test": "vitest", "bench": "vitest bench", "typecheck": "tsc --noEmit"}}