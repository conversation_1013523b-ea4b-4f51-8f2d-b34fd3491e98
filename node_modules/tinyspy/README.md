# tinyspy

> minimal fork of nanospy, with more features 🕵🏻‍♂️

A `10KB` package for minimal and easy testing with no dependencies.
This package was created for having a tiny spy library to use in `vitest`, but it can also be used in `jest` and other test environments.

_In case you need more tiny libraries like tinypool or tinyspy, please consider submitting an [RFC](https://github.com/tinylibs/rfcs)_

## Docs
Read full docs **[here](https://github.com/tinylibs/tinyspy#readme)**.
