/**
 * ProfileSetup Component - DataHero4 Week 5
 * =========================================
 * 
 * Intuitive profile setup wizard for user onboarding and configuration.
 * Multi-step process with role selection, KPI preferences, and settings.
 * 
 * Features:
 * - Step-by-step wizard interface
 * - Profile type selection with characteristics
 * - KPI recommendation and selection
 * - Preferences configuration
 * - Auto-save and validation
 * - Responsive design with animations
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-21
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Check, User, Settings, BarChart3, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { ProfileSetupProps, ProfileSetupState, ProfileType, UserProfile, ProfileCharacteristics, PROFILE_CHARACTERISTICS, DEFAULT_PREFERENCES, KpiOption } from '@/types/profile';

// Mock KPI options - in production this would come from API
const MOCK_KPI_OPTIONS: KpiOption[] = [
  {
    id: 'spread_income_detailed',
    name: 'Spread Income Detalhado',
    description: 'Receita detalhada por spread por moeda e período',
    category: 'spread',
    isRecommended: true,
    isPriority: true,
    targetProfiles: ['CEO', 'Trader']
  },
  {
    id: 'margem_liquida_operacional',
    name: 'Margem Líquida Operacional',
    description: 'Margem operacional líquida: (Receita Spread - Custos) / Receita Total * 100',
    category: 'performance',
    isRecommended: true,
    isPriority: true,
    targetProfiles: ['CEO', 'CFO']
  },
  {
    id: 'custo_por_transacao',
    name: 'Custo por Transação',
    description: 'Custo operacional médio por transação processada',
    category: 'performance',
    isRecommended: true,
    isPriority: false,
    targetProfiles: ['CFO', 'Operations']
  },
  {
    id: 'tempo_processamento_medio',
    name: 'Tempo Processamento Médio',
    description: 'Tempo médio de processamento de transações (em segundos)',
    category: 'performance',
    isRecommended: true,
    isPriority: false,
    targetProfiles: ['Risk_Manager', 'Trader', 'Operations']
  }
];

const ProfileSetup: React.FC<ProfileSetupProps> = ({
  userId,
  onComplete,
  onSkip,
  initialStep = 0,
  className
}) => {
  const [setupState, setSetupState] = useState<ProfileSetupState>({
    currentStep: initialStep,
    totalSteps: 4,
    steps: [
      {
        id: 'welcome',
        title: 'Bem-vindo',
        description: 'Configure seu perfil para uma experiência personalizada',
        component: 'role-selection',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'role',
        title: 'Seu Perfil',
        description: 'Selecione o perfil que melhor descreve sua função',
        component: 'role-selection',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'kpis',
        title: 'KPIs Relevantes',
        description: 'Escolha os indicadores mais importantes para você',
        component: 'kpi-selection',
        isRequired: false,
        isCompleted: false
      },
      {
        id: 'preferences',
        title: 'Preferências',
        description: 'Configure suas preferências de visualização',
        component: 'preferences',
        isRequired: false,
        isCompleted: false
      }
    ],
    selectedProfile: null,
    selectedKpis: [],
    preferences: DEFAULT_PREFERENCES,
    isValid: false,
    errors: {}
  });

  const [isLoading, setIsLoading] = useState(false);

  // Validate current step
  const validateStep = useCallback((step: number): boolean => {
    switch (step) {
      case 0: // Welcome
        return true;
      case 1: // Role selection
        return setupState.selectedProfile !== null;
      case 2: // KPI selection
        return setupState.selectedKpis.length > 0;
      case 3: // Preferences
        return true;
      default:
        return false;
    }
  }, [setupState.selectedProfile, setupState.selectedKpis]);

  // Update validation when state changes
  useEffect(() => {
    const isValid = validateStep(setupState.currentStep);
    setSetupState(prev => ({ ...prev, isValid }));
  }, [setupState.currentStep, setupState.selectedProfile, setupState.selectedKpis, validateStep]);

  // Handle profile selection
  const handleProfileSelect = (profileType: ProfileType) => {
    const characteristics = PROFILE_CHARACTERISTICS[profileType];
    const recommendedKpis = characteristics.recommendedKpis;

    setSetupState(prev => ({
      ...prev,
      selectedProfile: profileType,
      selectedKpis: recommendedKpis, // Auto-select recommended KPIs
      steps: prev.steps.map((step, index) => 
        index === 1 ? { ...step, isCompleted: true } : step
      )
    }));
  };

  // Handle KPI toggle
  const handleKpiToggle = (kpiId: string) => {
    setSetupState(prev => ({
      ...prev,
      selectedKpis: prev.selectedKpis.includes(kpiId)
        ? prev.selectedKpis.filter(id => id !== kpiId)
        : [...prev.selectedKpis, kpiId]
    }));
  };

  // Handle next step
  const handleNext = () => {
    if (setupState.currentStep < setupState.totalSteps - 1) {
      setSetupState(prev => ({
        ...prev,
        currentStep: prev.currentStep + 1,
        steps: prev.steps.map((step, index) => 
          index === prev.currentStep ? { ...step, isCompleted: true } : step
        )
      }));
    } else {
      handleComplete();
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if (setupState.currentStep > 0) {
      setSetupState(prev => ({
        ...prev,
        currentStep: prev.currentStep - 1
      }));
    }
  };

  // Handle setup completion
  const handleComplete = async () => {
    if (!setupState.selectedProfile) return;

    setIsLoading(true);
    try {
      // Create user profile object
      const userProfile = {
        id: `profile_${userId}_${Date.now()}`,
        userId,
        profileType: setupState.selectedProfile,
        displayName: PROFILE_CHARACTERISTICS[setupState.selectedProfile].name,
        description: PROFILE_CHARACTERISTICS[setupState.selectedProfile].description,
        preferences: setupState.preferences,
        selectedKpis: setupState.selectedKpis,
        cacheStrategy: PROFILE_CHARACTERISTICS[setupState.selectedProfile].cacheStrategy,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Call completion handler
      await onComplete(userProfile);
    } catch (error) {
      console.error('Error completing profile setup:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (setupState.currentStep) {
      case 0:
        return <WelcomeStep />;
      case 1:
        return (
          <RoleSelectionStep
            selectedProfile={setupState.selectedProfile}
            onSelect={handleProfileSelect}
          />
        );
      case 2:
        return (
          <KpiSelectionStep
            selectedKpis={setupState.selectedKpis}
            onToggle={handleKpiToggle}
            profileType={setupState.selectedProfile}
          />
        );
      case 3:
        return (
          <PreferencesStep
            preferences={setupState.preferences}
            onChange={(prefs) => setSetupState(prev => ({ ...prev, preferences: { ...prev.preferences, ...prefs } }))}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={cn("max-w-4xl mx-auto p-6", className)}>
      {/* Progress Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Configuração de Perfil
          </h1>
          {onSkip && (
            <Button variant="ghost" onClick={onSkip}>
              Pular configuração
            </Button>
          )}
        </div>

        {/* Progress Bar */}
        <div className="flex items-center space-x-4 mb-2">
          {setupState.steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors",
                index < setupState.currentStep || step.isCompleted
                  ? "bg-blue-600 text-white"
                  : index === setupState.currentStep
                  ? "bg-blue-100 text-blue-600 border-2 border-blue-600"
                  : "bg-gray-200 text-gray-500"
              )}>
                {step.isCompleted ? (
                  <Check className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < setupState.steps.length - 1 && (
                <div className={cn(
                  "w-12 h-0.5 mx-2 transition-colors",
                  index < setupState.currentStep ? "bg-blue-600" : "bg-gray-200"
                )} />
              )}
            </div>
          ))}
        </div>

        <div className="text-sm text-gray-600">
          Passo {setupState.currentStep + 1} de {setupState.totalSteps}: {setupState.steps[setupState.currentStep]?.title}
        </div>
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={setupState.currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="mb-8"
        >
          {renderStepContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={setupState.currentStep === 0}
          className="flex items-center space-x-2"
        >
          <ChevronLeft className="w-4 h-4" />
          <span>Anterior</span>
        </Button>

        <div className="text-sm text-gray-500">
          {setupState.currentStep + 1} / {setupState.totalSteps}
        </div>

        <Button
          onClick={handleNext}
          disabled={!setupState.isValid || isLoading}
          className="flex items-center space-x-2"
        >
          <span>
            {setupState.currentStep === setupState.totalSteps - 1 ? 'Finalizar' : 'Próximo'}
          </span>
          {setupState.currentStep === setupState.totalSteps - 1 ? (
            <Check className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
        </Button>
      </div>
    </div>
  );
};

// === STEP COMPONENTS ===

const WelcomeStep: React.FC = () => (
  <Card className="text-center">
    <CardHeader>
      <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
        <Sparkles className="w-8 h-8 text-blue-600" />
      </div>
      <CardTitle className="text-2xl">Bem-vindo ao DataHero4!</CardTitle>
      <CardDescription className="text-lg">
        Vamos configurar seu perfil para oferecer uma experiência personalizada
        com os KPIs mais relevantes para sua função.
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="text-center">
          <User className="w-8 h-8 text-blue-600 mx-auto mb-2" />
          <h3 className="font-medium">Perfil Personalizado</h3>
          <p className="text-sm text-gray-600">Configure seu perfil profissional</p>
        </div>
        <div className="text-center">
          <BarChart3 className="w-8 h-8 text-blue-600 mx-auto mb-2" />
          <h3 className="font-medium">KPIs Relevantes</h3>
          <p className="text-sm text-gray-600">Veja apenas o que importa</p>
        </div>
        <div className="text-center">
          <Settings className="w-8 h-8 text-blue-600 mx-auto mb-2" />
          <h3 className="font-medium">Preferências</h3>
          <p className="text-sm text-gray-600">Personalize sua experiência</p>
        </div>
      </div>
    </CardContent>
  </Card>
);

interface RoleSelectionStepProps {
  selectedProfile: ProfileType | null;
  onSelect: (profile: ProfileType) => void;
}

const RoleSelectionStep: React.FC<RoleSelectionStepProps> = ({ selectedProfile, onSelect }) => (
  <div>
    <div className="text-center mb-6">
      <h2 className="text-xl font-semibold mb-2">Qual é o seu perfil profissional?</h2>
      <p className="text-gray-600">
        Selecione o perfil que melhor descreve sua função para receber recomendações personalizadas.
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Object.values(PROFILE_CHARACTERISTICS).map((profile) => (
        <Card
          key={profile.id}
          className={cn(
            "cursor-pointer transition-all hover:shadow-md",
            selectedProfile === profile.id
              ? "ring-2 ring-blue-600 bg-blue-50"
              : "hover:bg-gray-50"
          )}
          onClick={() => onSelect(profile.id)}
        >
          <CardHeader className="text-center">
            <div className="text-3xl mb-2">{profile.icon}</div>
            <CardTitle className="text-lg">{profile.name}</CardTitle>
            <CardDescription className="text-sm">
              {profile.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Características:</h4>
                <div className="flex flex-wrap gap-1">
                  {profile.keyFeatures.slice(0, 2).map((feature) => (
                    <Badge key={feature} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Atualização:</h4>
                <p className="text-xs text-gray-600">{profile.updateFrequency}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

interface KpiSelectionStepProps {
  selectedKpis: string[];
  onToggle: (kpiId: string) => void;
  profileType: ProfileType | null;
}

const KpiSelectionStep: React.FC<KpiSelectionStepProps> = ({ selectedKpis, onToggle, profileType }) => {
  const recommendedKpis = profileType ? PROFILE_CHARACTERISTICS[profileType].recommendedKpis : [];

  return (
    <div>
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold mb-2">Selecione seus KPIs</h2>
        <p className="text-gray-600">
          Escolha os indicadores mais importantes para seu trabalho. 
          {profileType && ` Recomendamos os KPIs destacados para ${PROFILE_CHARACTERISTICS[profileType].name}.`}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {MOCK_KPI_OPTIONS.map((kpi) => {
          const isSelected = selectedKpis.includes(kpi.id);
          const isRecommended = recommendedKpis.includes(kpi.id);

          return (
            <Card
              key={kpi.id}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                isSelected
                  ? "ring-2 ring-blue-600 bg-blue-50"
                  : "hover:bg-gray-50"
              )}
              onClick={() => onToggle(kpi.id)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-base flex items-center space-x-2">
                      <span>{kpi.name}</span>
                      {isRecommended && (
                        <Badge variant="default" className="text-xs">
                          Recomendado
                        </Badge>
                      )}
                      {kpi.isPriority && (
                        <Badge variant="secondary" className="text-xs">
                          Prioridade
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription className="text-sm mt-1">
                      {kpi.description}
                    </CardDescription>
                  </div>
                  <div className={cn(
                    "w-5 h-5 rounded border-2 flex items-center justify-center ml-2",
                    isSelected
                      ? "bg-blue-600 border-blue-600"
                      : "border-gray-300"
                  )}>
                    {isSelected && <Check className="w-3 h-3 text-white" />}
                  </div>
                </div>
              </CardHeader>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

interface PreferencesStepProps {
  preferences: any;
  onChange: (preferences: any) => void;
}

const PreferencesStep: React.FC<PreferencesStepProps> = ({ preferences, onChange }) => (
  <Card>
    <CardHeader>
      <CardTitle>Preferências de Visualização</CardTitle>
      <CardDescription>
        Configure como você prefere visualizar seus dados
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="text-center text-gray-600">
        <Settings className="w-12 h-12 mx-auto mb-2 text-gray-400" />
        <p>Configurações de preferências serão implementadas na próxima versão.</p>
        <p className="text-sm">Por enquanto, usaremos as configurações padrão.</p>
      </div>
    </CardContent>
  </Card>
);

export default ProfileSetup;
