/**
 * useAlertConfig Hook for DataHero4
 * 
 * Custom React hook for managing alert configuration state and API interactions.
 * Integrates with the KPI Drawer to provide seamless alert management functionality.
 */

import { useState, useEffect, useCallback } from 'react';
import type { 
  AlertConfig, 
  ActiveAlert, 
  UseAlertConfigReturn, 
  AlertConfigForm,
  AlertConfigResponse,
  ActiveAlertResponse 
} from '../../../types/alert';

const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? '' 
  : 'http://localhost:8000';

export const useAlertConfig = (kpiId: string): UseAlertConfigReturn => {
  const [config, setConfig] = useState<AlertConfig | null>(null);
  const [activeAlert, setActiveAlert] = useState<ActiveAlert | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load alert configuration for the specified KPI
   */
  const loadConfig = useCallback(async () => {
    if (!kpiId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/api/alerts/config/${kpiId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data: AlertConfigResponse = await response.json();
        if (data.success && data.config) {
          setConfig(data.config as AlertConfig);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar configuração de alerta';
      setError(errorMessage);
      console.error('Error loading alert config:', err);
    } finally {
      setIsLoading(false);
    }
  }, [kpiId]);

  /**
   * Save alert configuration
   */
  const saveConfig = useCallback(async (formData: AlertConfigForm) => {
    if (!kpiId) return;

    try {
      setIsLoading(true);
      setError(null);

      const requestBody = {
        enabled: formData.enabled,
        warningThreshold: formData.warningThreshold ? parseFloat(formData.warningThreshold) : null,
        criticalThreshold: formData.criticalThreshold ? parseFloat(formData.criticalThreshold) : null,
        notificationChannels: formData.notificationChannels,
      };

      const response = await fetch(`${API_BASE_URL}/api/alerts/config/${kpiId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data: AlertConfigResponse = await response.json();
        if (data.success && data.config) {
          setConfig(data.config as AlertConfig);
          
          // Reload active alert after saving configuration
          await loadActiveAlert();
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao salvar configuração de alerta';
      setError(errorMessage);
      console.error('Error saving alert config:', err);
      throw err; // Re-throw to allow component to handle
    } finally {
      setIsLoading(false);
    }
  }, [kpiId]);

  /**
   * Load active alert for the specified KPI
   */
  const loadActiveAlert = useCallback(async () => {
    if (!kpiId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/alerts/active/${kpiId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data: ActiveAlertResponse = await response.json();
        if (data.success) {
          setActiveAlert(data.alert as ActiveAlert | null);
        }
      } else {
        // If no active alert, that's not an error
        setActiveAlert(null);
      }
    } catch (err) {
      console.error('Error loading active alert:', err);
      setActiveAlert(null);
    }
  }, [kpiId]);

  /**
   * Dismiss the current active alert (client-side only)
   */
  const dismissAlert = useCallback(() => {
    setActiveAlert(null);
  }, []);

  /**
   * Convert AlertConfig to form data for editing
   */
  const configToFormData = useCallback((alertConfig: AlertConfig | null): AlertConfigForm => {
    if (!alertConfig) {
      return {
        enabled: true,
        warningThreshold: '',
        criticalThreshold: '',
        notificationChannels: ['dashboard'],
      };
    }

    return {
      enabled: alertConfig.enabled,
      warningThreshold: alertConfig.warningThreshold?.toString() || '',
      criticalThreshold: alertConfig.criticalThreshold?.toString() || '',
      notificationChannels: alertConfig.notificationChannels,
    };
  }, []);

  /**
   * Validate form data before saving
   */
  const validateFormData = useCallback((formData: AlertConfigForm): string[] => {
    const errors: string[] = [];

    if (formData.enabled) {
      // Check if at least one threshold is set
      if (!formData.warningThreshold && !formData.criticalThreshold) {
        errors.push('Pelo menos um limite (aviso ou crítico) deve ser definido quando alertas estão ativos');
      }

      // Validate numeric values
      if (formData.warningThreshold) {
        const warningValue = parseFloat(formData.warningThreshold);
        if (isNaN(warningValue)) {
          errors.push('Limite de aviso deve ser um número válido');
        }
      }

      if (formData.criticalThreshold) {
        const criticalValue = parseFloat(formData.criticalThreshold);
        if (isNaN(criticalValue)) {
          errors.push('Limite crítico deve ser um número válido');
        }
      }

      // Validate threshold relationship
      if (formData.warningThreshold && formData.criticalThreshold) {
        const warningValue = parseFloat(formData.warningThreshold);
        const criticalValue = parseFloat(formData.criticalThreshold);
        
        if (!isNaN(warningValue) && !isNaN(criticalValue) && warningValue <= criticalValue) {
          errors.push('Limite de aviso deve ser maior que o limite crítico');
        }
      }

      // Validate notification channels
      if (formData.notificationChannels.length === 0) {
        errors.push('Pelo menos um canal de notificação deve ser selecionado');
      }
    }

    return errors;
  }, []);

  // Load initial data when kpiId changes
  useEffect(() => {
    if (kpiId) {
      loadConfig();
      loadActiveAlert();
    }
  }, [kpiId, loadConfig, loadActiveAlert]);

  // Set up polling for active alerts (every 30 seconds)
  useEffect(() => {
    if (!kpiId) return;

    const interval = setInterval(() => {
      loadActiveAlert();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [kpiId, loadActiveAlert]);

  return {
    config,
    activeAlert,
    isLoading,
    error,
    saveConfig,
    loadConfig,
    loadActiveAlert,
    dismissAlert,
    configToFormData,
    validateFormData,
  };
};

// Export additional utility functions
export const formatAlertThreshold = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '';
  return value.toLocaleString('pt-BR');
};

export const formatAlertTimeAgo = (triggeredAt: string): string => {
  const now = new Date();
  const triggered = new Date(triggeredAt);
  const diffInMinutes = Math.floor((now.getTime() - triggered.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'há poucos segundos';
  if (diffInMinutes < 60) return `há ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  return `há ${diffInDays} dia${diffInDays > 1 ? 's' : ''}`;
};
