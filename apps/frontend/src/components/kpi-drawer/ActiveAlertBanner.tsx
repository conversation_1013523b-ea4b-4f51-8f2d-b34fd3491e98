/**
 * ActiveAlertBanner Component for DataHero4
 * 
 * Displays active alerts at the top of the KPI Drawer with smooth animations
 * and contextual information about the alert status.
 */

import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { AlertTriangle, X, Clock } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import type { ActiveAlertBannerProps } from '../../types/alert';
import { formatAlertTimeAgo, formatAlertThreshold } from './hooks/useAlertConfig';

export const ActiveAlertBanner: React.FC<ActiveAlertBannerProps> = ({
  alert,
  onDismiss
}) => {
  /**
   * Get alert-specific styling based on alert type
   */
  const getAlertStyles = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? 'border-red-400 bg-red-50 text-red-800'
      : 'border-amber-400 bg-amber-50 text-amber-800';
  };

  /**
   * Get appropriate icon for alert type
   */
  const getIcon = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? <AlertTriangle className="w-5 h-5 text-red-500" />
      : <AlertTriangle className="w-5 h-5 text-amber-500" />;
  };

  /**
   * Get alert type label in Portuguese
   */
  const getAlertTypeLabel = (type: 'warning' | 'critical') => {
    return type === 'critical' ? 'Crítico' : 'de Aviso';
  };

  /**
   * Get threshold comparison text
   */
  const getThresholdText = (type: 'warning' | 'critical', currentValue: number, threshold: number) => {
    const comparison = currentValue < threshold ? 'abaixo' : 'acima';
    const limitType = type === 'critical' ? 'limite crítico' : 'limite de aviso';
    
    return `${comparison} do ${limitType}`;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20, height: 0 }}
        animate={{ opacity: 1, y: 0, height: 'auto' }}
        exit={{ opacity: 0, y: -20, height: 0 }}
        transition={{ 
          duration: 0.3,
          ease: 'easeInOut'
        }}
        className={cn(
          "border-l-4 p-4 mb-4 rounded-r-lg shadow-sm",
          getAlertStyles(alert.type)
        )}
        role="alert"
        aria-live="polite"
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            {/* Alert Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getIcon(alert.type)}
            </div>
            
            {/* Alert Content */}
            <div className="flex-1 min-w-0">
              {/* Alert Title */}
              <p className="text-sm font-medium leading-5">
                Alerta {getAlertTypeLabel(alert.type)}: {alert.kpiName}
              </p>
              
              {/* Alert Details */}
              <p className="text-xs mt-1 leading-4">
                Valor atual: <strong>{formatAlertThreshold(alert.currentValue)}</strong> está{' '}
                {getThresholdText(alert.type, alert.currentValue, alert.threshold)} de{' '}
                <strong>{formatAlertThreshold(alert.threshold)}</strong>
              </p>
              
              {/* Alert Timing */}
              <div className="flex items-center gap-1 mt-2 text-xs opacity-75">
                <Clock className="w-3 h-3 flex-shrink-0" />
                <span>Disparado {formatAlertTimeAgo(alert.triggeredAt)}</span>
              </div>
              
              {/* Custom Message (if available) */}
              {alert.message && alert.message !== `${alert.kpiName} alert triggered` && (
                <p className="text-xs mt-2 italic opacity-90">
                  {alert.message}
                </p>
              )}
            </div>
          </div>
          
          {/* Dismiss Button */}
          {onDismiss && (
            <div className="flex-shrink-0 ml-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className={cn(
                  "h-6 w-6 p-0 hover:bg-white/20 transition-colors",
                  alert.type === 'critical' 
                    ? "hover:bg-red-100 text-red-600" 
                    : "hover:bg-amber-100 text-amber-600"
                )}
                aria-label="Dispensar alerta"
                title="Dispensar alerta (o alerta continuará ativo até que o valor seja corrigido)"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          )}
        </div>
        
        {/* Progress indicator for critical alerts */}
        {alert.type === 'critical' && (
          <motion.div
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-3 h-1 bg-red-200 rounded-full overflow-hidden"
          >
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: '0%' }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatType: 'reverse',
                ease: 'easeInOut'
              }}
              className="h-full w-1/3 bg-red-400 rounded-full"
            />
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

/**
 * Compact version of the alert banner for smaller spaces
 */
export const CompactAlertBanner: React.FC<ActiveAlertBannerProps> = ({
  alert,
  onDismiss
}) => {
  const getAlertStyles = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? 'border-red-400 bg-red-50 text-red-700'
      : 'border-amber-400 bg-amber-50 text-amber-700';
  };

  const getIcon = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? <AlertTriangle className="w-4 h-4 text-red-500" />
      : <AlertTriangle className="w-4 h-4 text-amber-500" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "border-l-3 p-3 mb-3 rounded-r-md",
        getAlertStyles(alert.type)
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {getIcon(alert.type)}
          <div className="flex-1 min-w-0">
            <p className="text-xs font-medium truncate">
              {alert.kpiName}: {formatAlertThreshold(alert.currentValue)}
            </p>
            <p className="text-xs opacity-75">
              {formatAlertTimeAgo(alert.triggeredAt)}
            </p>
          </div>
        </div>
        
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-5 w-5 p-0 hover:bg-white/20 ml-2"
            aria-label="Dispensar alerta"
          >
            <X className="w-2.5 h-2.5" />
          </Button>
        )}
      </div>
    </motion.div>
  );
};

export default ActiveAlertBanner;
