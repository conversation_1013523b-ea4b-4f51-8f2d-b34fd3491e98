/**
 * AlertConfigModal Component for DataHero4
 * 
 * Modal dialog for configuring KPI alert thresholds and notification preferences.
 * Uses Radix UI for accessibility and proper modal behavior.
 */

import React, { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { motion, AnimatePresence } from 'motion/react';
import { Bell, X, AlertTriangle, Info, Save, Loader2 } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';
import { Checkbox } from '../ui/checkbox';
import { Separator } from '../ui/separator';
import { useAlertConfig } from './hooks/useAlertConfig';
import type { AlertConfigModalProps, AlertConfigForm, NotificationChannel } from '../../types/alert';

export const AlertConfigModal: React.FC<AlertConfigModalProps> = ({
  kpiId,
  kpiName,
  isOpen,
  onClose
}) => {
  const { 
    config, 
    saveConfig, 
    isLoading, 
    error, 
    configToFormData, 
    validateFormData 
  } = useAlertConfig(kpiId);

  const [formData, setFormData] = useState<AlertConfigForm>({
    enabled: true,
    warningThreshold: '',
    criticalThreshold: '',
    notificationChannels: ['dashboard']
  });
  
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  // Update form data when config loads
  useEffect(() => {
    if (config) {
      setFormData(configToFormData(config));
    }
  }, [config, configToFormData]);

  // Clear validation errors when form data changes
  useEffect(() => {
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  }, [formData]);

  /**
   * Handle form submission
   */
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Validate form data
    const errors = validateFormData(formData);
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    try {
      setIsSaving(true);
      setValidationErrors([]);
      
      await saveConfig(formData);
      
      // Close modal on successful save
      onClose();
    } catch (err) {
      // Error is already handled by the hook
      console.error('Failed to save alert configuration:', err);
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Handle notification channel toggle
   */
  const handleChannelToggle = (channel: NotificationChannel, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      notificationChannels: checked
        ? [...prev.notificationChannels, channel]
        : prev.notificationChannels.filter(c => c !== channel)
    }));
  };

  /**
   * Reset form to default values
   */
  const handleReset = () => {
    if (config) {
      setFormData(configToFormData(config));
    }
    setValidationErrors([]);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay asChild>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
          />
        </Dialog.Overlay>
        
        <Dialog.Content asChild>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
          >
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-hidden flex flex-col"
          >
            {/* Header */}
            <div className="p-6 pb-4">
              <Dialog.Title className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                <Bell className="w-5 h-5 text-blue-600" />
                Configurar Alertas
              </Dialog.Title>
              
              <Dialog.Description className="text-sm text-gray-600">
                Configure os limites de alerta para <strong>{kpiName}</strong>
              </Dialog.Description>
              
              <Dialog.Close asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-4 h-8 w-8"
                  aria-label="Fechar modal"
                >
                  <X className="w-4 h-4" />
                </Button>
              </Dialog.Close>
            </div>

            <Separator />

            {/* Form Content */}
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Enable/Disable Toggle */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-gray-700">
                      Alertas Ativos
                    </Label>
                    <p className="text-xs text-gray-500">
                      Ativar notificações para este KPI
                    </p>
                  </div>
                  <Switch
                    checked={formData.enabled}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, enabled: checked }))
                    }
                  />
                </div>

                {/* Configuration Fields (only when enabled) */}
                <AnimatePresence>
                  {formData.enabled && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      {/* Warning Threshold */}
                      <div className="space-y-2">
                        <Label htmlFor="warningThreshold" className="text-sm font-medium text-gray-700">
                          Limite de Aviso
                        </Label>
                        <Input
                          id="warningThreshold"
                          type="number"
                          placeholder="Ex: 1000000"
                          value={formData.warningThreshold}
                          onChange={(e) => setFormData(prev => ({ 
                            ...prev, 
                            warningThreshold: e.target.value 
                          }))}
                          className="w-full"
                        />
                        <p className="text-xs text-gray-500 flex items-start gap-1">
                          <Info className="w-3 h-3 mt-0.5 flex-shrink-0" />
                          Alerta amarelo quando valor for menor que este limite
                        </p>
                      </div>

                      {/* Critical Threshold */}
                      <div className="space-y-2">
                        <Label htmlFor="criticalThreshold" className="text-sm font-medium text-gray-700">
                          Limite Crítico
                        </Label>
                        <Input
                          id="criticalThreshold"
                          type="number"
                          placeholder="Ex: 500000"
                          value={formData.criticalThreshold}
                          onChange={(e) => setFormData(prev => ({ 
                            ...prev, 
                            criticalThreshold: e.target.value 
                          }))}
                          className="w-full"
                        />
                        <p className="text-xs text-gray-500 flex items-start gap-1">
                          <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0 text-red-500" />
                          Alerta vermelho quando valor for menor que este limite
                        </p>
                      </div>

                      {/* Notification Channels */}
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">
                          Canais de Notificação
                        </Label>
                        <div className="space-y-2">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <Checkbox
                              checked={formData.notificationChannels.includes('dashboard')}
                              onCheckedChange={(checked) => 
                                handleChannelToggle('dashboard', checked as boolean)
                              }
                            />
                            <span className="text-sm text-gray-700">Dashboard</span>
                            <span className="text-xs text-gray-500">(Visual no painel)</span>
                          </label>
                          
                          <label className="flex items-center gap-2 cursor-pointer">
                            <Checkbox
                              checked={formData.notificationChannels.includes('email')}
                              onCheckedChange={(checked) => 
                                handleChannelToggle('email', checked as boolean)
                              }
                            />
                            <span className="text-sm text-gray-700">Email</span>
                            <span className="text-xs text-gray-500">(Notificação por email)</span>
                          </label>
                          
                          <label className="flex items-center gap-2 cursor-pointer opacity-50">
                            <Checkbox disabled />
                            <span className="text-sm text-gray-500">Slack</span>
                            <span className="text-xs text-gray-400">(Em breve)</span>
                          </label>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Error Messages */}
                {(validationErrors.length > 0 || error) && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div className="space-y-1">
                        {validationErrors.map((err, index) => (
                          <p key={index} className="text-xs text-red-700">{err}</p>
                        ))}
                        {error && (
                          <p className="text-xs text-red-700">{error}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>

            <Separator />

            {/* Footer */}
            <div className="p-6 pt-4">
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={handleReset}
                  disabled={isSaving}
                  type="button"
                >
                  Resetar
                </Button>
                
                <Dialog.Close asChild>
                  <Button
                    variant="ghost"
                    disabled={isSaving}
                    type="button"
                  >
                    Cancelar
                  </Button>
                </Dialog.Close>
                
                <Button
                  onClick={handleSubmit}
                  disabled={isSaving || isLoading}
                  className="min-w-[120px]"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Salvar
                    </>
                  )}
                </Button>
              </div>
            </div>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default AlertConfigModal;
