/**
 * Drawer Timeframe Selector Component for DataHero4
 * =================================================
 * 
 * Timeframe selector component specifically designed for the KPI drawer.
 * Provides the same timeframe options as the dashboard controls but with
 * drawer-specific styling and behavior.
 * 
 * Features:
 * - Same options as dashboard controls ('1d', 'week', 'month', 'quarter')
 * - Visual indication of current selection
 * - Smooth transitions between timeframes
 * - Integration with drawer state management
 * - Real-time data updates when timeframe changes
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import React from 'react';
import { Clock, Calendar, TrendingUp } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Types for timeframe options
export type TimeframeOption = '1d' | 'week' | 'month' | 'quarter';

export interface DrawerTimeframeSelectorProps {
  /** Current selected timeframe */
  value: TimeframeOption;
  /** Callback when timeframe changes */
  onChange: (timeframe: TimeframeOption) => void;
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Whether data is currently loading */
  isLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Show compact version */
  compact?: boolean;
}

// Timeframe configuration with labels and descriptions
const TIMEFRAME_OPTIONS = [
  {
    value: '1d' as TimeframeOption,
    label: 'Hoje',
    description: 'Últimas 24 horas',
    icon: Clock,
    color: 'bg-blue-50 text-blue-700 border-blue-200'
  },
  {
    value: 'week' as TimeframeOption,
    label: '7 dias',
    description: 'Última semana',
    icon: Calendar,
    color: 'bg-green-50 text-green-700 border-green-200'
  },
  {
    value: 'month' as TimeframeOption,
    label: '30 dias',
    description: 'Último mês',
    icon: TrendingUp,
    color: 'bg-orange-50 text-orange-700 border-orange-200'
  },
  {
    value: 'quarter' as TimeframeOption,
    label: '3 meses',
    description: 'Último trimestre',
    icon: TrendingUp,
    color: 'bg-purple-50 text-purple-700 border-purple-200'
  }
] as const;

/**
 * Get timeframe option configuration by value
 */
const getTimeframeOption = (value: TimeframeOption) => {
  return TIMEFRAME_OPTIONS.find(option => option.value === value) || TIMEFRAME_OPTIONS[1];
};

/**
 * Drawer Timeframe Selector Component
 */
export const DrawerTimeframeSelector: React.FC<DrawerTimeframeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  isLoading = false,
  className,
  compact = false
}) => {
  const currentOption = getTimeframeOption(value);

  // Compact version for smaller spaces
  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Clock className="w-4 h-4 text-muted-foreground" />
        <Select 
          value={value} 
          onValueChange={onChange}
          disabled={disabled || isLoading}
        >
          <SelectTrigger className="w-24 h-8 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {TIMEFRAME_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  // Simplified version with circular buttons
  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4 text-muted-foreground" />
        <h3 className="text-sm font-medium text-foreground">
          Período de Análise
        </h3>
      </div>

      {/* Circular Button Group */}
      <div className="flex items-center justify-between">
        {TIMEFRAME_OPTIONS.map((option) => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            disabled={disabled || isLoading}
            className={cn(
              "w-16 h-8 text-xs rounded-full border transition-all duration-200",
              "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
              option.value === value
                ? "bg-blue-600 border-blue-600 text-white font-medium shadow-sm"
                : "bg-white border-gray-200 text-gray-700 hover:border-gray-300",
              (disabled || isLoading) && "opacity-50 cursor-not-allowed"
            )}
          >
            {option.label}
          </button>
        ))}
      </div>

      {/* Simple help text */}
      <p className="text-xs text-muted-foreground">
        Os dados serão atualizados automaticamente quando você alterar o período.
      </p>
    </div>
  );
};

/**
 * Hook for managing drawer timeframe state
 */
export const useDrawerTimeframe = (initialTimeframe: TimeframeOption = 'week') => {
  const [timeframe, setTimeframe] = React.useState<TimeframeOption>(initialTimeframe);
  const [isChanging, setIsChanging] = React.useState(false);

  const handleTimeframeChange = React.useCallback(async (newTimeframe: TimeframeOption) => {
    if (newTimeframe === timeframe) return;

    setIsChanging(true);
    
    try {
      // Simulate async operation (API call, data refresh, etc.)
      await new Promise(resolve => setTimeout(resolve, 500));
      setTimeframe(newTimeframe);
    } finally {
      setIsChanging(false);
    }
  }, [timeframe]);

  return {
    timeframe,
    setTimeframe: handleTimeframeChange,
    isChanging
  };
};

export default DrawerTimeframeSelector;
