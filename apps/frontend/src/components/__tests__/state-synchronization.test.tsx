/**
 * State Synchronization Integration Tests
 * =======================================
 * 
 * Integration tests focused on state synchronization between
 * dashboard and drawer components, event flow, and data consistency.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderHook } from '@testing-library/react';
import { useDrawerFilters } from '../../hooks/useDrawerFilters';
import { useKPIDrawer } from '../../hooks/useKPIDrawer';
import { kpiEvents } from '../../lib/events';

// Mock debounce to execute immediately
vi.mock('use-debounce', () => ({
  useDebouncedCallback: (fn: any) => fn
}));

describe('State Synchronization Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    kpiEvents.removeAllListeners();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Dashboard-Drawer Filter Synchronization', () => {
    it('should synchronize filters bidirectionally', async () => {
      // Render drawer filters hook
      const { result: drawerResult } = renderHook(() => 
        useDrawerFilters({
          syncWithDashboard: true,
          emitEvents: true,
          kpiId: 'test-kpi'
        })
      );

      // Mock dashboard filter change
      const dashboardFilters = {
        timeframe: 'month' as const,
        currency: 'usd' as const
      };

      // Simulate dashboard filter change event
      act(() => {
        kpiEvents.emit('dashboard:filters-changed', {
          filters: dashboardFilters,
          source: 'dashboard',
          timestamp: new Date().toISOString()
        });
      });

      // Drawer filters should update
      await waitFor(() => {
        expect(drawerResult.current.filters).toEqual(dashboardFilters);
      });
    });

    it('should emit events when drawer filters change', async () => {
      const mockEventListener = vi.fn();
      kpiEvents.on('drawer:filters-changed', mockEventListener);

      const { result } = renderHook(() => 
        useDrawerFilters({
          emitEvents: true,
          kpiId: 'test-kpi'
        })
      );

      // Change timeframe in drawer
      act(() => {
        result.current.setTimeframe('quarter');
      });

      // Should emit drawer filters changed event
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          filters: { timeframe: 'quarter', currency: 'all' },
          kpiId: 'test-kpi',
          source: 'timeframe'
        })
      );
    });

    it('should prevent infinite loops in synchronization', async () => {
      const mockEventListener = vi.fn();
      kpiEvents.on('drawer:filters-changed', mockEventListener);

      const { result } = renderHook(() => 
        useDrawerFilters({
          syncWithDashboard: true,
          emitEvents: true,
          kpiId: 'test-kpi'
        })
      );

      // Simulate dashboard change that originated from drawer
      act(() => {
        kpiEvents.emit('dashboard:filters-changed', {
          filters: { timeframe: 'month', currency: 'eur' },
          source: 'drawer', // This should be ignored
          timestamp: new Date().toISOString()
        });
      });

      // Should not emit additional drawer events
      expect(mockEventListener).not.toHaveBeenCalled();
    });
  });

  describe('Card Selection State Synchronization', () => {
    it('should synchronize card selection with drawer state', async () => {
      const { result } = renderHook(() => useKPIDrawer());

      // Initially no card selected
      expect(result.current.selectedCardId).toBeNull();
      expect(result.current.isCardSelected('test-kpi')).toBe(false);

      // Simulate KPI selection
      act(() => {
        kpiEvents.selectKPI({
          kpiId: 'test-kpi',
          element: document.createElement('div'),
          cardRect: new DOMRect(0, 0, 100, 100)
        });
      });

      // Card should be selected and drawer should be open
      expect(result.current.selectedCardId).toBe('test-kpi');
      expect(result.current.isCardSelected('test-kpi')).toBe(true);
      expect(result.current.isOpen).toBe(true);
    });

    it('should emit card selection events', async () => {
      const mockEventListener = vi.fn();
      kpiEvents.on('card:selection-changed', mockEventListener);

      renderHook(() => useKPIDrawer());

      // Select KPI
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      });

      // Should emit selection event
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          kpiId: 'test-kpi',
          selected: true
        })
      );

      // Close drawer
      act(() => {
        kpiEvents.closeDrawer();
      });

      // Should emit deselection event
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          kpiId: 'test-kpi',
          selected: false
        })
      );
    });

    it('should handle multiple card selections correctly', async () => {
      const { result } = renderHook(() => useKPIDrawer());

      // Select first KPI
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'kpi-1' });
      });

      expect(result.current.selectedCardId).toBe('kpi-1');
      expect(result.current.isCardSelected('kpi-1')).toBe(true);
      expect(result.current.isCardSelected('kpi-2')).toBe(false);

      // Select second KPI (should replace first)
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'kpi-2' });
      });

      expect(result.current.selectedCardId).toBe('kpi-2');
      expect(result.current.isCardSelected('kpi-1')).toBe(false);
      expect(result.current.isCardSelected('kpi-2')).toBe(true);
    });
  });

  describe('Event Flow Integration', () => {
    it('should maintain correct event order', async () => {
      const eventOrder: string[] = [];
      
      // Track event order
      kpiEvents.on('kpi:selected', () => eventOrder.push('kpi:selected'));
      kpiEvents.on('card:selection-changed', () => eventOrder.push('card:selection-changed'));
      kpiEvents.on('drawer:timeframe-changed', () => eventOrder.push('drawer:timeframe-changed'));

      const { result: drawerResult } = renderHook(() => useKPIDrawer());
      const { result: filtersResult } = renderHook(() => 
        useDrawerFilters({ emitEvents: true, kpiId: 'test-kpi' })
      );

      // Select KPI
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      });

      // Change timeframe
      act(() => {
        filtersResult.current.setTimeframe('month');
      });

      // Events should be in correct order
      expect(eventOrder).toEqual([
        'kpi:selected',
        'card:selection-changed',
        'drawer:timeframe-changed'
      ]);
    });

    it('should handle concurrent events correctly', async () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();
      
      kpiEvents.on('drawer:filters-changed', mockListener1);
      kpiEvents.on('drawer:timeframe-changed', mockListener2);

      const { result } = renderHook(() => 
        useDrawerFilters({ emitEvents: true, kpiId: 'test-kpi' })
      );

      // Trigger multiple events simultaneously
      act(() => {
        result.current.setTimeframe('month');
        result.current.setCurrency('usd');
      });

      // Both events should be handled
      expect(mockListener1).toHaveBeenCalledTimes(2);
      expect(mockListener2).toHaveBeenCalledTimes(1);
    });
  });

  describe('State Consistency', () => {
    it('should maintain state consistency across components', async () => {
      const { result: drawerResult } = renderHook(() => useKPIDrawer());
      const { result: filtersResult } = renderHook(() => 
        useDrawerFilters({ 
          syncWithDashboard: true,
          emitEvents: true,
          kpiId: 'test-kpi'
        })
      );

      // Initial state should be consistent
      expect(drawerResult.current.isOpen).toBe(false);
      expect(drawerResult.current.selectedCardId).toBeNull();
      expect(filtersResult.current.filters.timeframe).toBe('week');

      // Select KPI
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      });

      // State should be consistent
      expect(drawerResult.current.isOpen).toBe(true);
      expect(drawerResult.current.selectedCardId).toBe('test-kpi');

      // Change filters
      act(() => {
        filtersResult.current.setTimeframe('month');
      });

      // Filter state should be updated
      expect(filtersResult.current.filters.timeframe).toBe('month');

      // Close drawer
      act(() => {
        kpiEvents.closeDrawer();
      });

      // State should be reset consistently
      expect(drawerResult.current.isOpen).toBe(false);
      expect(drawerResult.current.selectedCardId).toBeNull();
    });

    it('should handle state recovery after errors', async () => {
      const { result } = renderHook(() => useKPIDrawer());

      // Select KPI
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      });

      expect(result.current.isOpen).toBe(true);

      // Simulate error scenario - force close
      act(() => {
        kpiEvents.closeDrawer();
      });

      // State should recover
      expect(result.current.isOpen).toBe(false);
      expect(result.current.selectedCardId).toBeNull();

      // Should be able to select again
      act(() => {
        kpiEvents.selectKPI({ kpiId: 'recovery-kpi' });
      });

      expect(result.current.isOpen).toBe(true);
      expect(result.current.selectedCardId).toBe('recovery-kpi');
    });
  });

  describe('Performance and Memory', () => {
    it('should not create memory leaks with event listeners', () => {
      const initialListenerCount = kpiEvents.listenerCount('kpi:selected');

      // Render and unmount multiple times
      for (let i = 0; i < 5; i++) {
        const { unmount } = renderHook(() => useKPIDrawer());
        unmount();
      }

      // Listener count should not increase
      expect(kpiEvents.listenerCount('kpi:selected')).toBeLessThanOrEqual(initialListenerCount + 1);
    });

    it('should handle rapid state changes efficiently', async () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ emitEvents: true, kpiId: 'test-kpi' })
      );

      const startTime = performance.now();

      // Perform many rapid state changes
      act(() => {
        for (let i = 0; i < 100; i++) {
          result.current.setTimeframe(i % 2 === 0 ? 'week' : 'month');
        }
      });

      const endTime = performance.now();

      // Should complete quickly (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);

      // Final state should be correct
      expect(result.current.filters.timeframe).toBe('month');
    });
  });
});
