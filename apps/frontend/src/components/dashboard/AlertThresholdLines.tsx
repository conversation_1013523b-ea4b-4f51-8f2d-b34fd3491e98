import React from 'react';
import { motion } from 'framer-motion';

// Simplified alert config interface
interface SimpleAlertConfig {
  enabled: boolean;
  warningThreshold?: number;
  criticalThreshold?: number;
}

interface AlertThresholdLinesProps {
  alertConfig?: SimpleAlertConfig | null;
  chartData: any[];
  yAxisKey: string;
  isVisible: boolean;
  chartHeight?: number;
  chartMargin?: { top: number; right: number; left: number; bottom: number };
}

export const AlertThresholdLines: React.FC<AlertThresholdLinesProps> = ({
  alertConfig,
  chartData,
  yAxisKey,
  isVisible,
  chartHeight = 80,
  chartMargin = { top: 4, right: 2, left: 2, bottom: 2 }
}) => {
  if (!isVisible || !alertConfig?.enabled || !chartData?.length) {
    return null;
  }

  // Calculate Y-axis domain from chart data
  const values = chartData.map(item => item[yAxisKey]).filter(val => typeof val === 'number');
  if (values.length === 0) return null;

  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const valueRange = maxValue - minValue;
  
  // Add padding to the range for better visualization
  const paddedMin = minValue - (valueRange * 0.1);
  const paddedMax = maxValue + (valueRange * 0.1);
  const paddedRange = paddedMax - paddedMin;

  // Calculate positions for threshold lines
  const getLinePosition = (threshold: number): number => {
    if (paddedRange === 0) return 50; // Center if no range

    const normalizedPosition = (threshold - paddedMin) / paddedRange;
    // Convert to percentage from top (SVG coordinate system)
    const percentageFromTop = (1 - normalizedPosition) * 100;

    // Apply chart margins
    const availableHeight = chartHeight - chartMargin.top - chartMargin.bottom;
    const marginTopPercentage = (chartMargin.top / chartHeight) * 100;
    const marginBottomPercentage = (chartMargin.bottom / chartHeight) * 100;

    const finalPosition = marginTopPercentage + (percentageFromTop * (100 - marginTopPercentage - marginBottomPercentage) / 100);

    // Clamp position to visible area (with some tolerance for edge cases)
    const clampedPosition = Math.max(marginTopPercentage, Math.min(100 - marginBottomPercentage, finalPosition));

    return clampedPosition;
  };

  const thresholdLines = [];

  // Warning threshold line (yellow/amber)
  if (alertConfig.warningThreshold !== undefined && alertConfig.warningThreshold !== null) {
    const warningPosition = getLinePosition(alertConfig.warningThreshold);
    
    // Only show line if it's within visible range
    if (warningPosition >= 0 && warningPosition <= 100) {
      thresholdLines.push({
        id: 'warning',
        position: warningPosition,
        color: '#F59E0B', // amber-500
        strokeColor: '#F59E0B',
        threshold: alertConfig.warningThreshold,
        type: 'warning' as const
      });
    }
  }

  // Critical threshold line (red)
  if (alertConfig.criticalThreshold !== undefined && alertConfig.criticalThreshold !== null) {
    const criticalPosition = getLinePosition(alertConfig.criticalThreshold);
    
    // Only show line if it's within visible range
    if (criticalPosition >= 0 && criticalPosition <= 100) {
      thresholdLines.push({
        id: 'critical',
        position: criticalPosition,
        color: '#EF4444', // red-500
        strokeColor: '#EF4444',
        threshold: alertConfig.criticalThreshold,
        type: 'critical' as const
      });
    }
  }

  if (thresholdLines.length === 0) return null;

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      <svg
        width="100%"
        height="100%"
        className="absolute inset-0"
        style={{ overflow: 'visible' }}
      >
        {thresholdLines.map((line) => (
          <motion.g
            key={line.id}
            initial={{ opacity: 0, scaleX: 0 }}
            animate={{ opacity: 1, scaleX: 1 }}
            exit={{ opacity: 0, scaleX: 0 }}
            transition={{ 
              duration: 0.4,
              ease: "easeOut",
              delay: line.type === 'warning' ? 0.1 : 0.2
            }}
          >
            {/* Main threshold line */}
            <line
              x1="2%"
              y1={`${line.position}%`}
              x2="98%"
              y2={`${line.position}%`}
              stroke={line.strokeColor}
              strokeWidth={1.5}
              strokeDasharray="4 3"
              opacity={0.8}
            />
            
            {/* Subtle glow effect */}
            <line
              x1="2%"
              y1={`${line.position}%`}
              x2="98%"
              y2={`${line.position}%`}
              stroke={line.strokeColor}
              strokeWidth={3}
              opacity={0.2}
              filter="blur(1px)"
            />
            
            {/* Threshold label (only show on hover or when focused) */}
            <g className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <rect
                x="85%"
                y={`${Math.max(5, Math.min(95, line.position - 2))}%`}
                width="12%"
                height="4%"
                fill={line.color}
                fillOpacity={0.9}
                rx={2}
              />
              <text
                x="91%"
                y={`${Math.max(7, Math.min(97, line.position + 0.5))}%`}
                textAnchor="middle"
                dominantBaseline="middle"
                fill="white"
                fontSize="10"
                fontWeight="500"
              >
                {line.threshold.toLocaleString('pt-BR', { 
                  notation: 'compact',
                  maximumFractionDigits: 1 
                })}
              </text>
            </g>
          </motion.g>
        ))}
      </svg>
    </div>
  );
};

export default AlertThresholdLines;
