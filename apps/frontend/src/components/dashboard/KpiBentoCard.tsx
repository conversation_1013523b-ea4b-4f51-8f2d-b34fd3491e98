import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Star,
  AlertCircle
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid
} from 'recharts';
import { KpiData, DashboardFilters } from '@/types/dashboard';
import { cn } from '@/lib/utils';
import { kpiEvents } from '@/lib/events';
import { formatKpiValue, formatChangePercent, getTrendColorClass } from '@/lib/formatters';
import { useKpiAlerts } from '@/hooks/useKpiAlerts';
import AlertThresholdLines from './AlertThresholdLines';

interface KpiBentoCardProps {
  kpi: KpiData;
  filters: DashboardFilters;
  onRemoveKpi?: (kpiId: string) => void;
  isLarge?: boolean;
  isSelected?: boolean;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiBentoCard: React.FC<KpiBentoCardProps> = ({
  kpi,
  filters,
  onRemoveKpi,
  isLarge = false,
  isSelected = false,
  periodData
}) => {

  const [isHovered, setIsHovered] = useState(false);

  // Alert management - using real alert configurations from backend
  const {
    loadAlertConfig,
    getAlertConfig,
    shouldShowAlertLines,
    shouldShowAlertLinesInDrawer
  } = useKpiAlerts();

  // Check if KPI is favorited/prioritized
  const isFavorited = kpi.is_priority || kpi.isPriority || false;

  // Load alert configuration for this KPI on mount and when KPI changes
  useEffect(() => {
    if (kpi.id) {
      loadAlertConfig(kpi.id);
    }
  }, [kpi.id, loadAlertConfig]);

  // Get real alert configuration from backend (no mocks)
  const alertConfig = getAlertConfig(kpi.id);

  // Determine if alert lines should be shown with differentiated logic
  // Dashboard: only show on favorited KPIs with alerts (business rule)
  // Drawer: show on any KPI with alerts configured (user experience)
  const showAlertLines = isSelected ?
    shouldShowAlertLinesInDrawer(kpi.id) :  // Drawer: any KPI with alerts
    shouldShowAlertLines(kpi.id, isFavorited);  // Dashboard: only favorited KPIs

  console.log(`🔍 KPI ${kpi.id} alert status:`, {
    isFavorited,
    isSelected,
    alertConfig,
    showAlertLines,
    currentValue: kpi.currentValue
  });

  const formatValue = (value: number) => {
    return formatKpiValue(value, kpi.format_type || kpi.format || 'number', filters);
  };

  // Function to determine if KPI is in alert state based on real thresholds
  const getAlertState = () => {
    if (!alertConfig?.enabled || !kpi.currentValue) return null;

    const value = typeof kpi.currentValue === 'number' ? kpi.currentValue : parseFloat(kpi.currentValue);
    if (isNaN(value)) return null;

    // Check critical threshold first (more severe)
    if (alertConfig.criticalThreshold !== undefined && alertConfig.criticalThreshold !== null) {
      if (value <= alertConfig.criticalThreshold) {
        return { type: 'critical', isActive: true };
      }
    }

    // Check warning threshold
    if (alertConfig.warningThreshold !== undefined && alertConfig.warningThreshold !== null) {
      if (value <= alertConfig.warningThreshold) {
        return { type: 'warning', isActive: true };
      }
    }

    return null;
  };

  // Function to get card styles based on state (preserves alert borders during hover)
  const getCardStyles = (isSelected: boolean, isHovered: boolean) => {
    const baseStyles = "h-full relative overflow-hidden transition-all duration-500 bg-white cursor-pointer";

    // Selected state styles
    if (isSelected) {
      return cn(
        baseStyles,
        "border-2 border-blue-500 shadow-xl ring-2 ring-blue-100",
        "transform scale-[1.02]",
        "bg-blue-50/30"
      );
    }

    // Default styles with enhanced alert logic using REAL alert configurations
    let borderClass = "border-gray-200";
    let ringClass = "";
    let shadowClass = "hover:shadow-xl";

    // Get real alert state from actual thresholds (NO MOCKS)
    const alertState = getAlertState();

    // Alert styling - prioritize active alerts (don't let hover override)
    if (alertState?.isActive) {
      if (alertState.type === 'critical') {
        borderClass = "border-red-400 border-2";
        ringClass = "ring-2 ring-red-100";
        // Keep alert styling even on hover - this is the key fix
        shadowClass = isHovered ? "shadow-xl" : "hover:shadow-xl";
      } else if (alertState.type === 'warning') {
        borderClass = "border-amber-400 border-2";
        ringClass = "ring-2 ring-amber-100";
        // Keep alert styling even on hover - this is the key fix
        shadowClass = isHovered ? "shadow-xl" : "hover:shadow-xl";
      }
    } else if (kpi.is_priority || kpi.isPriority) {
      // Priority cards get a subtle border when no active alert
      ringClass = "ring-2 ring-amber-100";
      shadowClass = isHovered ? "shadow-xl" : "hover:shadow-xl";
    } else {
      // Regular hover behavior for non-alert cards
      shadowClass = isHovered ? "shadow-xl" : "hover:shadow-xl";
    }

    return cn(
      baseStyles,
      shadowClass,
      borderClass,
      ringClass
    );
  };

  const getTrendIcon = () => {
    if (!kpi.changePercent) return null;

    const Icon = kpi.changePercent > 0 ? TrendingUp : TrendingDown;
    const color = getTrendColorClass(kpi.changePercent);

    return (
      <div className={cn("flex items-center gap-1", color)}>
        <Icon className="w-4 h-4" />
        <span className="text-sm font-medium">
          {formatChangePercent(kpi.changePercent)}
        </span>
      </div>
    );
  };

  const renderChart = () => {
    if (!kpi.chartData || kpi.chartData.length === 0) {
      return (
        <div className="h-full flex items-center justify-center text-gray-400 text-sm">
          Sem dados históricos
        </div>
      );
    }

    // Define gradient ID for this specific chart
    const gradientId = `gradient-${kpi.id}`;

    // Custom tooltip component with light background
    const CustomTooltip = ({ active, payload, label }: any) => {
      if (active && payload && payload.length) {
        return (
          <div className="bg-white border border-gray-300 rounded-lg shadow-xl p-3 z-50">
            <p className="text-xs text-gray-600 mb-1">{label}</p>
            <p className="text-sm font-semibold text-gray-900">
              {formatValue(payload[0].value)}
            </p>
          </div>
        );
      }
      return null;
    };



    return (
      <div className="h-full -mx-4 -mb-2 relative group" style={{ minHeight: '80px' }}>
        <ResponsiveContainer width="100%" height="100%" minHeight={80}>
          <AreaChart data={kpi.chartData} margin={{ top: 4, right: 2, left: 2, bottom: 2 }}>
            <defs>
              <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#374151" stopOpacity={0.15} />
                <stop offset="25%" stopColor="#4B5563" stopOpacity={0.12} />
                <stop offset="50%" stopColor="#6B7280" stopOpacity={0.08} />
                <stop offset="75%" stopColor="#9CA3AF" stopOpacity={0.05} />
                <stop offset="100%" stopColor="#D1D5DB" stopOpacity={0.02} />
              </linearGradient>
            </defs>
            <XAxis dataKey="name" hide />
            <YAxis hide />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="value"
              stroke="#000000"
              strokeWidth={3}
              fill={`url(#${gradientId})`}
              fillOpacity={1}
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>

        {/* Alert Threshold Lines */}
        {showAlertLines && (
          <AlertThresholdLines
            alertConfig={alertConfig}
            chartData={kpi.chartData}
            yAxisKey="value"
            isVisible={showAlertLines}
            chartHeight={80}
            chartMargin={{ top: 4, right: 2, left: 2, bottom: 2 }}
          />
        )}
      </div>
    );
  };

  const cardContent = (
    <div className={cn(
      "relative h-full flex flex-col",
      isLarge ? "p-6" : "p-4"
    )}>
      {/* Header */}
      <div className={cn(
        "flex items-start justify-between",
        isLarge ? "mb-6" : "mb-3"
      )}>
        <div className="flex-1 min-w-0"> {/* min-w-0 prevents text overflow */}
          <div className="flex items-center gap-2">
            <h3 className={cn(
              "font-semibold text-gray-900 truncate",
              isLarge ? "text-xl" : "text-base"
            )}>
              {kpi.title}
            </h3>
            {/* Alert icon - only show when alert is active */}
            {kpi.alert?.isActive && (
              <AlertCircle className={cn(
                "flex-shrink-0",
                kpi.alert.type === 'critical' ? "text-red-500" : "text-amber-500",
                isLarge ? "w-5 h-5" : "w-4 h-4"
              )} />
            )}
          </div>
          <p className={cn(
            "text-gray-500 mt-1 line-clamp-2",
            isLarge ? "text-sm" : "text-xs"
          )}>
            {kpi.description}
          </p>
        </div>
        
        {/* Star that appears on hover - NO LOCAL EVENT HANDLER */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: isHovered || (kpi.is_priority || kpi.isPriority) ? 1 : 0,
            scale: isHovered || (kpi.is_priority || kpi.isPriority) ? 1 : 0.8
          }}
          transition={{ duration: 0.2 }}
          className="cursor-pointer select-none flex-shrink-0 ml-3"
          data-kpi-id={kpi.id}
          data-kpi-action="toggle-priority"
        >
          <Star 
            className={cn(
              "h-4 w-4 transition-all duration-200 stroke-[1.5] pointer-events-none", 
              (kpi.is_priority || kpi.isPriority)
                ? "fill-amber-500 text-amber-500 hover:fill-amber-600 hover:text-amber-600" 
                : "text-gray-400 hover:text-amber-500 hover:fill-amber-100"
            )}
          />
        </motion.div>
      </div>

      {/* Value Section */}
      <div className={cn(
        "flex items-center justify-between",
        isLarge ? "mb-6" : "mb-3"
      )}>
        <div 
          className={cn(
            "font-bold text-gray-900 truncate",
            isLarge ? "text-3xl" : "text-xl"
          )}
          data-testid={`${kpi.id}-value`}
        >
          {formatValue(kpi.currentValue)}
        </div>
        <div className="flex-shrink-0 ml-2">
          {getTrendIcon()}
        </div>
      </div>

      {/* Chart Section */}
      <div className={cn(
        "flex-1 min-h-0 overflow-hidden",
        isLarge ? "min-h-[120px]" : "min-h-[60px]"
      )}>
        {renderChart()}
      </div>
    </div>
  );

  return (
    <motion.div
      className="h-full"
      animate={{
        scale: isSelected ? 1.02 : 1,
        height: isSelected ? "auto" : "100%"
      }}
      whileHover={{ scale: isSelected ? 1.02 : 1.01 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.3
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      layout
    >
      <Card
        className={getCardStyles(isSelected, isHovered)}
        data-testid="kpi-card"
        data-kpi-id={kpi.id}
      onClick={(e) => {
        // Check if click was on the star element - if so, ignore
        const target = e.target as HTMLElement;
        const starElement = target.closest('[data-kpi-action="toggle-priority"]');
        
        if (starElement) {
          // Click was on star, let grid handler manage it
          return;
        }
        
        const cardElement = e.currentTarget as HTMLElement;
        const rect = cardElement.getBoundingClientRect();
        kpiEvents.selectKPI({ 
          kpiId: kpi.id, 
          element: cardElement,
          cardRect: rect 
        });
      }}
      >
        {cardContent}
      </Card>
    </motion.div>
  );
};

export default KpiBentoCard;