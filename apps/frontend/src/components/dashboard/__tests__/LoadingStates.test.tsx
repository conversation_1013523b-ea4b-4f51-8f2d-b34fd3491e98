/**
 * Tests for LoadingStates Components
 * 
 * Tests loading skeletons and states
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { KpiCardSkeleton, DashboardLoading, FilterLoading } from '../LoadingStates';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  }
}));

describe('LoadingStates', () => {
  describe('KpiCardSkeleton', () => {
    it('should render skeleton with default size', () => {
      const { container } = render(<KpiCardSkeleton />);
      
      const skeleton = container.querySelector('.bg-white.rounded-xl');
      expect(skeleton).toBeInTheDocument();
    });

    it('should render large skeleton when isLarge is true', () => {
      const { container } = render(<KpiCardSkeleton isLarge />);
      
      // Check for large-specific classes
      const largeTitleSkeleton = container.querySelector('.h-6.w-48');
      expect(largeTitleSkeleton).toBeInTheDocument();
    });

    it('should render small skeleton when isLarge is false', () => {
      const { container } = render(<KpiCardSkeleton isLarge={false} />);
      
      // Check for small-specific classes
      const smallTitleSkeleton = container.querySelector('.h-5.w-32');
      expect(smallTitleSkeleton).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      const { container } = render(<KpiCardSkeleton className="custom-class" />);
      
      const skeleton = container.querySelector('.custom-class');
      expect(skeleton).toBeInTheDocument();
    });
  });

  describe('FilterLoading', () => {
    it('should render filter loading indicator', () => {
      render(<FilterLoading />);
      
      expect(screen.getByText('Atualizando...')).toBeInTheDocument();
    });

    it('should render with spinner', () => {
      const { container } = render(<FilterLoading />);
      
      const spinner = container.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      const { container } = render(<FilterLoading className="custom-filter-class" />);
      
      const filterLoading = container.querySelector('.custom-filter-class');
      expect(filterLoading).toBeInTheDocument();
    });
  });

  describe('DashboardLoading', () => {
    it('should render skeleton grid by default', () => {
      const { container } = render(<DashboardLoading />);
      
      const gridContainer = container.querySelector('.grid.grid-cols-12');
      expect(gridContainer).toBeInTheDocument();
    });

    it('should render spinner when showSkeleton is false', () => {
      render(<DashboardLoading showSkeleton={false} />);
      
      expect(screen.getByText('Carregando métricas...')).toBeInTheDocument();
    });

    it('should render default number of skeletons (6)', () => {
      const { container } = render(<DashboardLoading />);

      const skeletons = container.querySelectorAll('.bg-white.rounded-xl');
      expect(skeletons).toHaveLength(6);
    });

    it('should render with proper wrapper structure', () => {
      const { container } = render(<DashboardLoading />);

      // Check for the main wrapper structure
      const wrapper = container.querySelector('.w-full.px-4');
      expect(wrapper).toBeInTheDocument();
    });

    it('should render spinner with correct structure', () => {
      const { container } = render(<DashboardLoading showSkeleton={false} />);
      
      const spinner = container.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
      
      const flexContainer = container.querySelector('.flex.items-center.justify-center');
      expect(flexContainer).toBeInTheDocument();
    });
  });
});
