/**
 * Tests for KpiBentoGrid Component
 * 
 * Tests the responsive grid layout and KPI rendering
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import KpiBentoGrid from '../KpiBentoGrid';
import { KpiData, DashboardFilters } from '@/types/dashboard';

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  }
}));

// Mock KpiBentoCard component
vi.mock('../KpiBentoCard', () => ({
  default: ({ kpi, isLarge }: { kpi: KpiData; isLarge: boolean }) => (
    <div data-testid={`kpi-card-${kpi.id}`} data-large={isLarge}>
      <h3>{kpi.name}</h3>
      <span>{kpi.value}</span>
    </div>
  )
}));

const mockFilters: DashboardFilters = {
  timeframe: 'week',
  currency: 'all',
  sector: 'cambio',
  priority_only: false
};

// Minimal test data - no hardcoded business values
const mockKpis: KpiData[] = [
  {
    id: 'test_kpi_1',
    name: 'Test KPI 1',
    value: 100,
    formattedValue: '100',
    change: 5,
    trend: 'up',
    category: 'test',
    priority: true,
    unit: 'number',
    description: 'Test KPI for unit testing'
  },
  {
    id: 'test_kpi_2',
    name: 'Test KPI 2',
    value: 200,
    formattedValue: '200',
    change: -2,
    trend: 'down',
    category: 'test',
    priority: true,
    unit: 'number',
    description: 'Test KPI for unit testing'
  }
];

describe('KpiBentoGrid', () => {
  const defaultProps = {
    kpis: mockKpis,
    filters: mockFilters,
    onTogglePriority: vi.fn(),
    onRemoveKpi: vi.fn(),
    periodData: {
      currentDate: new Date().toISOString(),
      isSimulated: false
    }
  };

  it('should render all KPIs', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    expect(screen.getByTestId('kpi-card-test_kpi_1')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-card-test_kpi_2')).toBeInTheDocument();
  });

  it('should apply large size to first 2 KPIs when there are 5+ KPIs', () => {
    const manyKpis = [
      ...mockKpis,
      { ...mockKpis[0], id: 'kpi3', name: 'KPI 3' },
      { ...mockKpis[0], id: 'kpi4', name: 'KPI 4' },
      { ...mockKpis[0], id: 'kpi5', name: 'KPI 5' }
    ];

    render(<KpiBentoGrid {...defaultProps} kpis={manyKpis} />);

    expect(screen.getByTestId('kpi-card-test_kpi_1')).toHaveAttribute('data-large', 'true');
    expect(screen.getByTestId('kpi-card-test_kpi_2')).toHaveAttribute('data-large', 'true');
    expect(screen.getByTestId('kpi-card-kpi3')).toHaveAttribute('data-large', 'false');
  });

  it('should not apply large size when there are fewer than 5 KPIs', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    expect(screen.getByTestId('kpi-card-test_kpi_1')).toHaveAttribute('data-large', 'false');
    expect(screen.getByTestId('kpi-card-test_kpi_2')).toHaveAttribute('data-large', 'false');
  });

  it('should handle empty KPI list', () => {
    render(<KpiBentoGrid {...defaultProps} kpis={[]} />);

    expect(screen.queryByTestId(/kpi-card-/)).not.toBeInTheDocument();
  });

  it('should render with correct grid structure', () => {
    const { container } = render(<KpiBentoGrid {...defaultProps} />);

    const gridContainer = container.querySelector('.grid.grid-cols-12');
    expect(gridContainer).toBeInTheDocument();
  });
});
