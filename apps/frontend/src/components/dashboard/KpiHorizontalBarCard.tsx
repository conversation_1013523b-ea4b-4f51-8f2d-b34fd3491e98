import React, { useState, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Star,
  AlertCircle,
  Users,
  Building2
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { KpiData, DashboardFilters } from '@/types/dashboard';
import { cn } from '@/lib/utils';
import { kpiEvents } from '@/lib/events';
import { formatKpiValue, formatChangePercent, getTrendColorClass } from '@/lib/formatters';

interface KpiHorizontalBarCardProps {
  kpi: KpiData;
  filters: DashboardFilters;
  onRemoveKpi?: (kpiId: string) => void;
  isLarge?: boolean;
  isSelected?: boolean;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiHorizontalBarCard: React.FC<KpiHorizontalBarCardProps> = ({
  kpi,
  filters,
  onRemoveKpi,
  isLarge = false,
  isSelected = false,
  periodData
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Generate colors for bars
  const generateBarColors = (index: number, total: number) => {
    const hue = (index * 360) / total;
    return {
      primary: `hsl(${hue}, 65%, 55%)`,
      light: `hsl(${hue}, 65%, 85%)`,
      dark: `hsl(${hue}, 65%, 45%)`
    };
  };

  const getCardStyles = (selected: boolean, hovered: boolean) => {
    const baseStyles = "h-full p-6 transition-all duration-300 cursor-pointer relative overflow-hidden";
    
    if (selected) {
      return cn(baseStyles, "ring-2 ring-blue-500 shadow-lg bg-white border-blue-200");
    }
    
    if (hovered) {
      return cn(baseStyles, "shadow-md bg-white border-gray-200 transform");
    }
    
    return cn(
      baseStyles, 
      "shadow-sm bg-white hover:shadow-md",
      // Alert styling with amber border
      kpi.alert?.isActive ? "border-amber-400 border-2" : "border-gray-100",
      // Priority cards get a subtle ring
      (kpi.is_priority || kpi.isPriority) && "ring-2 ring-amber-100"
    );
  };

  const renderTrendIcon = () => {
    if (!kpi.trend || kpi.trend === 'neutral') return null;
    
    const Icon = kpi.trend === 'up' ? TrendingUp : TrendingDown;
    const colorClass = getTrendColorClass(kpi.trend);
    
    return (
      <div className={cn("flex items-center gap-1 text-sm font-medium", colorClass)}>
        <Icon className="w-4 h-4" />
        <span>{formatChangePercent(kpi.changePercent)}</span>
      </div>
    );
  };

  const renderHorizontalBars = () => {
    if (!kpi.chartData || kpi.chartData.length === 0) {
      return (
        <div className="h-full flex items-center justify-center text-gray-400 text-sm">
          <div className="text-center">
            <Building2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>Sem dados categóricos</p>
          </div>
        </div>
      );
    }

    // Sort data by value (descending) and take top 5
    const sortedData = [...kpi.chartData]
      .sort((a, b) => b.value - a.value)
      .slice(0, 5);

    const maxValue = Math.max(...sortedData.map(d => d.value));

    return (
      <div className="space-y-3">
        {sortedData.map((item, index) => {
          const colors = generateBarColors(index, sortedData.length);
          const percentage = (item.value / maxValue) * 100;
          
          return (
            <motion.div
              key={item.category || item.name || index}
              className="relative"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {/* Category Label and Value */}
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-700 truncate">
                  {item.category || item.name || `Item ${index + 1}`}
                </span>
                <span className="text-sm font-semibold text-gray-900">
                  {item.formatted_value || formatKpiValue(item.value, kpi.format || 'number')}
                </span>
              </div>
              
              {/* Progress Bar */}
              <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                <motion.div
                  className="absolute top-0 left-0 h-full rounded-full"
                  style={{ 
                    background: `linear-gradient(90deg, ${colors.primary} 0%, ${colors.dark} 100%)`,
                  }}
                  initial={{ width: 0 }}
                  animate={{ width: `${percentage}%` }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                />
                
                {/* Shine effect */}
                <motion.div
                  className="absolute top-0 left-0 h-full w-full"
                  style={{
                    background: `linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%)`,
                  }}
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{ 
                    duration: 1.5, 
                    delay: index * 0.1 + 0.5,
                    ease: "easeInOut"
                  }}
                />
              </div>
              
              {/* Percentage indicator */}
              {item.percentage && (
                <div className="text-xs text-gray-500 mt-1">
                  {item.percentage.toFixed(1)}% do total
                </div>
              )}
            </motion.div>
          );
        })}
      </div>
    );
  };

  const cardContent = (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {kpi.title || kpi.name}
            </h3>
            {kpi.isPriority && (
              <Star 
                className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" 
                data-kpi-action="toggle-priority"
              />
            )}
          </div>
          <p className="text-sm text-gray-600 line-clamp-2">
            {kpi.description}
          </p>
        </div>
        
        {kpi.alert?.isActive && (
          <AlertCircle className={cn(
            "flex-shrink-0 ml-2",
            kpi.alert.type === 'critical' ? "text-red-500" : "text-amber-500",
            isLarge ? "w-5 h-5" : "w-4 h-4"
          )} />
        )}
      </div>

      {/* Main Value */}
      <div className="mb-4">
        <div className="flex items-baseline gap-2">
          <span className="text-2xl font-bold text-gray-900">
            {formatKpiValue(kpi.currentValue, kpi.format || 'number')}
          </span>
          {kpi.unit && (
            <span className="text-sm text-gray-500">{kpi.unit}</span>
          )}
        </div>
        {renderTrendIcon()}
      </div>

      {/* Horizontal Bars Chart */}
      <div className={cn(
        "flex-1 min-h-0 overflow-hidden",
        isLarge ? "min-h-[160px]" : "min-h-[120px]"
      )}>
        {renderHorizontalBars()}
      </div>

      {/* Footer with category icon */}
      <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <Users className="w-3 h-3" />
          <span>Top {Math.min(5, kpi.chartData?.length || 0)} categorias</span>
        </div>
        {periodData?.isSimulated && (
          <span className="text-xs text-blue-600 font-medium">
            Simulado
          </span>
        )}
      </div>
    </div>
  );

  return (
    <motion.div
      ref={cardRef}
      className="h-full"
      whileHover={{ scale: 1.02 }}
      transition={{ 
        type: "spring", 
        stiffness: 300,
        damping: 30
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      layout
    >
      <Card
        className={getCardStyles(isSelected, isHovered)}
        data-testid="kpi-horizontal-bar-card"
        data-kpi-id={kpi.id}
        onClick={(e) => {
          const target = e.target as HTMLElement;
          const starElement = target.closest('[data-kpi-action="toggle-priority"]');
          
          if (starElement) {
            return;
          }
          
          const cardElement = e.currentTarget as HTMLElement;
          const rect = cardElement.getBoundingClientRect();
          kpiEvents.selectKPI({ 
            kpiId: kpi.id, 
            element: cardElement,
            cardRect: rect 
          });
        }}
      >
        {cardContent}
      </Card>
    </motion.div>
  );
};

export default KpiHorizontalBarCard;
