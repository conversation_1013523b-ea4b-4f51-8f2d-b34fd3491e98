import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoadingSkeletonProps {
  isLarge?: boolean;
  className?: string;
}

export const KpiCardSkeleton: React.FC<LoadingSkeletonProps> = ({ isLarge = false, className = '' }) => {
  return (
    <motion.div
      className={`bg-white rounded-xl border border-gray-200 p-6 h-full ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className={`bg-gray-200 rounded-lg animate-pulse ${isLarge ? 'h-6 w-48' : 'h-5 w-32'} mb-2`}></div>
          <div className={`bg-gray-200 rounded-lg animate-pulse ${isLarge ? 'h-4 w-64' : 'h-3 w-40'}`}></div>
        </div>
        <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>
      
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div className={`bg-gray-200 rounded-lg animate-pulse ${isLarge ? 'h-10 w-56' : 'h-8 w-32'}`}></div>
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-12 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>
      
      <div className={`bg-gray-200 rounded-lg animate-pulse ${isLarge ? 'h-32' : 'h-20'} relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-shimmer"></div>
      </div>
    </motion.div>
  );
};

interface FilterLoadingProps {
  className?: string;
}

export const FilterLoading: React.FC<FilterLoadingProps> = ({ className = '' }) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
      <span className="text-sm text-gray-600">Atualizando...</span>
    </div>
  );
};

interface DashboardLoadingProps {
  showSkeleton?: boolean;
  kpiCount?: number;
  className?: string;
}

export const DashboardLoading: React.FC<DashboardLoadingProps> = ({
  showSkeleton = true,
  kpiCount = 6,
  className = ''
}) => {
  if (!showSkeleton) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600">Carregando métricas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-4 md:px-6 lg:px-8">
      <div className="grid grid-cols-12 gap-4 md:gap-6 auto-rows-[minmax(180px,_1fr)]">
        {/* Large cards */}
        <div className="col-span-12 md:col-span-6 row-span-2">
          <KpiCardSkeleton isLarge />
        </div>
        <div className="col-span-12 md:col-span-6 row-span-2">
          <KpiCardSkeleton isLarge />
        </div>
        
        {/* Small cards */}
        <div className="col-span-6 md:col-span-3 row-span-1">
          <KpiCardSkeleton />
        </div>
        <div className="col-span-6 md:col-span-3 row-span-1">
          <KpiCardSkeleton />
        </div>
        <div className="col-span-6 md:col-span-3 row-span-1">
          <KpiCardSkeleton />
        </div>
        <div className="col-span-6 md:col-span-3 row-span-1">
          <KpiCardSkeleton />
        </div>
      </div>
    </div>
  );
};

// Shimmer animation CSS (add to your global styles)
const shimmerStyles = `
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
.animate-shimmer {
  animation: shimmer 1.5s infinite;
}
`;

export default {
  KpiCardSkeleton,
  FilterLoading,
  DashboardLoading,
  shimmerStyles
};