/**
 * KPI Formatter Service
 * 
 * Centralized service for formatting KPI values, handling currency,
 * percentages, and number formatting with proper localization.
 */

import { KpiData, CurrencyOption, FormattingOptions, FormatConfig } from '@/types/dashboard';

// ============================================================================
// CONFIGURATION
// ============================================================================

const DEFAULT_LOCALE = 'pt-BR';

const CURRENCY_MAP: Record<CurrencyOption, string> = {
  all: 'BRL',
  usd: 'USD',
  eur: 'EUR',
  gbp: 'GBP',
};

const FORMAT_CONFIGS: Record<KpiData['format'], Partial<FormatConfig>> = {
  currency: {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  },
  percentage: {
    minimumFractionDigits: 1,
    maximumFractionDigits: 2,
  },
  number: {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  },
};

// ============================================================================
// CORE FORMATTING FUNCTIONS
// ============================================================================

/**
 * Format a KPI value based on its type and currency settings
 */
export const formatKpiValue = (options: FormattingOptions): string => {
  const { format, currency, value } = options;

  switch (format) {
    case 'currency':
      return formatCurrency(value, currency);
    case 'percentage':
      return formatPercentage(value);
    case 'number':
      return formatNumber(value);
    default:
      return value.toString();
  }
};

/**
 * Format currency values with proper localization
 */
export const formatCurrency = (value: number, currency: CurrencyOption): string => {
  const currencyCode = CURRENCY_MAP[currency];
  const config = FORMAT_CONFIGS.currency;

  // Adjust decimal places based on value magnitude
  const maximumFractionDigits = value < 1000 ? 2 : 0;

  return value.toLocaleString(DEFAULT_LOCALE, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: config.minimumFractionDigits,
    maximumFractionDigits,
  });
};

/**
 * Format percentage values
 */
export const formatPercentage = (value: number): string => {
  const config = FORMAT_CONFIGS.percentage;
  
  return `${value.toFixed(config.maximumFractionDigits)}%`;
};

/**
 * Format number values with proper localization
 */
export const formatNumber = (value: number): string => {
  const config = FORMAT_CONFIGS.number;

  return value.toLocaleString(DEFAULT_LOCALE, {
    minimumFractionDigits: config.minimumFractionDigits,
    maximumFractionDigits: config.maximumFractionDigits,
  });
};

// ============================================================================
// CHANGE FORMATTING
// ============================================================================

/**
 * Format change percentage with proper sign and color indication
 */
export const formatChangePercent = (changePercent: number): {
  formatted: string;
  sign: 'positive' | 'negative' | 'neutral';
  color: string;
} => {
  const sign = changePercent > 0 ? 'positive' : changePercent < 0 ? 'negative' : 'neutral';
  const color = sign === 'positive' ? 'text-green-600' : sign === 'negative' ? 'text-red-600' : 'text-gray-600';
  
  const formatted = changePercent > 0 
    ? `+${changePercent.toFixed(1)}%`
    : `${changePercent.toFixed(1)}%`;

  return { formatted, sign, color };
};

/**
 * Format change value with proper currency/format
 */
export const formatChangeValue = (
  currentValue: number,
  previousValue: number,
  format: KpiData['format'],
  currency: CurrencyOption
): string => {
  const changeValue = currentValue - previousValue;
  
  return formatKpiValue({
    format,
    currency,
    value: changeValue,
  });
};

// ============================================================================
// COMPACT FORMATTING
// ============================================================================

/**
 * Format large numbers in compact form (1.2M, 1.5K, etc.)
 */
export const formatCompactNumber = (value: number): string => {
  const formatter = new Intl.NumberFormat(DEFAULT_LOCALE, {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  });

  return formatter.format(value);
};

/**
 * Format currency in compact form
 */
export const formatCompactCurrency = (value: number, currency: CurrencyOption): string => {
  const currencyCode = CURRENCY_MAP[currency];
  
  const formatter = new Intl.NumberFormat(DEFAULT_LOCALE, {
    style: 'currency',
    currency: currencyCode,
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  });

  return formatter.format(value);
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get appropriate format config for a KPI
 */
export const getFormatConfig = (format: KpiData['format']): FormatConfig => {
  return {
    locale: DEFAULT_LOCALE,
    currency: CURRENCY_MAP.all,
    ...FORMAT_CONFIGS[format],
  } as FormatConfig;
};

/**
 * Determine if a value should use compact formatting
 */
export const shouldUseCompactFormat = (value: number): boolean => {
  return Math.abs(value) >= 10000;
};

/**
 * Get currency symbol for display
 */
export const getCurrencySymbol = (currency: CurrencyOption): string => {
  const symbols: Record<CurrencyOption, string> = {
    all: 'R$',
    usd: '$',
    eur: '€',
    gbp: '£',
  };

  return symbols[currency];
};

// ============================================================================
// VALIDATION
// ============================================================================

/**
 * Validate if a value can be formatted
 */
export const isValidValue = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
};

/**
 * Safe format function that handles invalid values
 */
export const safeFormatKpiValue = (options: FormattingOptions): string => {
  if (!isValidValue(options.value)) {
    return '--';
  }

  try {
    return formatKpiValue(options);
  } catch (error) {
    console.warn('Error formatting KPI value:', error);
    return '--';
  }
};

// ============================================================================
// EXPORTS
// ============================================================================

export const kpiFormatter = {
  formatValue: formatKpiValue,
  formatCurrency,
  formatPercentage,
  formatNumber,
  formatChangePercent,
  formatChangeValue,
  formatCompact: formatCompactNumber,
  formatCompactCurrency,
  safeFormat: safeFormatKpiValue,
  isValid: isValidValue,
  getCurrencySymbol,
  shouldUseCompact: shouldUseCompactFormat,
};

export default kpiFormatter;
