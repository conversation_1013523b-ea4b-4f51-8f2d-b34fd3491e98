/**
 * Formatters - Simple formatting utilities
 * 
 * Centralized formatting functions for KPI values, maintaining
 * the exact same output as the original implementation.
 */

import { DashboardFilters } from '@/types/dashboard';

/**
 * Format KPI value based on format type and filters
 * Maintains exact same behavior as original KpiBentoCard
 */
export const formatKpiValue = (
  value: number, 
  format: string, 
  filters: DashboardFilters
): string => {
  const currency = filters.currency || 'all';
  
  if (format === 'currency') {
    return value.toLocaleString('pt-BR', { 
      style: 'currency', 
      currency: currency === 'all' ? 'BRL' : currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: value < 1000 ? 2 : 0
    });
  } else if (format === 'percentage') {
    return `${value.toFixed(2)}%`;
  } else {
    return value.toLocaleString('pt-BR', { 
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
};

/**
 * Format change percentage with sign
 * Maintains exact same behavior as original
 */
export const formatChangePercent = (changePercent: number): string => {
  return `${changePercent > 0 ? '+' : ''}${changePercent.toFixed(1)}%`;
};

/**
 * Get trend color class
 * Maintains exact same behavior as original
 */
export const getTrendColorClass = (changePercent: number): string => {
  return changePercent > 0 ? 'text-green-600' : 'text-red-600';
};
