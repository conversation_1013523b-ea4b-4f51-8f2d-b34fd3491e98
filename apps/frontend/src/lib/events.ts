import EventEmitter from 'eventemitter3';

// ============================================================================
// EVENT INTERFACES
// ============================================================================

export interface KPIClickEvent {
  kpiId: string;
  element?: HTMLElement;
  cardRect?: DOMRect;
}

export interface DrawerTimeframeChangedEvent {
  timeframe: string;
  kpiId: string;
  timestamp: string;
}

export interface DrawerDataUpdatedEvent {
  kpiId: string;
  data: any;
  timestamp: string;
  source: 'timeframe' | 'currency' | 'refresh';
}

export interface CardSelectionChangedEvent {
  kpiId: string;
  selected: boolean;
  timestamp: string;
}

export interface AlertConfigUpdatedEvent {
  kpiId: string;
  config: {
    enabled: boolean;
    warningThreshold?: number;
    criticalThreshold?: number;
  };
  timestamp: string;
}

export interface AlertConfigDeletedEvent {
  kpiId: string;
  timestamp: string;
}

export interface FiltersSynchronizedEvent {
  source: 'dashboard' | 'drawer';
  filters: {
    timeframe: string;
    currency: string;
  };
  timestamp: string;
}

export interface DrawerFiltersChangedEvent {
  filters: {
    timeframe: string;
    currency: string;
  };
  kpiId: string | null;
  source: 'timeframe' | 'currency' | 'batch';
  timestamp: string;
}

export interface DashboardFiltersChangedEvent {
  filters: {
    timeframe: string;
    currency: string;
  };
  source: 'dashboard' | 'drawer';
  timestamp: string;
}

export interface DrawerSyncedWithDashboardEvent {
  filters: {
    timeframe: string;
    currency: string;
  };
  kpiId: string | null;
  timestamp: string;
}

// ============================================================================
// ENHANCED KPI EVENT BUS
// ============================================================================

class KPIEventBus extends EventEmitter {
  private _isDrawerOpen = false;
  private _selectedKpiId: string | null = null;
  private _currentFilters = {
    timeframe: 'week',
    currency: 'all'
  };

  // ========================================================================
  // EXISTING METHODS (Enhanced)
  // ========================================================================

  selectKPI(data: KPIClickEvent) {
    this._isDrawerOpen = true;
    this._selectedKpiId = data.kpiId;
    this.emit('kpi:selected', data);

    // Emit card selection changed event
    this.emit('card:selection-changed', {
      kpiId: data.kpiId,
      selected: true,
      timestamp: new Date().toISOString()
    } as CardSelectionChangedEvent);
  }

  closeDrawer() {
    const previousKpiId = this._selectedKpiId;
    this._isDrawerOpen = false;
    this._selectedKpiId = null;
    this.emit('drawer:close');

    // Emit card selection changed event
    if (previousKpiId) {
      this.emit('card:selection-changed', {
        kpiId: previousKpiId,
        selected: false,
        timestamp: new Date().toISOString()
      } as CardSelectionChangedEvent);
    }
  }

  get isDrawerOpen() {
    return this._isDrawerOpen;
  }

  get selectedKpiId() {
    return this._selectedKpiId;
  }

  // ========================================================================
  // NEW DRAWER SYNCHRONIZATION METHODS
  // ========================================================================

  /**
   * Emit drawer timeframe changed event
   */
  drawerTimeframeChanged(timeframe: string, kpiId: string) {
    this._currentFilters.timeframe = timeframe;
    this.emit('drawer:timeframe-changed', {
      timeframe,
      kpiId,
      timestamp: new Date().toISOString()
    } as DrawerTimeframeChangedEvent);
  }

  /**
   * Emit drawer data updated event
   */
  drawerDataUpdated(kpiId: string, data: any, source: 'timeframe' | 'currency' | 'refresh' = 'refresh') {
    this.emit('drawer:data-updated', {
      kpiId,
      data,
      timestamp: new Date().toISOString(),
      source
    } as DrawerDataUpdatedEvent);
  }

  /**
   * Emit filters synchronized event
   */
  filtersSynchronized(source: 'dashboard' | 'drawer', filters: { timeframe: string; currency: string }) {
    this._currentFilters = { ...filters };
    this.emit('filters:synchronized', {
      source,
      filters,
      timestamp: new Date().toISOString()
    } as FiltersSynchronizedEvent);
  }

  /**
   * Emit dashboard filters changed event
   */
  dashboardFiltersChanged(filters: { timeframe: string; currency: string }, source: 'dashboard' | 'drawer' = 'dashboard') {
    this._currentFilters = { ...filters };
    this.emit('dashboard:filters-changed', {
      filters,
      source,
      timestamp: new Date().toISOString()
    } as DashboardFiltersChangedEvent);
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  /**
   * Get current filters state
   */
  getCurrentFilters() {
    return { ...this._currentFilters };
  }

  /**
   * Get current drawer state
   */
  getDrawerState() {
    return {
      isOpen: this._isDrawerOpen,
      selectedKpiId: this._selectedKpiId,
      filters: { ...this._currentFilters }
    };
  }

  /**
   * Emit alert configuration updated event
   */
  alertConfigUpdated(kpiId: string, config: { enabled: boolean; warningThreshold?: number; criticalThreshold?: number; }) {
    const event: AlertConfigUpdatedEvent = {
      kpiId,
      config,
      timestamp: new Date().toISOString()
    };

    console.log(`🚨 [Events] Alert config updated for KPI: ${kpiId}`, config);
    this.emit('alert:config-updated', event);
  }

  /**
   * Emit alert configuration deleted event
   */
  alertConfigDeleted(kpiId: string) {
    const event: AlertConfigDeletedEvent = {
      kpiId,
      timestamp: new Date().toISOString()
    };

    console.log(`🗑️ [Events] Alert config deleted for KPI: ${kpiId}`);
    this.emit('alert:config-deleted', event);
  }

  /**
   * Debug method to log all events
   */
  enableDebugMode() {
    const events = [
      'kpi:selected',
      'drawer:close',
      'drawer:timeframe-changed',
      'drawer:data-updated',
      'card:selection-changed',
      'filters:synchronized',
      'dashboard:filters-changed',
      'drawer:filters-changed',
      'drawer:synced-with-dashboard',
      'alert:config-updated',
      'alert:config-deleted'
    ];

    events.forEach(event => {
      this.on(event, (data) => {
        console.log(`🎯 Event: ${event}`, data);
      });
    });
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const kpiEvents = new KPIEventBus();

// Enable debug mode in development
if (process.env.NODE_ENV === 'development') {
  // Uncomment to enable debug logging
  // kpiEvents.enableDebugMode();
}

// ============================================================================
// TYPE EXPORTS FOR CONSUMERS
// ============================================================================

export type {
  KPIClickEvent,
  DrawerTimeframeChangedEvent,
  DrawerDataUpdatedEvent,
  CardSelectionChangedEvent,
  FiltersSynchronizedEvent,
  DrawerFiltersChangedEvent,
  DashboardFiltersChangedEvent,
  DrawerSyncedWithDashboardEvent
};