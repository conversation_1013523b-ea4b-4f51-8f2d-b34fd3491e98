/**
 * Events System Tests
 * ===================
 * 
 * Unit tests for the enhanced KPI event bus system.
 * Tests event emission, state management, and synchronization functionality.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { kpiEvents, KPIClickEvent } from '../events';

describe('KPIEventBus', () => {
  beforeEach(() => {
    // Reset the event bus state
    kpiEvents.removeAllListeners();
    // Reset internal state by closing drawer if open
    if (kpiEvents.isDrawerOpen) {
      kpiEvents.closeDrawer();
    }
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Event Functionality', () => {
    it('should be an instance of EventEmitter', () => {
      expect(kpiEvents.emit).toBeDefined();
      expect(kpiEvents.on).toBeDefined();
      expect(kpiEvents.off).toBeDefined();
    });

    it('should emit and listen to custom events', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('test-event', mockListener);
      kpiEvents.emit('test-event', { data: 'test' });

      expect(mockListener).toHaveBeenCalledWith({ data: 'test' });
    });
  });

  describe('KPI Selection', () => {
    it('should handle KPI selection correctly', () => {
      const mockListener = vi.fn();
      const mockCardSelectionListener = vi.fn();
      
      kpiEvents.on('kpi:selected', mockListener);
      kpiEvents.on('card:selection-changed', mockCardSelectionListener);

      const clickData: KPIClickEvent = {
        kpiId: 'test-kpi',
        element: document.createElement('div'),
        cardRect: new DOMRect(0, 0, 100, 100)
      };

      kpiEvents.selectKPI(clickData);

      expect(kpiEvents.isDrawerOpen).toBe(true);
      expect(kpiEvents.selectedKpiId).toBe('test-kpi');
      expect(mockListener).toHaveBeenCalledWith(clickData);
      expect(mockCardSelectionListener).toHaveBeenCalledWith(
        expect.objectContaining({
          kpiId: 'test-kpi',
          selected: true,
          timestamp: expect.any(String)
        })
      );
    });

    it('should handle KPI selection without optional parameters', () => {
      const clickData: KPIClickEvent = {
        kpiId: 'simple-kpi'
      };

      kpiEvents.selectKPI(clickData);

      expect(kpiEvents.isDrawerOpen).toBe(true);
      expect(kpiEvents.selectedKpiId).toBe('simple-kpi');
    });
  });

  describe('Drawer Close', () => {
    it('should handle drawer close correctly', () => {
      const mockCloseListener = vi.fn();
      const mockCardSelectionListener = vi.fn();
      
      kpiEvents.on('drawer:close', mockCloseListener);
      kpiEvents.on('card:selection-changed', mockCardSelectionListener);

      // First select a KPI
      kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      expect(kpiEvents.isDrawerOpen).toBe(true);

      // Then close the drawer
      kpiEvents.closeDrawer();

      expect(kpiEvents.isDrawerOpen).toBe(false);
      expect(kpiEvents.selectedKpiId).toBeNull();
      expect(mockCloseListener).toHaveBeenCalled();
      expect(mockCardSelectionListener).toHaveBeenCalledWith(
        expect.objectContaining({
          kpiId: 'test-kpi',
          selected: false,
          timestamp: expect.any(String)
        })
      );
    });

    it('should not emit card selection event if no KPI was selected', () => {
      const mockCardSelectionListener = vi.fn();
      
      kpiEvents.on('card:selection-changed', mockCardSelectionListener);

      // Close drawer without selecting KPI first
      kpiEvents.closeDrawer();

      expect(mockCardSelectionListener).not.toHaveBeenCalled();
    });
  });

  describe('Drawer Timeframe Changes', () => {
    it('should emit timeframe changed event', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('drawer:timeframe-changed', mockListener);

      kpiEvents.drawerTimeframeChanged('month', 'test-kpi');

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          timeframe: 'month',
          kpiId: 'test-kpi',
          timestamp: expect.any(String)
        })
      );
    });

    it('should update internal filters state', () => {
      kpiEvents.drawerTimeframeChanged('quarter', 'test-kpi');

      const currentFilters = kpiEvents.getCurrentFilters();
      expect(currentFilters.timeframe).toBe('quarter');
    });
  });

  describe('Drawer Data Updates', () => {
    it('should emit data updated event with default source', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('drawer:data-updated', mockListener);

      const testData = { value: 100, trend: 'up' };
      kpiEvents.drawerDataUpdated('test-kpi', testData);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          kpiId: 'test-kpi',
          data: testData,
          source: 'refresh',
          timestamp: expect.any(String)
        })
      );
    });

    it('should emit data updated event with custom source', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('drawer:data-updated', mockListener);

      const testData = { value: 200 };
      kpiEvents.drawerDataUpdated('test-kpi', testData, 'timeframe');

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          source: 'timeframe'
        })
      );
    });
  });

  describe('Filter Synchronization', () => {
    it('should emit filters synchronized event', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('filters:synchronized', mockListener);

      const filters = { timeframe: 'week', currency: 'usd' };
      kpiEvents.filtersSynchronized('dashboard', filters);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          source: 'dashboard',
          filters,
          timestamp: expect.any(String)
        })
      );
    });

    it('should update internal filters state', () => {
      const filters = { timeframe: 'month', currency: 'eur' };
      kpiEvents.filtersSynchronized('drawer', filters);

      const currentFilters = kpiEvents.getCurrentFilters();
      expect(currentFilters).toEqual(filters);
    });
  });

  describe('Dashboard Filter Changes', () => {
    it('should emit dashboard filters changed event with default source', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('dashboard:filters-changed', mockListener);

      const filters = { timeframe: 'quarter', currency: 'gbp' };
      kpiEvents.dashboardFiltersChanged(filters);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          filters,
          source: 'dashboard',
          timestamp: expect.any(String)
        })
      );
    });

    it('should emit dashboard filters changed event with custom source', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('dashboard:filters-changed', mockListener);

      const filters = { timeframe: 'week', currency: 'all' };
      kpiEvents.dashboardFiltersChanged(filters, 'drawer');

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          source: 'drawer'
        })
      );
    });
  });

  describe('State Management', () => {
    it('should return current filters state', () => {
      const filters = kpiEvents.getCurrentFilters();
      
      expect(filters).toHaveProperty('timeframe');
      expect(filters).toHaveProperty('currency');
      expect(typeof filters.timeframe).toBe('string');
      expect(typeof filters.currency).toBe('string');
    });

    it('should return current drawer state', () => {
      const state = kpiEvents.getDrawerState();
      
      expect(state).toHaveProperty('isOpen');
      expect(state).toHaveProperty('selectedKpiId');
      expect(state).toHaveProperty('filters');
      expect(typeof state.isOpen).toBe('boolean');
    });

    it('should update drawer state correctly', () => {
      // Initial state
      let state = kpiEvents.getDrawerState();
      expect(state.isOpen).toBe(false);
      expect(state.selectedKpiId).toBeNull();

      // Select KPI
      kpiEvents.selectKPI({ kpiId: 'test-kpi' });
      state = kpiEvents.getDrawerState();
      expect(state.isOpen).toBe(true);
      expect(state.selectedKpiId).toBe('test-kpi');

      // Close drawer
      kpiEvents.closeDrawer();
      state = kpiEvents.getDrawerState();
      expect(state.isOpen).toBe(false);
      expect(state.selectedKpiId).toBeNull();
    });
  });

  describe('Debug Mode', () => {
    it('should enable debug mode without errors', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      expect(() => {
        kpiEvents.enableDebugMode();
      }).not.toThrow();

      // Test that debug logging works
      kpiEvents.selectKPI({ kpiId: 'debug-test' });
      
      // Should have logged the event
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Event Cleanup', () => {
    it('should remove event listeners correctly', () => {
      const mockListener = vi.fn();
      
      kpiEvents.on('test-event', mockListener);
      kpiEvents.emit('test-event', { test: true });
      expect(mockListener).toHaveBeenCalledTimes(1);

      kpiEvents.off('test-event', mockListener);
      kpiEvents.emit('test-event', { test: true });
      expect(mockListener).toHaveBeenCalledTimes(1); // Should not be called again
    });

    it('should remove all listeners', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();
      
      kpiEvents.on('event1', mockListener1);
      kpiEvents.on('event2', mockListener2);

      kpiEvents.removeAllListeners();

      kpiEvents.emit('event1', {});
      kpiEvents.emit('event2', {});

      expect(mockListener1).not.toHaveBeenCalled();
      expect(mockListener2).not.toHaveBeenCalled();
    });
  });
});
