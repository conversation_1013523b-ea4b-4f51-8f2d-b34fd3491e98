/**
 * Drawer Filters Hook for DataHero4 Timeframe Synchronization
 * ===========================================================
 * 
 * Custom hook for managing drawer-specific filters while maintaining
 * synchronization with dashboard filters. Provides independent filter
 * state for the drawer with optional bidirectional synchronization.
 * 
 * Features:
 * - Independent timeframe and currency state for drawer
 * - Synchronization with dashboard filters when needed
 * - Event emission for filter changes
 * - Debounced updates to prevent excessive API calls
 * - Automatic data refresh when filters change
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { kpiEvents } from '@/lib/events';

// Types for drawer filters
export type TimeframeOption = '1d' | 'week' | 'month' | 'quarter';
export type CurrencyOption = 'all' | 'usd' | 'eur' | 'gbp';

export interface DrawerFilters {
  timeframe: TimeframeOption;
  currency: CurrencyOption;
}

export interface UseDrawerFiltersProps {
  /** Initial timeframe value */
  initialTimeframe?: TimeframeOption;
  /** Initial currency value */
  initialCurrency?: CurrencyOption;
  /** Whether to sync with dashboard filters */
  syncWithDashboard?: boolean;
  /** Whether to emit events on filter changes */
  emitEvents?: boolean;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** KPI ID for event context */
  kpiId?: string | null;
}

export interface UseDrawerFiltersReturn {
  /** Current drawer filters */
  filters: DrawerFilters;
  /** Update timeframe */
  setTimeframe: (timeframe: TimeframeOption) => void;
  /** Update currency */
  setCurrency: (currency: CurrencyOption) => void;
  /** Update multiple filters at once */
  updateFilters: (filters: Partial<DrawerFilters>) => void;
  /** Whether filters are currently changing */
  isChanging: boolean;
  /** Reset filters to initial values */
  resetFilters: () => void;
  /** Sync with dashboard filters */
  syncWithDashboard: (dashboardFilters: DrawerFilters) => void;
}

/**
 * Hook for managing drawer filters with synchronization
 */
export const useDrawerFilters = ({
  initialTimeframe = 'week',
  initialCurrency = 'all',
  syncWithDashboard = true,
  emitEvents = true,
  debounceDelay = 300,
  kpiId = null
}: UseDrawerFiltersProps = {}): UseDrawerFiltersReturn => {
  
  // Internal state
  const [filters, setFilters] = useState<DrawerFilters>({
    timeframe: initialTimeframe,
    currency: initialCurrency
  });
  
  const [isChanging, setIsChanging] = useState(false);
  const [initialFilters] = useState<DrawerFilters>({
    timeframe: initialTimeframe,
    currency: initialCurrency
  });

  // Debounced filter change handler
  const debouncedFilterChange = useDebouncedCallback(
    (newFilters: DrawerFilters, source: 'timeframe' | 'currency' | 'batch') => {
      setIsChanging(false);
      
      if (emitEvents) {
        // Emit specific events based on what changed
        kpiEvents.emit('drawer:filters-changed', {
          filters: newFilters,
          kpiId,
          source,
          timestamp: new Date().toISOString()
        });

        // Emit timeframe-specific event if timeframe changed
        if (source === 'timeframe' || source === 'batch') {
          kpiEvents.emit('drawer:timeframe-changed', {
            timeframe: newFilters.timeframe,
            kpiId: kpiId || 'unknown',
            timestamp: new Date().toISOString()
          });
        }
      }
    },
    debounceDelay
  );

  // Update timeframe
  const setTimeframe = useCallback((timeframe: TimeframeOption) => {
    if (timeframe === filters.timeframe) return;

    setIsChanging(true);
    const newFilters = { ...filters, timeframe };
    setFilters(newFilters);
    debouncedFilterChange(newFilters, 'timeframe');
  }, [filters, debouncedFilterChange]);

  // Update currency
  const setCurrency = useCallback((currency: CurrencyOption) => {
    if (currency === filters.currency) return;

    setIsChanging(true);
    const newFilters = { ...filters, currency };
    setFilters(newFilters);
    debouncedFilterChange(newFilters, 'currency');
  }, [filters, debouncedFilterChange]);

  // Update multiple filters at once
  const updateFilters = useCallback((partialFilters: Partial<DrawerFilters>) => {
    const newFilters = { ...filters, ...partialFilters };
    
    // Check if anything actually changed
    const hasChanges = Object.keys(partialFilters).some(
      key => partialFilters[key as keyof DrawerFilters] !== filters[key as keyof DrawerFilters]
    );

    if (!hasChanges) return;

    setIsChanging(true);
    setFilters(newFilters);
    debouncedFilterChange(newFilters, 'batch');
  }, [filters, debouncedFilterChange]);

  // Reset filters to initial values
  const resetFilters = useCallback(() => {
    setIsChanging(true);
    setFilters(initialFilters);
    debouncedFilterChange(initialFilters, 'batch');
  }, [initialFilters, debouncedFilterChange]);

  // Sync with dashboard filters
  const syncWithDashboardFilters = useCallback((dashboardFilters: DrawerFilters) => {
    if (!syncWithDashboard) return;

    const hasChanges = 
      dashboardFilters.timeframe !== filters.timeframe ||
      dashboardFilters.currency !== filters.currency;

    if (hasChanges) {
      setIsChanging(true);
      setFilters(dashboardFilters);
      
      if (emitEvents) {
        kpiEvents.emit('drawer:synced-with-dashboard', {
          filters: dashboardFilters,
          kpiId,
          timestamp: new Date().toISOString()
        });
      }
      
      // Don't debounce sync operations
      setIsChanging(false);
    }
  }, [filters, syncWithDashboard, emitEvents, kpiId]);

  // Listen for dashboard filter changes if sync is enabled
  useEffect(() => {
    if (!syncWithDashboard) return;

    const handleDashboardFiltersChanged = (event: any) => {
      if (event.source !== 'drawer') { // Avoid circular updates
        syncWithDashboardFilters(event.filters);
      }
    };

    kpiEvents.on('dashboard:filters-changed', handleDashboardFiltersChanged);

    return () => {
      kpiEvents.off('dashboard:filters-changed', handleDashboardFiltersChanged);
    };
  }, [syncWithDashboard, syncWithDashboardFilters]);

  // Memoized return value
  const returnValue = useMemo(() => ({
    filters,
    setTimeframe,
    setCurrency,
    updateFilters,
    isChanging,
    resetFilters,
    syncWithDashboard: syncWithDashboardFilters
  }), [
    filters,
    setTimeframe,
    setCurrency,
    updateFilters,
    isChanging,
    resetFilters,
    syncWithDashboardFilters
  ]);

  return returnValue;
};

/**
 * Hook for drawer filters with automatic KPI data refresh
 */
export const useDrawerFiltersWithRefresh = (
  props: UseDrawerFiltersProps & {
    onFiltersChange?: (filters: DrawerFilters) => void;
  }
) => {
  const { onFiltersChange, ...filterProps } = props;
  const drawerFilters = useDrawerFilters(filterProps);

  // Auto-refresh when filters change
  useEffect(() => {
    if (onFiltersChange && !drawerFilters.isChanging) {
      onFiltersChange(drawerFilters.filters);
    }
  }, [drawerFilters.filters, drawerFilters.isChanging, onFiltersChange]);

  return drawerFilters;
};

/**
 * Utility function to get filter labels
 */
export const getFilterLabels = () => ({
  timeframe: {
    '1d': 'Hoje',
    'week': '7 dias',
    'month': '30 dias',
    'quarter': '3 meses'
  },
  currency: {
    'all': 'Todas',
    'usd': 'USD/BRL',
    'eur': 'EUR/BRL',
    'gbp': 'GBP/BRL'
  }
});

/**
 * Utility function to check if filters are equal
 */
export const areFiltersEqual = (filters1: DrawerFilters, filters2: DrawerFilters): boolean => {
  return filters1.timeframe === filters2.timeframe && filters1.currency === filters2.currency;
};

export default useDrawerFilters;
