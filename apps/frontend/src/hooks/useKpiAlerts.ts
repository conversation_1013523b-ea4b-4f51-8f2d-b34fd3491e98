import { useState } from 'react';

// Simplified alert config type for now
interface SimpleAlertConfig {
  enabled: boolean;
  warningThreshold?: number;
  criticalThreshold?: number;
}

interface UseKpiAlertsReturn {
  alertConfigs: Record<string, SimpleAlertConfig | null>;
  isLoading: boolean;
  error: string | null;
  loadAlertConfig: (kpiId: string) => Promise<void>;
  hasAlertConfig: (kpiId: string) => boolean;
  getAlertConfig: (kpiId: string) => SimpleAlertConfig | null;
  shouldShowAlertLines: (kpiId: string, isFavorited: boolean) => boolean;
  loadMultipleAlertConfigs: (kpiIds: string[]) => Promise<void>;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const useKpiAlerts = (): UseKpiAlertsReturn => {
  const [alertConfigs, setAlertConfigs] = useState<Record<string, SimpleAlertConfig | null>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load alert configuration for a specific KPI from the real API
   */
  const loadAlertConfig = async (kpiId: string) => {
    // Prevent duplicate loading
    if (alertConfigs[kpiId] !== undefined) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log(`🔍 Loading alert config for KPI: ${kpiId}`);

      const response = await fetch(`${API_BASE_URL}/api/alerts/config/${kpiId}?user_id=test_user_ceo`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Alert config response for ${kpiId}:`, data);

        if (data.success && data.config) {
          const config: SimpleAlertConfig = {
            enabled: data.config.enabled,
            warningThreshold: data.config.warningThreshold,
            criticalThreshold: data.config.criticalThreshold
          };

          setAlertConfigs(prev => ({
            ...prev,
            [kpiId]: config
          }));

          console.log(`✅ Alert config loaded for ${kpiId}:`, config);
        } else {
          // No config found
          setAlertConfigs(prev => ({
            ...prev,
            [kpiId]: null
          }));
          console.log(`❌ No alert config found for ${kpiId}`);
        }
      } else {
        console.error(`❌ API error for ${kpiId}:`, response.status, response.statusText);
        // API error - treat as no config
        setAlertConfigs(prev => ({
          ...prev,
          [kpiId]: null
        }));
      }
    } catch (err) {
      console.error(`❌ Error loading alert config for KPI ${kpiId}:`, err);
      setAlertConfigs(prev => ({
        ...prev,
        [kpiId]: null
      }));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Check if a KPI has alert configuration
   */
  const hasAlertConfig = (kpiId: string): boolean => {
    const config = alertConfigs[kpiId];
    return config !== null && config !== undefined && config.enabled;
  };

  /**
   * Get alert configuration for a KPI
   */
  const getAlertConfig = (kpiId: string): SimpleAlertConfig | null => {
    return alertConfigs[kpiId] || null;
  };

  /**
   * Determine if alert threshold lines should be shown
   * Lines are shown when:
   * 1. KPI has alert configuration enabled
   * 2. At least one threshold is configured
   * 3. KPI is favorited/prioritized (for dashboard view)
   */
  const shouldShowAlertLines = (kpiId: string, isFavorited: boolean): boolean => {
    const config = getAlertConfig(kpiId);
    if (!config || !config.enabled) return false;

    // Check if at least one threshold is configured
    const hasWarning = config.warningThreshold !== undefined && config.warningThreshold !== null;
    const hasCritical = config.criticalThreshold !== undefined && config.criticalThreshold !== null;

    if (!hasWarning && !hasCritical) return false;

    // For dashboard view, only show on favorited KPIs
    // For drawer view, this will be overridden in the component
    return isFavorited;
  };

  /**
   * Batch load alert configurations for multiple KPIs
   */
  const loadMultipleAlertConfigs = async (kpiIds: string[]) => {
    const unloadedKpiIds = kpiIds.filter(kpiId => alertConfigs[kpiId] === undefined);

    if (unloadedKpiIds.length === 0) return;

    // Load configs in parallel
    await Promise.all(unloadedKpiIds.map(kpiId => loadAlertConfig(kpiId)));
  };

  /**
   * Determine if alert threshold lines should be shown in drawer (ignores favorited status)
   */
  const shouldShowAlertLinesInDrawer = (kpiId: string): boolean => {
    const config = getAlertConfig(kpiId);
    if (!config || !config.enabled) return false;

    // Check if at least one threshold is configured
    const hasWarning = config.warningThreshold !== undefined && config.warningThreshold !== null;
    const hasCritical = config.criticalThreshold !== undefined && config.criticalThreshold !== null;

    return hasWarning || hasCritical;
  };

  return {
    alertConfigs,
    isLoading,
    error,
    loadAlertConfig,
    hasAlertConfig,
    getAlertConfig,
    shouldShowAlertLines,
    shouldShowAlertLinesInDrawer,
    // Expose batch loading for performance
    loadMultipleAlertConfigs
  };
};
