import { useState, useCallback, useEffect } from 'react';
import { kpiEvents } from '@/lib/events';

// Simplified alert config type for now
interface SimpleAlertConfig {
  enabled: boolean;
  warningThreshold?: number;
  criticalThreshold?: number;
}

interface UseKpiAlertsReturn {
  alertConfigs: Record<string, SimpleAlertConfig | null>;
  isLoading: boolean;
  error: string | null;
  loadAlertConfig: (kpiId: string) => Promise<void>;
  hasAlertConfig: (kpiId: string) => boolean;
  getAlertConfig: (kpiId: string) => SimpleAlertConfig | null;
  shouldShowAlertLines: (kpiId: string, isFavorited: boolean) => boolean;
  loadMultipleAlertConfigs: (kpiIds: string[]) => Promise<void>;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const useKpiAlerts = (): UseKpiAlertsReturn => {
  const [alertConfigs, setAlertConfigs] = useState<Record<string, SimpleAlertConfig | null>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Listen for real-time alert configuration updates
  useEffect(() => {
    const handleAlertConfigUpdated = (event: any) => {
      console.log(`🔄 [useKpiAlerts] Real-time update for KPI: ${event.kpiId}`, event.config);
      setAlertConfigs(prev => ({
        ...prev,
        [event.kpiId]: event.config
      }));
    };

    const handleAlertConfigDeleted = (event: any) => {
      console.log(`🗑️ [useKpiAlerts] Real-time deletion for KPI: ${event.kpiId}`);
      setAlertConfigs(prev => ({
        ...prev,
        [event.kpiId]: { enabled: false }
      }));
    };

    // Subscribe to events
    kpiEvents.on('alert:config-updated', handleAlertConfigUpdated);
    kpiEvents.on('alert:config-deleted', handleAlertConfigDeleted);

    // Cleanup
    return () => {
      kpiEvents.off('alert:config-updated', handleAlertConfigUpdated);
      kpiEvents.off('alert:config-deleted', handleAlertConfigDeleted);
    };
  }, []);

  /**
   * Load alert configuration for a specific KPI from the real API
   * NO MOCKS OR FALLBACKS - only real backend data
   */
  const loadAlertConfig = useCallback(async (kpiId: string) => {
    // Prevent duplicate loading
    if (alertConfigs[kpiId] !== undefined) {
      console.log(`🔄 [useKpiAlerts] Config for ${kpiId} already loaded/loading`);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log(`🔍 [useKpiAlerts] Loading REAL alert config for KPI: ${kpiId} from ${API_BASE_URL}`);

      // Set loading state immediately
      setAlertConfigs(prev => ({ ...prev, [kpiId]: null }));

      const response = await fetch(`${API_BASE_URL}/api/alerts/config/${kpiId}?user_id=test_user_ceo`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ [useKpiAlerts] Real alert config response for ${kpiId}:`, data);

        if (data.success && data.config) {
          const config: SimpleAlertConfig = {
            enabled: data.config.enabled || false,
            warningThreshold: data.config.warningThreshold,
            criticalThreshold: data.config.criticalThreshold
          };

          setAlertConfigs(prev => ({
            ...prev,
            [kpiId]: config
          }));

          console.log(`✅ [useKpiAlerts] REAL alert config loaded for ${kpiId}:`, config);
        } else {
          // No config found - this is valid, not an error
          setAlertConfigs(prev => ({
            ...prev,
            [kpiId]: { enabled: false }
          }));
          console.log(`ℹ️ [useKpiAlerts] No alert config found for ${kpiId} - setting as disabled`);
        }
      } else {
        console.error(`❌ [useKpiAlerts] API error for ${kpiId}:`, response.status, response.statusText);
        const errorText = await response.text();
        console.error(`❌ [useKpiAlerts] Error details:`, errorText);

        // API error - fail fast, no fallbacks
        setAlertConfigs(prev => ({
          ...prev,
          [kpiId]: { enabled: false }
        }));
        setError(`API error loading alert config for ${kpiId}: ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ [useKpiAlerts] Network error loading alert config for ${kpiId}:`, error);
      setError(`Network error loading alert config for ${kpiId}: ${error}`);

      // Network error - fail fast, no fallbacks
      setAlertConfigs(prev => ({
        ...prev,
        [kpiId]: { enabled: false }
      }));
    } finally {
      setIsLoading(false);
    }
  }, [alertConfigs, API_BASE_URL]);

  /**
   * Check if a KPI has alert configuration
   */
  const hasAlertConfig = (kpiId: string): boolean => {
    const config = alertConfigs[kpiId];
    return config !== null && config !== undefined && config.enabled;
  };

  /**
   * Get alert configuration for a KPI
   */
  const getAlertConfig = (kpiId: string): SimpleAlertConfig | null => {
    return alertConfigs[kpiId] || null;
  };

  /**
   * Determine if alert threshold lines should be shown
   * Lines are shown when:
   * 1. KPI is favorited/prioritized
   * 2. KPI has alert configuration enabled
   * 3. At least one threshold is configured
   */
  const shouldShowAlertLines = (kpiId: string, isFavorited: boolean): boolean => {
    if (!isFavorited) return false;

    const config = getAlertConfig(kpiId);
    if (!config || !config.enabled) return false;

    // Check if at least one threshold is configured
    const hasWarning = config.warningThreshold !== undefined && config.warningThreshold !== null;
    const hasCritical = config.criticalThreshold !== undefined && config.criticalThreshold !== null;

    return hasWarning || hasCritical;
  };

  /**
   * Batch load alert configurations for multiple KPIs
   */
  const loadMultipleAlertConfigs = async (kpiIds: string[]) => {
    const unloadedKpiIds = kpiIds.filter(kpiId => alertConfigs[kpiId] === undefined);

    if (unloadedKpiIds.length === 0) return;

    // Load configs in parallel
    await Promise.all(unloadedKpiIds.map(kpiId => loadAlertConfig(kpiId)));
  };

  /**
   * Determine if alert threshold lines should be shown in drawer (ignores favorited status)
   */
  const shouldShowAlertLinesInDrawer = (kpiId: string): boolean => {
    const config = getAlertConfig(kpiId);
    if (!config || !config.enabled) return false;

    // Check if at least one threshold is configured
    const hasWarning = config.warningThreshold !== undefined && config.warningThreshold !== null;
    const hasCritical = config.criticalThreshold !== undefined && config.criticalThreshold !== null;

    return hasWarning || hasCritical;
  };

  /**
   * Invalidate cache for a specific KPI to force reload
   * Used when alert configurations are updated
   */
  const invalidateAlertConfig = useCallback((kpiId: string) => {
    console.log(`🔄 [useKpiAlerts] Invalidating cache for KPI: ${kpiId}`);
    setAlertConfigs(prev => {
      const newConfigs = { ...prev };
      delete newConfigs[kpiId];
      return newConfigs;
    });
  }, []);

  /**
   * Refresh alert configuration for a specific KPI (force reload)
   */
  const refreshAlertConfig = useCallback(async (kpiId: string) => {
    console.log(`🔄 [useKpiAlerts] Force refreshing alert config for KPI: ${kpiId}`);
    invalidateAlertConfig(kpiId);
    await loadAlertConfig(kpiId);
  }, [invalidateAlertConfig, loadAlertConfig]);

  return {
    alertConfigs,
    isLoading,
    error,
    loadAlertConfig,
    hasAlertConfig,
    getAlertConfig,
    shouldShowAlertLines,
    shouldShowAlertLinesInDrawer,
    invalidateAlertConfig,
    refreshAlertConfig,
    // Expose batch loading for performance
    loadMultipleAlertConfigs
  };
};
