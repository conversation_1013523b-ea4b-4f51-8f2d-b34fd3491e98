/**
 * usePersonalization Hook - DataHero4 Week 5
 * ==========================================
 * 
 * Custom React hook for profile-aware personalization state management.
 * Handles profile detection, configuration, preferences, and KPI recommendations.
 * 
 * Features:
 * - Profile detection and auto-configuration
 * - User preferences management
 * - Profile-aware KPI recommendations
 * - Cache strategy optimization
 * - Error handling and loading states
 * - Local storage persistence
 * 
 * Integrates with backend Profile API endpoints.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-21
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  UserProfile,
  ProfileType,
  ProfileDetectionResult,
  ProfileConfigurationRequest,
  ProfilePreferences,
  ProfileCharacteristics,
  CacheStrategy,
  UsePersonalizationReturn,
  PROFILE_CHARACTERISTICS,
  DEFAULT_PREFERENCES
} from '@/types/profile';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Local Storage Keys
const STORAGE_KEYS = {
  USER_PROFILE: 'datahero4_user_profile',
  PROFILE_CACHE: 'datahero4_profile_cache',
  PREFERENCES: 'datahero4_preferences'
} as const;

// Cache TTL for profile data (5 minutes)
const PROFILE_CACHE_TTL = 5 * 60 * 1000;

interface ProfileCache {
  profile: UserProfile;
  timestamp: number;
  ttl: number;
}

/**
 * Custom hook for profile-aware personalization
 */
export const usePersonalization = (userId?: string): UsePersonalizationReturn => {
  // State management
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isProfileLoaded, setIsProfileLoaded] = useState(false);
  const [isDetecting, setIsDetecting] = useState(false);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Profile characteristics (static data)
  const profileCharacteristics = useMemo(() => 
    Object.values(PROFILE_CHARACTERISTICS), 
    []
  );

  // Derived state
  const isProfileSetupRequired = useMemo(() => 
    isProfileLoaded && !userProfile && userId,
    [isProfileLoaded, userProfile, userId]
  );

  const recommendedKpis = useMemo(() => 
    userProfile ? PROFILE_CHARACTERISTICS[userProfile.profileType].recommendedKpis : [],
    [userProfile]
  );

  const cacheStrategy = useMemo(() => 
    userProfile ? PROFILE_CHARACTERISTICS[userProfile.profileType].cacheStrategy : 
    { ttl: 900, layer: 'cache' as const, priority: 'efficiency' as const },
    [userProfile]
  );

  // Error handling
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Local storage helpers
  const saveToStorage = useCallback((key: string, data: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }, []);

  const loadFromStorage = useCallback((key: string) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
      return null;
    }
  }, []);

  // Check if cached profile is valid
  const isCacheValid = useCallback((cache: ProfileCache | null): boolean => {
    if (!cache) return false;
    const now = Date.now();
    return (now - cache.timestamp) < cache.ttl;
  }, []);

  // Load profile from cache or storage
  const loadProfile = useCallback(async () => {
    if (!userId) {
      setIsProfileLoaded(true);
      return;
    }

    try {
      // Try to load from cache first
      const cachedProfile = loadFromStorage(STORAGE_KEYS.PROFILE_CACHE) as ProfileCache | null;

      if (cachedProfile && isCacheValid(cachedProfile) && cachedProfile.profile.userId === userId) {
        console.log('🔄 [usePersonalization] Loading profile from cache:', cachedProfile.profile.profileType);
        setUserProfile(cachedProfile.profile);
        setIsProfileLoaded(true);
        return;
      }

      // Try to load from persistent storage
      const storedProfile = loadFromStorage(STORAGE_KEYS.USER_PROFILE) as UserProfile | null;

      if (storedProfile && storedProfile.userId === userId) {
        console.log('🔄 [usePersonalization] Loading profile from storage:', storedProfile.profileType);
        setUserProfile(storedProfile);

        // Cache the loaded profile
        const cache: ProfileCache = {
          profile: storedProfile,
          timestamp: Date.now(),
          ttl: PROFILE_CACHE_TTL
        };
        saveToStorage(STORAGE_KEYS.PROFILE_CACHE, cache);
        setIsProfileLoaded(true);
        return;
      }

      // Try to load last selected profile from simple localStorage
      const lastProfile = localStorage.getItem('datahero4_last_profile');
      if (lastProfile) {
        console.log('🔄 [usePersonalization] Loading last selected profile:', lastProfile);

        // Create a basic profile from last selection
        const basicProfile: UserProfile = {
          userId: userId,
          profileType: lastProfile as ProfileType,
          preferences: DEFAULT_PREFERENCES,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        setUserProfile(basicProfile);

        // Save to persistent storage for future use
        saveToStorage(STORAGE_KEYS.USER_PROFILE, basicProfile);

        // Cache the profile
        const cache: ProfileCache = {
          profile: basicProfile,
          timestamp: Date.now(),
          ttl: PROFILE_CACHE_TTL
        };
        saveToStorage(STORAGE_KEYS.PROFILE_CACHE, cache);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      setError('Failed to load profile');
    } finally {
      setIsProfileLoaded(true);
    }
  }, [userId, loadFromStorage, saveToStorage, isCacheValid]);

  // Save profile to storage and cache
  const saveProfile = useCallback((profile: UserProfile) => {
    try {
      // Save to persistent storage
      saveToStorage(STORAGE_KEYS.USER_PROFILE, profile);
      
      // Save to cache
      const cache: ProfileCache = {
        profile,
        timestamp: Date.now(),
        ttl: PROFILE_CACHE_TTL
      };
      saveToStorage(STORAGE_KEYS.PROFILE_CACHE, cache);
      
      setUserProfile(profile);
    } catch (error) {
      console.error('Error saving profile:', error);
      setError('Failed to save profile');
    }
  }, [saveToStorage]);

  // API call helper
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API Error: ${response.status}`);
    }

    return response.json();
  }, []);

  // Profile detection
  const detectProfile = useCallback(async (targetUserId: string): Promise<ProfileDetectionResult> => {
    if (!targetUserId) {
      throw new Error('User ID is required for profile detection');
    }

    setIsDetecting(true);
    setError(null);

    try {
      const response = await apiCall('/api/profiles/detect', {
        method: 'POST',
        body: JSON.stringify({
          userId: targetUserId,
          analysisDays: 30
        })
      });

      return response as ProfileDetectionResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile detection failed';
      setError(errorMessage);
      throw error;
    } finally {
      setIsDetecting(false);
    }
  }, [apiCall]);

  // Profile configuration
  const configureProfile = useCallback(async (config: ProfileConfigurationRequest): Promise<void> => {
    setIsConfiguring(true);
    setError(null);

    try {
      const response = await apiCall('/api/profiles/configure', {
        method: 'POST',
        body: JSON.stringify(config)
      });

      // Create full user profile object
      const userProfile: UserProfile = {
        id: `profile_${config.userId}_${Date.now()}`,
        userId: config.userId,
        profileType: config.profileType,
        displayName: PROFILE_CHARACTERISTICS[config.profileType].name,
        description: PROFILE_CHARACTERISTICS[config.profileType].description,
        preferences: config.preferences,
        selectedKpis: PROFILE_CHARACTERISTICS[config.profileType].recommendedKpis,
        cacheStrategy: PROFILE_CHARACTERISTICS[config.profileType].cacheStrategy,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      saveProfile(userProfile);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile configuration failed';
      setError(errorMessage);
      throw error;
    } finally {
      setIsConfiguring(false);
    }
  }, [apiCall, saveProfile]);

  // Update preferences
  const updatePreferences = useCallback(async (preferences: Partial<ProfilePreferences>): Promise<void> => {
    if (!userProfile) {
      throw new Error('No profile loaded');
    }

    setIsUpdating(true);
    setError(null);

    try {
      // Update locally first for immediate UI feedback
      const updatedProfile: UserProfile = {
        ...userProfile,
        preferences: { ...userProfile.preferences, ...preferences },
        updatedAt: new Date().toISOString()
      };

      saveProfile(updatedProfile);

      // Sync with backend
      await apiCall('/api/profiles/configure', {
        method: 'POST',
        body: JSON.stringify({
          userId: userProfile.userId,
          profileType: userProfile.profileType,
          preferences: updatedProfile.preferences
        })
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update preferences';
      setError(errorMessage);
      
      // Revert local changes on error
      if (userProfile) {
        saveProfile(userProfile);
      }
      
      throw error;
    } finally {
      setIsUpdating(false);
    }
  }, [userProfile, apiCall, saveProfile]);

  // Load profile on mount or userId change
  useEffect(() => {
    loadProfile();
  }, [loadProfile]);

  // Auto-detect profile if none exists and userId is provided
  // DISABLED: Auto-detect profile causing 404 errors and infinite loops
  // useEffect(() => {
  //   if (isProfileSetupRequired && userId && !isDetecting) {
  //     detectProfile(userId)
  //       .then((result) => {
  //         if (result.detectedProfile && result.confidence >= 0.30) {
  //           // Auto-configure with detected profile
  //           const config: ProfileConfigurationRequest = {
  //             userId,
  //             profileType: result.detectedProfile,
  //             preferences: DEFAULT_PREFERENCES
  //           };
  //
  //           return configureProfile(config);
  //         }
  //       })
  //       .catch((error) => {
  //         console.warn('Auto profile detection failed:', error);
  //         // Don't set error state for auto-detection failures
  //       });
  //   }
  // }, [isProfileSetupRequired, userId, isDetecting, detectProfile, configureProfile]);

  return {
    // Profile state
    userProfile,
    isProfileLoaded,
    isProfileSetupRequired,
    
    // Profile actions
    detectProfile,
    configureProfile,
    updatePreferences,
    
    // Profile data
    profileCharacteristics,
    recommendedKpis,
    cacheStrategy,
    
    // Loading states
    isDetecting,
    isConfiguring,
    isUpdating,
    
    // Error handling
    error,
    clearError
  };
};
