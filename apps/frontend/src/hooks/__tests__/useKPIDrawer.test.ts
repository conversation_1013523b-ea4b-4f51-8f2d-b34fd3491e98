/**
 * useKPIDrawer Hook Tests
 * =======================
 * 
 * Unit tests for the KPI drawer state management hook.
 * Tests drawer open/close state, card selection, and event handling.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { useKPIDrawer } from '../useKPIDrawer';
import { kpiEvents } from '../../lib/events';

// Mock the events system
const mockEmit = vi.fn();
const mockOn = vi.fn();
const mockOff = vi.fn();

vi.mock('../../lib/events', () => ({
  kpiEvents: {
    emit: mockEmit,
    on: mockOn,
    off: mockOff,
    selectKPI: vi.fn(),
    closeDrawer: vi.fn()
  }
}));

describe('useKPIDrawer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useKPIDrawer());

      expect(result.current.isOpen).toBe(false);
      expect(result.current.currentKPI).toBeNull();
      expect(result.current.selectedCardId).toBeNull();
      expect(result.current.superAgentActive).toBe(false);
      expect(result.current.originalElement).toBeNull();
      expect(result.current.originalRect).toBeNull();
    });

    it('should register event listeners on mount', () => {
      renderHook(() => useKPIDrawer());

      expect(mockOn).toHaveBeenCalledWith('kpi:selected', expect.any(Function));
      expect(mockOn).toHaveBeenCalledWith('drawer:close', expect.any(Function));
    });

    it('should cleanup event listeners on unmount', () => {
      const { unmount } = renderHook(() => useKPIDrawer());

      unmount();

      expect(mockOff).toHaveBeenCalledWith('kpi:selected', expect.any(Function));
      expect(mockOff).toHaveBeenCalledWith('drawer:close', expect.any(Function));
    });
  });

  describe('Card Selection', () => {
    it('should provide isCardSelected helper function', () => {
      const { result } = renderHook(() => useKPIDrawer());

      expect(typeof result.current.isCardSelected).toBe('function');
      expect(result.current.isCardSelected('test-kpi')).toBe(false);
    });

    it('should return true for selected card', () => {
      const { result } = renderHook(() => useKPIDrawer());

      // Simulate KPI selection event
      act(() => {
        const handleKPISelected = mockOn.mock.calls.find(
          call => call[0] === 'kpi:selected'
        )?.[1];
        
        if (handleKPISelected) {
          handleKPISelected({
            kpiId: 'test-kpi',
            element: document.createElement('div'),
            cardRect: new DOMRect()
          });
        }
      });

      expect(result.current.isCardSelected('test-kpi')).toBe(true);
      expect(result.current.isCardSelected('other-kpi')).toBe(false);
    });

    it('should provide setSelectedCard function', () => {
      const { result } = renderHook(() => useKPIDrawer());

      expect(typeof result.current.setSelectedCard).toBe('function');

      act(() => {
        result.current.setSelectedCard('manual-kpi');
      });

      expect(result.current.selectedCardId).toBe('manual-kpi');
      expect(result.current.isCardSelected('manual-kpi')).toBe(true);
    });

    it('should clear selected card when set to null', () => {
      const { result } = renderHook(() => useKPIDrawer());

      // Set a card first
      act(() => {
        result.current.setSelectedCard('test-kpi');
      });

      expect(result.current.selectedCardId).toBe('test-kpi');

      // Clear selection
      act(() => {
        result.current.setSelectedCard(null);
      });

      expect(result.current.selectedCardId).toBeNull();
      expect(result.current.isCardSelected('test-kpi')).toBe(false);
    });
  });

  describe('KPI Selection Event Handling', () => {
    it('should handle KPI selection event correctly', () => {
      const { result } = renderHook(() => useKPIDrawer());

      const mockElement = document.createElement('div');
      const mockRect = new DOMRect(0, 0, 100, 100);

      act(() => {
        const handleKPISelected = mockOn.mock.calls.find(
          call => call[0] === 'kpi:selected'
        )?.[1];
        
        if (handleKPISelected) {
          handleKPISelected({
            kpiId: 'test-kpi',
            element: mockElement,
            cardRect: mockRect
          });
        }
      });

      expect(result.current.isOpen).toBe(true);
      expect(result.current.currentKPI).toBe('test-kpi');
      expect(result.current.selectedCardId).toBe('test-kpi');
      expect(result.current.originalElement).toBe(mockElement);
      expect(result.current.originalRect).toBe(mockRect);
      expect(result.current.superAgentActive).toBe(false);
    });

    it('should handle KPI selection without element/rect', () => {
      const { result } = renderHook(() => useKPIDrawer());

      act(() => {
        const handleKPISelected = mockOn.mock.calls.find(
          call => call[0] === 'kpi:selected'
        )?.[1];
        
        if (handleKPISelected) {
          handleKPISelected({
            kpiId: 'test-kpi'
          });
        }
      });

      expect(result.current.isOpen).toBe(true);
      expect(result.current.currentKPI).toBe('test-kpi');
      expect(result.current.selectedCardId).toBe('test-kpi');
      expect(result.current.originalElement).toBeNull();
      expect(result.current.originalRect).toBeNull();
    });
  });

  describe('Drawer Close Event Handling', () => {
    it('should handle drawer close event correctly', () => {
      const { result } = renderHook(() => useKPIDrawer());

      // First open the drawer
      act(() => {
        const handleKPISelected = mockOn.mock.calls.find(
          call => call[0] === 'kpi:selected'
        )?.[1];
        
        if (handleKPISelected) {
          handleKPISelected({
            kpiId: 'test-kpi',
            element: document.createElement('div'),
            cardRect: new DOMRect()
          });
        }
      });

      expect(result.current.isOpen).toBe(true);
      expect(result.current.selectedCardId).toBe('test-kpi');

      // Now close the drawer
      act(() => {
        const handleDrawerClose = mockOn.mock.calls.find(
          call => call[0] === 'drawer:close'
        )?.[1];
        
        if (handleDrawerClose) {
          handleDrawerClose();
        }
      });

      expect(result.current.isOpen).toBe(false);
      expect(result.current.selectedCardId).toBeNull();
    });

    it('should reset state after animation delay on close', async () => {
      vi.useFakeTimers();
      
      const { result } = renderHook(() => useKPIDrawer());

      const mockElement = document.createElement('div');
      const mockRect = new DOMRect();

      // Open drawer
      act(() => {
        const handleKPISelected = mockOn.mock.calls.find(
          call => call[0] === 'kpi:selected'
        )?.[1];
        
        if (handleKPISelected) {
          handleKPISelected({
            kpiId: 'test-kpi',
            element: mockElement,
            cardRect: mockRect
          });
        }
      });

      // Close drawer
      act(() => {
        const handleDrawerClose = mockOn.mock.calls.find(
          call => call[0] === 'drawer:close'
        )?.[1];
        
        if (handleDrawerClose) {
          handleDrawerClose();
        }
      });

      expect(result.current.originalElement).toBe(mockElement);
      expect(result.current.originalRect).toBe(mockRect);

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(300);
      });

      await waitFor(() => {
        expect(result.current.originalElement).toBeNull();
        expect(result.current.originalRect).toBeNull();
      });

      vi.useRealTimers();
    });
  });

  describe('Super Agent State', () => {
    it('should provide setSuperAgentActive function', () => {
      const { result } = renderHook(() => useKPIDrawer());

      expect(typeof result.current.setSuperAgentActive).toBe('function');
      expect(result.current.superAgentActive).toBe(false);

      act(() => {
        result.current.setSuperAgentActive(true);
      });

      expect(result.current.superAgentActive).toBe(true);
    });
  });

  describe('Close Drawer Function', () => {
    it('should provide closeDrawer function that calls kpiEvents.closeDrawer', () => {
      const { result } = renderHook(() => useKPIDrawer());

      expect(typeof result.current.closeDrawer).toBe('function');

      act(() => {
        result.current.closeDrawer();
      });

      expect(kpiEvents.closeDrawer).toHaveBeenCalled();
    });
  });

  describe('Memoization', () => {
    it('should memoize isCardSelected function', () => {
      const { result, rerender } = renderHook(() => useKPIDrawer());

      const firstIsCardSelected = result.current.isCardSelected;
      
      rerender();
      
      const secondIsCardSelected = result.current.isCardSelected;

      expect(firstIsCardSelected).toBe(secondIsCardSelected);
    });

    it('should memoize setSelectedCard function', () => {
      const { result, rerender } = renderHook(() => useKPIDrawer());

      const firstSetSelectedCard = result.current.setSelectedCard;
      
      rerender();
      
      const secondSetSelectedCard = result.current.setSelectedCard;

      expect(firstSetSelectedCard).toBe(secondSetSelectedCard);
    });
  });
});
