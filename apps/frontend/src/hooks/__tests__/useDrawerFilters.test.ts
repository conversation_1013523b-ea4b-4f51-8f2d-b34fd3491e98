/**
 * useDrawerFilters Hook Tests
 * ===========================
 * 
 * Unit tests for the drawer filters management hook.
 * Tests filter state management, synchronization, and event emission.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { useDrawerFilters } from '../useDrawerFilters';
import { kpiEvents } from '../../lib/events';

// Mock the events system
vi.mock('../../lib/events', () => ({
  kpiEvents: {
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn()
  }
}));

// Mock use-debounce
vi.mock('use-debounce', () => ({
  useDebouncedCallback: (fn: any, delay: number) => {
    // Return the function immediately for testing (no debounce)
    return fn;
  }
}));

describe('useDrawerFilters', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useDrawerFilters());

      expect(result.current.filters).toEqual({
        timeframe: 'week',
        currency: 'all'
      });
      expect(result.current.isChanging).toBe(false);
    });

    it('should initialize with custom values', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({
          initialTimeframe: 'month',
          initialCurrency: 'usd'
        })
      );

      expect(result.current.filters).toEqual({
        timeframe: 'month',
        currency: 'usd'
      });
    });
  });

  describe('Timeframe Updates', () => {
    it('should update timeframe correctly', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ emitEvents: true, kpiId: 'test_kpi' })
      );

      act(() => {
        result.current.setTimeframe('month');
      });

      expect(result.current.filters.timeframe).toBe('month');
      expect(result.current.filters.currency).toBe('all'); // Should remain unchanged
    });

    it('should emit events when timeframe changes', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          emitEvents: true, 
          kpiId: 'test_kpi' 
        })
      );

      act(() => {
        result.current.setTimeframe('month');
      });

      expect(kpiEvents.emit).toHaveBeenCalledWith(
        'drawer:filters-changed',
        expect.objectContaining({
          filters: { timeframe: 'month', currency: 'all' },
          kpiId: 'test_kpi',
          source: 'timeframe'
        })
      );

      expect(kpiEvents.emit).toHaveBeenCalledWith(
        'drawer:timeframe-changed',
        expect.objectContaining({
          timeframe: 'month',
          kpiId: 'test_kpi'
        })
      );
    });

    it('should not emit events when emitEvents is false', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ emitEvents: false })
      );

      act(() => {
        result.current.setTimeframe('month');
      });

      expect(kpiEvents.emit).not.toHaveBeenCalled();
    });

    it('should not update if timeframe is the same', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ initialTimeframe: 'week' })
      );

      const initialFilters = result.current.filters;

      act(() => {
        result.current.setTimeframe('week');
      });

      expect(result.current.filters).toBe(initialFilters);
      expect(kpiEvents.emit).not.toHaveBeenCalled();
    });
  });

  describe('Currency Updates', () => {
    it('should update currency correctly', () => {
      const { result } = renderHook(() => useDrawerFilters());

      act(() => {
        result.current.setCurrency('usd');
      });

      expect(result.current.filters.currency).toBe('usd');
      expect(result.current.filters.timeframe).toBe('week'); // Should remain unchanged
    });

    it('should emit events when currency changes', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          emitEvents: true, 
          kpiId: 'test_kpi' 
        })
      );

      act(() => {
        result.current.setCurrency('eur');
      });

      expect(kpiEvents.emit).toHaveBeenCalledWith(
        'drawer:filters-changed',
        expect.objectContaining({
          filters: { timeframe: 'week', currency: 'eur' },
          kpiId: 'test_kpi',
          source: 'currency'
        })
      );
    });
  });

  describe('Batch Updates', () => {
    it('should update multiple filters at once', () => {
      const { result } = renderHook(() => useDrawerFilters());

      act(() => {
        result.current.updateFilters({
          timeframe: 'month',
          currency: 'usd'
        });
      });

      expect(result.current.filters).toEqual({
        timeframe: 'month',
        currency: 'usd'
      });
    });

    it('should emit batch event for multiple updates', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          emitEvents: true, 
          kpiId: 'test_kpi' 
        })
      );

      act(() => {
        result.current.updateFilters({
          timeframe: 'quarter',
          currency: 'gbp'
        });
      });

      expect(kpiEvents.emit).toHaveBeenCalledWith(
        'drawer:filters-changed',
        expect.objectContaining({
          filters: { timeframe: 'quarter', currency: 'gbp' },
          source: 'batch'
        })
      );
    });

    it('should not update if no changes detected', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          initialTimeframe: 'week',
          initialCurrency: 'all'
        })
      );

      const initialFilters = result.current.filters;

      act(() => {
        result.current.updateFilters({
          timeframe: 'week',
          currency: 'all'
        });
      });

      expect(result.current.filters).toBe(initialFilters);
      expect(kpiEvents.emit).not.toHaveBeenCalled();
    });
  });

  describe('Reset Functionality', () => {
    it('should reset filters to initial values', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({
          initialTimeframe: 'month',
          initialCurrency: 'usd'
        })
      );

      // Change filters
      act(() => {
        result.current.updateFilters({
          timeframe: 'quarter',
          currency: 'eur'
        });
      });

      expect(result.current.filters).toEqual({
        timeframe: 'quarter',
        currency: 'eur'
      });

      // Reset filters
      act(() => {
        result.current.resetFilters();
      });

      expect(result.current.filters).toEqual({
        timeframe: 'month',
        currency: 'usd'
      });
    });
  });

  describe('Dashboard Synchronization', () => {
    it('should sync with dashboard filters when enabled', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ syncWithDashboard: true })
      );

      const dashboardFilters = {
        timeframe: 'quarter' as const,
        currency: 'eur' as const
      };

      act(() => {
        result.current.syncWithDashboard(dashboardFilters);
      });

      expect(result.current.filters).toEqual(dashboardFilters);
    });

    it('should not sync when syncWithDashboard is false', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          syncWithDashboard: false,
          initialTimeframe: 'week',
          initialCurrency: 'all'
        })
      );

      const initialFilters = result.current.filters;
      const dashboardFilters = {
        timeframe: 'quarter' as const,
        currency: 'eur' as const
      };

      act(() => {
        result.current.syncWithDashboard(dashboardFilters);
      });

      expect(result.current.filters).toBe(initialFilters);
    });

    it('should emit sync event when syncing with dashboard', () => {
      const { result } = renderHook(() => 
        useDrawerFilters({ 
          syncWithDashboard: true,
          emitEvents: true,
          kpiId: 'test_kpi'
        })
      );

      const dashboardFilters = {
        timeframe: 'quarter' as const,
        currency: 'eur' as const
      };

      act(() => {
        result.current.syncWithDashboard(dashboardFilters);
      });

      expect(kpiEvents.emit).toHaveBeenCalledWith(
        'drawer:synced-with-dashboard',
        expect.objectContaining({
          filters: dashboardFilters,
          kpiId: 'test_kpi'
        })
      );
    });
  });

  describe('Loading States', () => {
    it('should set isChanging to true during updates', () => {
      const { result } = renderHook(() => useDrawerFilters());

      act(() => {
        result.current.setTimeframe('month');
      });

      // Note: In real implementation, isChanging would be true briefly
      // but our mock debounce executes immediately, so it's false
      expect(result.current.isChanging).toBe(false);
    });
  });
});
