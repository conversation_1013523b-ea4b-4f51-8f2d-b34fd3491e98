/**
 * Tests for useDynamicFilters Hook
 * 
 * Simple tests focusing on core functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDynamicFilters } from '../useDynamicFilters';

// Mock fetch
global.fetch = vi.fn();

describe('useDynamicFilters', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          timeframe: ['1d', 'week', 'month', 'quarter'],
          currency: ['all', 'usd', 'eur', 'gbp'],
          sector: ['cambio', 'crypto'],
          client_id: ['L2M', 'CLIENT2']
        }
      })
    });
  });

  it('should initialize with default filters', () => {
    const { result } = renderHook(() => useDynamicFilters({
      initialFilters: { timeframe: 'week', currency: 'all' }
    }));

    expect(result.current.filters).toEqual({
      timeframe: 'week',
      currency: 'all'
    });
    expect(result.current.isLoading).toBe(true);
  });

  it('should update filters correctly', async () => {
    const { result } = renderHook(() => useDynamicFilters({
      initialFilters: { timeframe: 'week' }
    }));

    act(() => {
      result.current.updateFilter('timeframe', 'month');
    });

    expect(result.current.filters.timeframe).toBe('month');
  });

  it('should reset filters to default values', async () => {
    const { result } = renderHook(() => useDynamicFilters({
      initialFilters: { timeframe: 'week', currency: 'all' }
    }));

    // Change filters
    act(() => {
      result.current.updateFilter('timeframe', 'month');
    });

    // Reset filters (resets to API-defined defaults, not initial filters)
    act(() => {
      result.current.resetFilters();
    });

    // After reset, filters should be empty object (no defaults loaded yet)
    expect(result.current.filters).toEqual({});
  });

  it('should handle bulk filter updates', async () => {
    const { result } = renderHook(() => useDynamicFilters({
      initialFilters: { timeframe: 'week' }
    }));

    const newFilters = { timeframe: 'month', currency: 'usd' };

    act(() => {
      result.current.updateFilters(newFilters);
    });

    expect(result.current.filters).toEqual(newFilters);
  });
});
