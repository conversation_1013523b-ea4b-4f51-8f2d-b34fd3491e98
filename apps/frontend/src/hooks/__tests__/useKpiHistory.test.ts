/**
 * useKpiHistory Hook Tests
 * ========================
 * 
 * Unit tests for the KPI history data fetching hook.
 * Tests data fetching, loading states, error handling, and cache management.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { useKpiHistory, KpiHistoryData } from '../useKpiHistory';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock data
const mockHistoryData: KpiHistoryData = {
  kpi_id: 'test_kpi',
  kpi_name: 'Test KPI',
  timeframe: 'week',
  currency: 'all',
  total_records: 2,
  history_data: [
    {
      period: '2024-01-02',
      value: 100,
      formatted_value: '100',
      change_percent: 5,
      status: 'positive',
      metadata: { type: 'volume' }
    },
    {
      period: '2024-01-01',
      value: 95,
      formatted_value: '95',
      change_percent: -2,
      status: 'negative',
      metadata: { type: 'volume' }
    }
  ],
  calculation_metadata: {
    format_type: 'number',
    unit: '',
    category: 'volume'
  },
  generated_at: '2024-01-02T10:00:00Z'
};

describe('useKpiHistory', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should start with correct initial state when enabled', () => {
      const { result } = renderHook(() => 
        useKpiHistory({ 
          kpiId: 'test_kpi',
          enabled: true 
        })
      );

      expect(result.current.historyData).toBeNull();
      expect(result.current.isLoading).toBe(true);
      expect(result.current.isRefetching).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.lastUpdated).toBeNull();
    });

    it('should not fetch when disabled', () => {
      const { result } = renderHook(() => 
        useKpiHistory({ 
          kpiId: 'test_kpi',
          enabled: false 
        })
      );

      expect(result.current.historyData).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should not fetch when kpiId is null', () => {
      const { result } = renderHook(() => 
        useKpiHistory({ 
          kpiId: null,
          enabled: true 
        })
      );

      expect(result.current.historyData).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe('Successful Data Fetching', () => {
    it('should fetch and return history data successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockHistoryData
      });

      const { result } = renderHook(() => 
        useKpiHistory({ 
          kpiId: 'test_kpi',
          timeframe: 'week',
          currency: 'all'
        })
      );

      expect(result.current.isLoading).toBe(true);

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.historyData).toEqual(mockHistoryData);
      expect(result.current.error).toBeNull();
      expect(result.current.lastUpdated).toBeInstanceOf(Date);
    });

    it('should call API with correct parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockHistoryData
      });

      renderHook(() => 
        useKpiHistory({ 
          kpiId: 'test_kpi',
          timeframe: 'month',
          currency: 'usd',
          clientId: 'client123',
          userId: 'user456',
          profileType: 'CEO'
        })
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/kpis/test_kpi/history'),
          expect.objectContaining({
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          })
        );
      });

      const callUrl = mockFetch.mock.calls[0][0];
      expect(callUrl).toContain('timeframe=month');
      expect(callUrl).toContain('currency=usd');
      expect(callUrl).toContain('client_id=client123');
      expect(callUrl).toContain('user_id=user456');
      expect(callUrl).toContain('profile_type=CEO');
    });
  });

  describe('Error Handling', () => {
    it('should handle HTTP errors correctly', async () => {
      const errorResponse = {
        detail: {
          message: 'KPI not found'
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: async () => errorResponse
      });

      const { result } = renderHook(() => 
        useKpiHistory({ kpiId: 'invalid_kpi' })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe('KPI not found');
      expect(result.current.historyData).toBeNull();
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => 
        useKpiHistory({ kpiId: 'test_kpi' })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe('Network error');
      expect(result.current.historyData).toBeNull();
    });

    it('should handle JSON parsing errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => { throw new Error('Invalid JSON'); }
      });

      const { result } = renderHook(() => 
        useKpiHistory({ kpiId: 'test_kpi' })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe('HTTP 500: Internal Server Error');
    });
  });

  describe('Refetch Functionality', () => {
    it('should refetch data when refetch is called', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockHistoryData
      });

      const { result } = renderHook(() => 
        useKpiHistory({ kpiId: 'test_kpi' })
      );

      // Wait for initial fetch
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Call refetch
      act(() => {
        result.current.refetch();
      });

      expect(result.current.isRefetching).toBe(true);

      await waitFor(() => {
        expect(result.current.isRefetching).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Parameter Changes', () => {
    it('should refetch when kpiId changes', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockHistoryData
      });

      const { result, rerender } = renderHook(
        ({ kpiId }) => useKpiHistory({ kpiId }),
        { initialProps: { kpiId: 'kpi1' } }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Change kpiId
      rerender({ kpiId: 'kpi2' });

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });
    });

    it('should refetch when timeframe changes', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockHistoryData
      });

      const { result, rerender } = renderHook(
        ({ timeframe }) => useKpiHistory({ kpiId: 'test_kpi', timeframe }),
        { initialProps: { timeframe: 'week' } }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Change timeframe
      rerender({ timeframe: 'month' });

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });
    });

    it('should clear data when kpiId becomes null', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockHistoryData
      });

      const { result, rerender } = renderHook(
        ({ kpiId }) => useKpiHistory({ kpiId }),
        { initialProps: { kpiId: 'test_kpi' } }
      );

      await waitFor(() => {
        expect(result.current.historyData).toEqual(mockHistoryData);
      });

      // Set kpiId to null
      rerender({ kpiId: null });

      expect(result.current.historyData).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.lastUpdated).toBeNull();
    });
  });
});
