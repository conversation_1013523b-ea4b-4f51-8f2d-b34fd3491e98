import { useState, useCallback } from 'react';

export type TimeframeOption = '1d' | 'week' | 'month' | 'quarter';
export type CurrencyOption = 'all' | 'usd' | 'eur' | 'gbp';

export interface DashboardFilters {
  timeframe: TimeframeOption;
  currency: CurrencyOption;
}

export const useDashboardFilters = () => {
  const [filters, setFilters] = useState<DashboardFilters>({
    timeframe: 'week',
    currency: 'all'
  });

  const updateTimeframe = useCallback((timeframe: TimeframeOption) => {
    setFilters(prev => ({ ...prev, timeframe }));
  }, []);

  const updateCurrency = useCallback((currency: CurrencyOption) => {
    setFilters(prev => ({ ...prev, currency }));
  }, []);

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  return {
    filters,
    updateTimeframe,
    updateCurrency,
    updateFilters
  };
}; 