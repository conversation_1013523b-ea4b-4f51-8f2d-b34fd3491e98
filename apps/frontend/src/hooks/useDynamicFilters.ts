/**
 * Dynamic Filters Hook
 * 
 * Manages dashboard filters dynamically based on API configuration.
 * No hardcoded filter options - everything comes from the backend.
 */

import { useState, useCallback, useEffect } from 'react';
import { DashboardFilters, FilterDefinition, FilterOption } from '@/types/dashboard';

interface UseDynamicFiltersProps {
  initialFilters?: DashboardFilters;
  filterDefinitions?: FilterDefinition[];
}

interface UseDynamicFiltersReturn {
  filters: DashboardFilters;
  filterDefinitions: FilterDefinition[];
  isLoading: boolean;
  error: string | null;
  updateFilter: (key: string, value: string | string[] | boolean) => void;
  updateFilters: (newFilters: Partial<DashboardFilters>) => void;
  resetFilters: () => void;
  getFilterValue: (key: string) => string | string[] | boolean | undefined;
  getFilterOptions: (key: string) => FilterOption[];
  isFilterActive: (key: string) => boolean;
}

export const useDynamicFilters = ({
  initialFilters = {},
  filterDefinitions = []
}: UseDynamicFiltersProps = {}): UseDynamicFiltersReturn => {
  
  const [filters, setFilters] = useState<DashboardFilters>(initialFilters);
  const [definitions, setDefinitions] = useState<FilterDefinition[]>(filterDefinitions);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize filters with default values from definitions
  useEffect(() => {
    if (definitions.length > 0) {
      const defaultFilters: DashboardFilters = {};
      
      definitions.forEach(def => {
        if (def.defaultValue !== undefined) {
          defaultFilters[def.key] = def.defaultValue;
        }
      });

      // Merge with initial filters, giving priority to initial values
      setFilters(prev => ({ ...defaultFilters, ...prev }));
    }
  }, [definitions]);

  // Update a single filter
  const updateFilter = useCallback((key: string, value: string | string[] | boolean) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Update multiple filters at once
  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  // Reset filters to default values
  const resetFilters = useCallback(() => {
    const defaultFilters: DashboardFilters = {};
    
    definitions.forEach(def => {
      if (def.defaultValue !== undefined) {
        defaultFilters[def.key] = def.defaultValue;
      }
    });

    setFilters(defaultFilters);
  }, [definitions]);

  // Get value for a specific filter
  const getFilterValue = useCallback((key: string) => {
    return filters[key];
  }, [filters]);

  // Get options for a specific filter
  const getFilterOptions = useCallback((key: string): FilterOption[] => {
    const definition = definitions.find(def => def.key === key);
    return definition?.options || [];
  }, [definitions]);

  // Check if a filter is active (has non-default value)
  const isFilterActive = useCallback((key: string): boolean => {
    const definition = definitions.find(def => def.key === key);
    if (!definition) return false;

    const currentValue = filters[key];
    const defaultValue = definition.defaultValue;

    // Handle different value types
    if (Array.isArray(currentValue) && Array.isArray(defaultValue)) {
      return JSON.stringify(currentValue.sort()) !== JSON.stringify(defaultValue.sort());
    }

    return currentValue !== defaultValue;
  }, [filters, definitions]);

  // Load filter definitions from API (if not provided)
  const loadFilterDefinitions = useCallback(async () => {
    if (definitions.length > 0) return;

    setIsLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/dashboard/filters');
      if (!response.ok) {
        throw new Error('Failed to load filter definitions');
      }

      const data = await response.json();
      setDefinitions(data.filters || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load filters');
    } finally {
      setIsLoading(false);
    }
  }, [definitions.length]);

  // Load definitions on mount if not provided
  useEffect(() => {
    loadFilterDefinitions();
  }, [loadFilterDefinitions]);

  return {
    filters,
    filterDefinitions: definitions,
    isLoading,
    error,
    updateFilter,
    updateFilters,
    resetFilters,
    getFilterValue,
    getFilterOptions,
    isFilterActive
  };
};

// Helper functions for common filter operations
export const filterHelpers = {
  /**
   * Create filter query string for API calls
   */
  toQueryString: (filters: DashboardFilters): string => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    return params.toString();
  },

  /**
   * Parse query string into filters
   */
  fromQueryString: (queryString: string): DashboardFilters => {
    const params = new URLSearchParams(queryString);
    const filters: DashboardFilters = {};

    for (const [key, value] of params.entries()) {
      if (filters[key]) {
        // Convert to array if multiple values
        if (Array.isArray(filters[key])) {
          (filters[key] as string[]).push(value);
        } else {
          filters[key] = [filters[key] as string, value];
        }
      } else {
        filters[key] = value;
      }
    }

    return filters;
  },

  /**
   * Validate filters against definitions
   */
  validate: (filters: DashboardFilters, definitions: FilterDefinition[]): string[] => {
    const errors: string[] = [];

    definitions.forEach(def => {
      const value = filters[def.key];

      // Check required filters
      if (def.required && (value === undefined || value === null || value === '')) {
        errors.push(`${def.name} is required`);
      }

      // Validate against options (for select/multiselect)
      if (value && (def.type === 'select' || def.type === 'multiselect')) {
        const validValues = def.options.map(opt => opt.value);
        const valuesToCheck = Array.isArray(value) ? value : [value];

        valuesToCheck.forEach(v => {
          if (!validValues.includes(v.toString())) {
            errors.push(`Invalid value "${v}" for ${def.name}`);
          }
        });
      }
    });

    return errors;
  }
};

export default useDynamicFilters;
