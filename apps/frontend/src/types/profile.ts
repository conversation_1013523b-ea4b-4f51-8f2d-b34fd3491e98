/**
 * Profile Types for DataHero4 Week 5 Frontend Components
 * =====================================================
 * 
 * TypeScript type definitions for profile-aware components:
 * - User profile types and configurations
 * - Profile detection and setup interfaces
 * - Personalization preferences and settings
 * - Profile-aware component props
 * 
 * Based on backend profile system and API endpoints.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-21
 */

// === CORE PROFILE TYPES ===

export type ProfileType = 
  | 'CEO'
  | 'CFO' 
  | 'Risk_Manager'
  | 'Trader'
  | 'Operations';

export interface UserProfile {
  id: string;
  userId: string;
  profileType: ProfileType;
  displayName: string;
  description: string;
  preferences: ProfilePreferences;
  selectedKpis: string[];
  cacheStrategy: CacheStrategy;
  createdAt: string;
  updatedAt: string;
}

export interface ProfilePreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'pt' | 'en';
  timezone: string;
  currency: 'BRL' | 'USD' | 'EUR' | 'all';
  refreshInterval: number; // seconds
  notifications: NotificationPreferences;
  dashboard: DashboardPreferences;
}

export interface NotificationPreferences {
  enabled: boolean;
  email: boolean;
  push: boolean;
  alertThresholds: boolean;
  kpiUpdates: boolean;
}

export interface DashboardPreferences {
  defaultTimeframe: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
  autoRefresh: boolean;
  showTrends: boolean;
  compactView: boolean;
  priorityKpisOnly: boolean;
}

export interface CacheStrategy {
  ttl: number; // seconds
  layer: 'snapshot' | 'cache' | 'direct';
  priority: 'accuracy' | 'speed' | 'efficiency' | 'real_time';
}

// === PROFILE DETECTION ===

export interface ProfileDetectionResult {
  detectedProfile: ProfileType | null;
  confidence: number;
  reason: string;
  analysis: ProfileAnalysis;
  recommendations: ProfileRecommendations;
}

export interface ProfileAnalysis {
  queryPatterns: string[];
  kpiUsage: Record<string, number>;
  timeframePreferences: Record<string, number>;
  behaviorScore: number;
  usageFrequency: 'low' | 'medium' | 'high';
}

export interface ProfileRecommendations {
  shouldUseProfile: boolean;
  suggestedKpis: string[];
  cacheStrategy: CacheStrategy;
  alternativeProfiles: ProfileType[];
}

// === PROFILE SETUP ===

export interface ProfileSetupStep {
  id: string;
  title: string;
  description: string;
  component: 'role-selection' | 'kpi-selection' | 'preferences' | 'confirmation';
  isRequired: boolean;
  isCompleted: boolean;
}

export interface ProfileSetupState {
  currentStep: number;
  totalSteps: number;
  steps: ProfileSetupStep[];
  selectedProfile: ProfileType | null;
  selectedKpis: string[];
  preferences: Partial<ProfilePreferences>;
  isValid: boolean;
  errors: Record<string, string>;
}

// === PROFILE CHARACTERISTICS ===

export interface ProfileCharacteristics {
  id: ProfileType;
  name: string;
  description: string;
  icon: string;
  color: string;
  keyFeatures: string[];
  recommendedKpis: string[];
  cacheStrategy: CacheStrategy;
  updateFrequency: string;
  targetUsers: string[];
  useCases: string[];
}

// === API INTERFACES ===

export interface ProfileDetectionRequest {
  userId: string;
  analysisDays?: number;
}

export interface ProfileConfigurationRequest {
  userId: string;
  profileType: ProfileType;
  preferences: ProfilePreferences;
}

export interface ProfileKpiRequest {
  userId: string;
  profileType: ProfileType;
  timeframe?: string;
  currency?: string;
}

export interface ProfileApiResponse<T = any> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

// === COMPONENT PROPS ===

export interface ProfileSetupProps {
  userId: string;
  onComplete: (profile: UserProfile) => void;
  onSkip?: () => void;
  initialStep?: number;
  className?: string;
}

export interface ProfileSelectorProps {
  profiles: ProfileCharacteristics[];
  selectedProfile: ProfileType | null;
  onSelect: (profile: ProfileType) => void;
  className?: string;
}

export interface KpiSelectorProps {
  availableKpis: KpiOption[];
  selectedKpis: string[];
  onToggle: (kpiId: string) => void;
  profileType: ProfileType;
  className?: string;
}

export interface KpiOption {
  id: string;
  name: string;
  description: string;
  category: string;
  isRecommended: boolean;
  isPriority: boolean;
  targetProfiles: ProfileType[];
}

export interface PreferencesFormProps {
  preferences: Partial<ProfilePreferences>;
  onChange: (preferences: Partial<ProfilePreferences>) => void;
  profileType: ProfileType;
  className?: string;
}

// === PERSONALIZATION HOOK ===

export interface UsePersonalizationReturn {
  // Profile state
  userProfile: UserProfile | null;
  isProfileLoaded: boolean;
  isProfileSetupRequired: boolean;
  
  // Profile actions
  detectProfile: (userId: string) => Promise<ProfileDetectionResult>;
  configureProfile: (config: ProfileConfigurationRequest) => Promise<void>;
  updatePreferences: (preferences: Partial<ProfilePreferences>) => Promise<void>;
  
  // Profile data
  profileCharacteristics: ProfileCharacteristics[];
  recommendedKpis: string[];
  cacheStrategy: CacheStrategy;
  
  // Loading states
  isDetecting: boolean;
  isConfiguring: boolean;
  isUpdating: boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

// === PROFILE-AWARE COMPONENTS ===

export interface ProfileAwareProps {
  userProfile?: UserProfile | null;
  profileType?: ProfileType;
  userId?: string;
}

export interface PersonalizedKpiGridProps extends ProfileAwareProps {
  kpis: any[];
  filters: any;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  className?: string;
}

// === CONSTANTS ===

export const PROFILE_CHARACTERISTICS: Record<ProfileType, ProfileCharacteristics> = {
  CEO: {
    id: 'CEO',
    name: 'Chief Executive Officer',
    description: 'Strategic overview with focus on high-level KPIs and business performance',
    icon: '👔',
    color: '#8B5CF6',
    keyFeatures: ['Strategic View', 'High-Level KPIs', 'Long-term Trends'],
    recommendedKpis: ['spread_income_detailed', 'margem_liquida_operacional'],
    cacheStrategy: { ttl: 3600, layer: 'snapshot', priority: 'accuracy' },
    updateFrequency: 'hourly',
    targetUsers: ['C-Level Executives', 'Board Members'],
    useCases: ['Strategic Planning', 'Board Reporting', 'Performance Review']
  },
  CFO: {
    id: 'CFO',
    name: 'Chief Financial Officer',
    description: 'Financial analysis with accuracy focus and detailed cost management',
    icon: '💰',
    color: '#10B981',
    keyFeatures: ['Financial Analysis', 'Cost Management', 'Accuracy Focus'],
    recommendedKpis: ['margem_liquida_operacional', 'custo_por_transacao'],
    cacheStrategy: { ttl: 1800, layer: 'snapshot', priority: 'accuracy' },
    updateFrequency: 'every_30_minutes',
    targetUsers: ['Financial Directors', 'Controllers', 'Finance Teams'],
    useCases: ['Financial Planning', 'Cost Analysis', 'Budget Management']
  },
  Risk_Manager: {
    id: 'Risk_Manager',
    name: 'Risk Manager',
    description: 'Real-time risk monitoring and compliance with immediate alerts',
    icon: '⚠️',
    color: '#EF4444',
    keyFeatures: ['Real-time Monitoring', 'Risk Alerts', 'Compliance'],
    recommendedKpis: ['tempo_processamento_medio'],
    cacheStrategy: { ttl: 300, layer: 'direct', priority: 'real_time' },
    updateFrequency: 'every_5_minutes',
    targetUsers: ['Risk Officers', 'Compliance Teams', 'Auditors'],
    useCases: ['Risk Assessment', 'Compliance Monitoring', 'Alert Management']
  },
  Trader: {
    id: 'Trader',
    name: 'Trader',
    description: 'Real-time trading data with minimal latency and fast updates',
    icon: '📈',
    color: '#F59E0B',
    keyFeatures: ['Real-time Data', 'Minimal Latency', 'Fast Updates'],
    recommendedKpis: ['spread_income_detailed', 'tempo_processamento_medio'],
    cacheStrategy: { ttl: 60, layer: 'cache', priority: 'speed' },
    updateFrequency: 'realtime',
    targetUsers: ['Traders', 'Market Makers', 'Trading Desks'],
    useCases: ['Trading Decisions', 'Market Analysis', 'Position Monitoring']
  },
  Operations: {
    id: 'Operations',
    name: 'Operations Manager',
    description: 'Operational efficiency and cost management with balanced performance',
    icon: '⚙️',
    color: '#6366F1',
    keyFeatures: ['Operational Efficiency', 'Cost Management', 'Process Optimization'],
    recommendedKpis: ['custo_por_transacao', 'tempo_processamento_medio'],
    cacheStrategy: { ttl: 900, layer: 'cache', priority: 'efficiency' },
    updateFrequency: 'every_15_minutes',
    targetUsers: ['Operations Managers', 'Process Owners', 'Service Teams'],
    useCases: ['Process Optimization', 'Cost Control', 'Service Management']
  }
};

export const DEFAULT_PREFERENCES: ProfilePreferences = {
  theme: 'system',
  language: 'pt',
  timezone: 'America/Sao_Paulo',
  currency: 'BRL',
  refreshInterval: 300, // 5 minutes
  notifications: {
    enabled: true,
    email: true,
    push: false,
    alertThresholds: true,
    kpiUpdates: false
  },
  dashboard: {
    defaultTimeframe: 'week',
    autoRefresh: true,
    showTrends: true,
    compactView: false,
    priorityKpisOnly: false
  }
};
