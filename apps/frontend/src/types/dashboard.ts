/**
 * Dashboard Types - UI and Component Types
 * 
 * This file contains UI-specific types for dashboard components.
 * Core KPI types are imported from @/types/kpi for consistency.
 */

// Import unified KPI types
import type { 
  KpiData, 
  KpiDefinition, 
  ChartDataPoint, 
  KpiAlert,
  DashboardFilters 
} from './kpi';

// ============================================================================
// FILTER TYPES - DYNAMIC
// ============================================================================

export interface FilterOption {
  value: string;
  label: string;
  description?: string;
  isDefault?: boolean;
}

export interface FilterDefinition {
  key: string;
  name: string;
  type: 'select' | 'multiselect' | 'daterange' | 'toggle';
  options: FilterOption[];
  defaultValue: string | string[];
  required: boolean;
}

// Note: DashboardFilters is now imported from @/types/kpi

// ============================================================================
// UI STATE TYPES
// ============================================================================

export interface LoadingState {
  isInitialLoading: boolean;
  isFilterChanging: boolean;
  isRefreshing: boolean;
  isLoading: boolean; // Computed: any of the above
}

export interface ErrorState {
  error: string | null;
  hasError: boolean; // Computed: error !== null
}

export interface DashboardState extends LoadingState, ErrorState {
  kpis: KpiData[];
  filters: DashboardFilters;
  snapshotMetadata: any;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface PeriodData {
  currentDate: string;
  isSimulated: boolean;
  periodDescription?: string | null;
}

export interface KpiCardProps {
  kpi: KpiData;
  filters: DashboardFilters;
  layout?: CardLayoutConfig;
  periodData?: PeriodData;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  onKpiClick?: (kpiId: string) => void;
}

export interface KpiGridProps {
  kpis: KpiData[];
  filters: DashboardFilters;
  layout?: GridLayoutConfig;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  onKpiClick?: (kpiId: string) => void;
  periodData?: PeriodData;
}

export interface DashboardControlsProps {
  filters: DashboardFilters;
  filterDefinitions: FilterDefinition[];
  onFilterChange: (key: string, value: string | string[] | boolean) => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onAddKpi?: () => void;
  isRefreshing?: boolean;
}

// ============================================================================
// ACTION TYPES
// ============================================================================

export interface KpiActions {
  togglePriority: (kpiId: string) => void;
  removeKpi: (kpiId: string) => void;
  refreshKpis: () => Promise<void>;
}

export interface FilterActions {
  updateTimeframe: (timeframe: TimeframeOption) => void;
  updateCurrency: (currency: CurrencyOption) => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UseKpiDataReturn extends LoadingState, ErrorState {
  kpis: KpiData[];
  snapshotMetadata: any;
  togglePriority: (kpiId: string) => void;
  refreshKpis: () => Promise<void>;
}

export interface UseDashboardFiltersReturn {
  filters: DashboardFilters;
  updateTimeframe: (timeframe: TimeframeOption) => void;
  updateCurrency: (currency: CurrencyOption) => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
}

// ============================================================================
// LAYOUT TYPES - DYNAMIC
// ============================================================================

export type GridLayoutType = 'bento' | 'uniform' | 'masonry' | 'auto';

export interface GridLayoutConfig {
  type: GridLayoutType;
  columns: number;
  gap: number;
  minCardHeight: number;
  maxCardHeight?: number;
  responsive: {
    mobile: { columns: number; gap: number };
    tablet: { columns: number; gap: number };
    desktop: { columns: number; gap: number };
  };
}

export interface CardLayoutConfig {
  size: 'small' | 'medium' | 'large' | 'auto';
  span: number;
  height: string;
  priority: number;
}

export interface DashboardLayoutConfig {
  grid: GridLayoutConfig;
  cards: Record<string, CardLayoutConfig>; // KPI ID -> layout config
  autoLayout: boolean; // Auto-generate layout based on KPI priorities
}

// ============================================================================
// FORMATTING TYPES
// ============================================================================

export interface FormatConfig {
  locale: string;
  currency: string;
  minimumFractionDigits: number;
  maximumFractionDigits: number;
}

export interface FormattingOptions {
  format: KpiData['format'];
  currency: CurrencyOption;
  value: number;
}

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface KpiSelectEvent {
  kpiId: string;
  element: HTMLElement;
  cardRect: DOMRect;
}

export interface KpiEventHandlers {
  onKpiSelect: (event: KpiSelectEvent) => void;
  onKpiHover: (kpiId: string, isHovered: boolean) => void;
  onKpiAction: (kpiId: string, action: string) => void;
}

// ============================================================================
// API TYPES - DYNAMIC
// ============================================================================

export interface DashboardConfig {
  filters: FilterDefinition[];
  layout: DashboardLayoutConfig;
  kpiDefinitions: KpiDefinition[];
  defaultFilters: DashboardFilters;
}

export interface DashboardSnapshot {
  data: any;
  metadata: {
    date: string;
    period: {
      current_date: string;
      is_simulated: boolean;
      period_description?: string;
    };
    generated_at: string;
    filters_applied: DashboardFilters;
    total_kpis: number;
    cache_info: {
      hit_rate: number;
      avg_query_time_ms: number;
    };
  };
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
  timestamp: string;
  metadata?: {
    total_count?: number;
    page?: number;
    per_page?: number;
    execution_time_ms?: number;
  };
}

export interface KpiListResponse extends ApiResponse<KpiData[]> {
  filters_applied: DashboardFilters;
  available_filters: FilterDefinition[];
  layout_config: DashboardLayoutConfig;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Re-export commonly used types for convenience
export type { KpiData as Kpi };
export type { DashboardFilters as Filters };
export type { LoadingState as Loading };
export type { ErrorState as Error };
