/**
 * Alert Configuration Types for DataHero4
 * 
 * Defines TypeScript interfaces for the alert configuration system
 * integrated with the KPI Drawer functionality.
 */

export interface AlertConfig {
  kpiId: string;
  userId: string;
  enabled: boolean;
  warningThreshold?: number;
  criticalThreshold?: number;
  notificationChannels: NotificationChannel[];
  createdAt: string;
  updatedAt: string;
}

export interface ActiveAlert {
  id: string;
  kpiId: string;
  kpiName: string;
  type: 'warning' | 'critical';
  message: string;
  currentValue: number;
  threshold: number;
  triggeredAt: string;
  isActive: boolean;
}

export type NotificationChannel = 'email' | 'dashboard' | 'slack';

export interface AlertConfigForm {
  enabled: boolean;
  warningThreshold: string;
  criticalThreshold: string;
  notificationChannels: NotificationChannel[];
}

export interface AlertConfigModalProps {
  kpiId: string;
  kpiName: string;
  isOpen: boolean;
  onClose: () => void;
}

export interface ActiveAlertBannerProps {
  alert: ActiveAlert;
  onDismiss?: () => void;
}

export interface UseAlertConfigReturn {
  config: AlertConfig | null;
  activeAlert: ActiveAlert | null;
  isLoading: boolean;
  error: string | null;
  saveConfig: (formData: AlertConfigForm) => Promise<void>;
  loadConfig: () => Promise<void>;
  loadActiveAlert: () => Promise<void>;
  dismissAlert: () => void;
  configToFormData: (config: AlertConfig | null) => AlertConfigForm;
  validateFormData: (formData: AlertConfigForm) => string[];
}

// API Response Types
export interface AlertConfigResponse {
  success: boolean;
  config: AlertConfig;
  message?: string;
}

export interface ActiveAlertResponse {
  success: boolean;
  alert: ActiveAlert | null;
  message?: string;
}

// Form Validation Types
export interface AlertConfigValidation {
  warningThreshold?: string;
  criticalThreshold?: string;
  general?: string;
}
