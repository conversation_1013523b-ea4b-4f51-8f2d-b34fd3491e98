/**
 * Dashboard E2E Tests
 * 
 * End-to-end tests for the dashboard functionality
 */

import { test, expect } from '@playwright/test';

test.describe('Dashboard E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/');
  });

  test('should load dashboard page successfully', async ({ page }) => {
    // Check if page loads
    await expect(page).toHaveTitle(/DataHero/);
    
    // Check for main dashboard elements
    await expect(page.locator('h1')).toContainText('Métricas Críticas');
    await expect(page.locator('text=6 indicadores essenciais')).toBeVisible();
  });

  test('should display dashboard controls', async ({ page }) => {
    // Check for filter controls
    await expect(page.locator('text=Filtros:')).toBeVisible();
    
    // Check for timeframe selector
    await expect(page.locator('button:has-text("7 dias")')).toBeVisible();
    
    // Check for currency selector  
    await expect(page.locator('button:has-text("Todas")')).toBeVisible();
    
    // Check for action buttons
    await expect(page.locator('button:has-text("Atualizar")')).toBeVisible();
    await expect(page.locator('button:has-text("Adicionar KPI")')).toBeVisible();
  });

  test('should handle filter changes', async ({ page }) => {
    // Click on timeframe filter
    await page.locator('button:has-text("7 dias")').click();
    
    // Should show dropdown options
    await expect(page.locator('text=1 dia')).toBeVisible();
    await expect(page.locator('text=30 dias')).toBeVisible();
    
    // Select different timeframe
    await page.locator('text=30 dias').click();
    
    // Should update the button text
    await expect(page.locator('button:has-text("30 dias")')).toBeVisible();
  });

  test('should open add KPI modal', async ({ page }) => {
    // Click add KPI button
    await page.locator('button:has-text("Adicionar KPI")').click();
    
    // Should open modal
    await expect(page.locator('text=Adicionar KPIs ao Dashboard')).toBeVisible();
    
    // Should have close button
    await expect(page.locator('button[aria-label="Close"]')).toBeVisible();
    
    // Close modal
    await page.locator('button[aria-label="Close"]').click();
    
    // Modal should be closed
    await expect(page.locator('text=Adicionar KPIs ao Dashboard')).not.toBeVisible();
  });

  test('should display KPI cards when data loads', async ({ page }) => {
    // Wait for potential loading to complete
    await page.waitForTimeout(2000);
    
    // Check if KPI grid is present
    await expect(page.locator('.grid.grid-cols-12')).toBeVisible();
    
    // Check for KPI card structure (even if empty)
    const kpiCards = page.locator('[data-testid*="kpi-card"]');
    
    // If KPIs are loaded, they should be visible
    // If not loaded, we should see loading or empty state
    const hasKpis = await kpiCards.count() > 0;
    const hasLoading = await page.locator('text=Carregando').isVisible();
    const hasEmptyState = await page.locator('text=Nenhum KPI').isVisible();
    
    // At least one of these should be true
    expect(hasKpis || hasLoading || hasEmptyState).toBeTruthy();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if page still loads correctly
    await expect(page.locator('h1')).toContainText('Métricas Críticas');
    
    // Check if controls are still accessible
    await expect(page.locator('button:has-text("7 dias")')).toBeVisible();
    await expect(page.locator('button:has-text("Adicionar KPI")')).toBeVisible();
  });

  test('should handle refresh action', async ({ page }) => {
    // Click refresh button
    await page.locator('button:has-text("Atualizar")').click();
    
    // Should show some indication of refresh (loading state, etc.)
    // This is a basic test - in real scenario we'd check for loading indicators
    await page.waitForTimeout(500);
    
    // Page should still be functional after refresh
    await expect(page.locator('h1')).toContainText('Métricas Críticas');
  });

  test('should maintain state after navigation', async ({ page }) => {
    // Change a filter
    await page.locator('button:has-text("7 dias")').click();
    await page.locator('text=30 dias').click();
    
    // Verify filter changed
    await expect(page.locator('button:has-text("30 dias")')).toBeVisible();
    
    // Navigate away and back (simulate page refresh)
    await page.reload();
    
    // Check if page loads correctly after reload
    await expect(page.locator('h1')).toContainText('Métricas Críticas');
  });
});
