/**
 * Profile Personalization E2E Tests
 *
 * End-to-end tests for profile-based KPI personalization
 */

import { test, expect } from '@playwright/test';

test.describe('Profile Personalization E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to landing page first
    await page.goto('/');

    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Check if we're on landing page and need to login
    const loginButton = page.locator('text=Entrar no Portal');
    if (await loginButton.isVisible()) {
      // Click login button
      await loginButton.click();
      await page.waitForTimeout(2000);

      // Check if there's a password field
      const passwordField = page.locator('input[type="password"], input[placeholder*="senha"], input[placeholder*="password"]');
      if (await passwordField.isVisible()) {
        // Enter the password
        await passwordField.fill('datahero2025');

        // Look for submit button
        const submitButton = page.locator('button[type="submit"], button:has-text("Entrar"), button:has-text("Login")');
        if (await submitButton.isVisible()) {
          await submitButton.click();
          await page.waitForTimeout(3000);
        }
      }
    }

    // Try to navigate directly to dashboard if not already there
    if (!page.url().includes('/dashboard')) {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }
  });

  test('should load the dashboard page', async ({ page }) => {
    // Check if page loads without errors
    await expect(page).toHaveTitle(/DataHero|Dashboard/);

    // Check if main content is visible
    const mainContent = page.locator('body');
    await expect(mainContent).toBeVisible();

    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-dashboard.png' });
  });

  test('should find profile selector after login', async ({ page }) => {
    // Take a screenshot to see current state
    await page.screenshot({ path: 'debug-after-login.png' });

    // Look for any select element on the page
    const anySelect = page.locator('select').first();
    const isSelectVisible = await anySelect.isVisible().catch(() => false);

    if (isSelectVisible) {
      console.log('✅ Select element found!');
      await expect(anySelect).toBeVisible();
    } else {
      console.log('❌ No select element found, checking page content...');
      const bodyText = await page.locator('body').textContent();
      console.log('Current page content:', bodyText?.substring(0, 300));

      // Check if we're actually on dashboard
      const isDashboard = bodyText?.includes('Dashboard') || bodyText?.includes('KPI') || page.url().includes('/dashboard');
      console.log('Is on dashboard?', isDashboard);
    }
  });

  test('should check page content', async ({ page }) => {
    // Wait longer for page to fully load
    await page.waitForTimeout(5000);

    // Check if there's any content on the page
    const bodyText = await page.locator('body').textContent();
    console.log('Page content preview:', bodyText?.substring(0, 200));

    // Look for common dashboard elements
    const commonElements = [
      'h1', 'h2', 'h3',
      'button',
      'select',
      'input',
      '.dashboard',
      '.header',
      '.content'
    ];

    for (const selector of commonElements) {
      const element = page.locator(selector).first();
      const isVisible = await element.isVisible().catch(() => false);
      console.log(`Element ${selector}: ${isVisible ? 'found' : 'not found'}`);
    }
  });
});
