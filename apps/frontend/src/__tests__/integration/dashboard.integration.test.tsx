/**
 * Dashboard Integration Tests
 * 
 * Tests the integration between hooks, components, and API calls
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor, act } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Dashboard from '@/pages/Dashboard';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>
}));

// Mock API calls
global.fetch = vi.fn();

// Minimal mock response for testing - no hardcoded business data
const mockKpiResponse = {
  success: true,
  data: [],
  totalCount: 0,
  lastUpdated: new Date().toISOString()
};

const mockFiltersResponse = {
  success: true,
  data: {
    timeframe: ['1d', 'week', 'month', 'quarter'],
    currency: ['all', 'usd', 'eur', 'gbp'],
    sector: ['cambio', 'crypto'],
    client_id: ['L2M', 'CLIENT2']
  }
};

const DashboardWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Dashboard Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default fetch responses
    (global.fetch as any).mockImplementation((url: string) => {
      if (url.includes('/api/dashboard/filters')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFiltersResponse)
        });
      }
      if (url.includes('/api/dashboard/kpis')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockKpiResponse)
        });
      }
      return Promise.reject(new Error(`Unhandled URL: ${url}`));
    });
  });

  it('should render dashboard with loading state initially', async () => {
    render(
      <DashboardWrapper>
        <Dashboard />
      </DashboardWrapper>
    );

    // Should show loading initially
    expect(screen.getByText('Carregando métricas...')).toBeInTheDocument();
  });

  it('should load and display KPIs after initial load', async () => {
    render(
      <DashboardWrapper>
        <Dashboard />
      </DashboardWrapper>
    );

    // Wait for KPIs to load
    await waitFor(() => {
      expect(screen.queryByText('Carregando métricas...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Should display KPI names
    await waitFor(() => {
      expect(screen.getByText('Volume Total')).toBeInTheDocument();
      expect(screen.getByText('Ticket Médio')).toBeInTheDocument();
    });
  });

  it('should make correct API calls on mount', async () => {
    render(
      <DashboardWrapper>
        <Dashboard />
      </DashboardWrapper>
    );

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/dashboard/filters'),
        expect.any(Object)
      );
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/dashboard/kpis'),
        expect.any(Object)
      );
    });
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    (global.fetch as any).mockRejectedValue(new Error('API Error'));

    render(
      <DashboardWrapper>
        <Dashboard />
      </DashboardWrapper>
    );

    // Should handle error without crashing
    await waitFor(() => {
      expect(screen.queryByText('Carregando métricas...')).not.toBeInTheDocument();
    }, { timeout: 3000 });

    // Dashboard should still render (with empty state or error handling)
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('should render dashboard controls', async () => {
    render(
      <DashboardWrapper>
        <Dashboard />
      </DashboardWrapper>
    );

    // Should render dashboard controls
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    // Should have refresh and add KPI buttons
    expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /adicionar kpi/i })).toBeInTheDocument();
  });
});
