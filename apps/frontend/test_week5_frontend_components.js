#!/usr/bin/env node
/**
 * Test Script: Week 5 Frontend Components Implementation
 * =====================================================
 * 
 * Tests the Week 5 Frontend Components implementation including:
 * 1. ProfileSetup.tsx component structure and types
 * 2. usePersonalization.ts hook functionality
 * 3. KpiBentoGrid.tsx profile-aware extensions
 * 4. useKpis.tsx personalization features
 * 5. TypeScript type definitions
 * 
 * Features tested:
 * - Component structure and imports
 * - TypeScript type definitions
 * - Profile-aware functionality
 * - Backward compatibility
 * - Hook implementations
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-21
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    log(`✅ ${description}: File exists`, 'green');
    return true;
  } else {
    log(`❌ ${description}: File missing at ${filePath}`, 'red');
    return false;
  }
}

function testFileContent(filePath, patterns, description) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    log(`❌ ${description}: File not found`, 'red');
    return false;
  }
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    let passedPatterns = 0;
    
    patterns.forEach(({ pattern, name }) => {
      if (content.includes(pattern)) {
        log(`  ✅ ${name}: Found`, 'green');
        passedPatterns++;
      } else {
        log(`  ❌ ${name}: Missing pattern "${pattern}"`, 'red');
      }
    });
    
    const success = passedPatterns === patterns.length;
    log(`${success ? '✅' : '❌'} ${description}: ${passedPatterns}/${patterns.length} patterns found`, success ? 'green' : 'red');
    return success;
    
  } catch (error) {
    log(`❌ ${description}: Error reading file - ${error.message}`, 'red');
    return false;
  }
}

function testProfileTypes() {
  log('\n🧪 Testing Profile Types...', 'cyan');
  
  return testFileContent('src/types/profile.ts', [
    { pattern: 'export type ProfileType', name: 'ProfileType definition' },
    { pattern: 'export interface UserProfile', name: 'UserProfile interface' },
    { pattern: 'export interface ProfilePreferences', name: 'ProfilePreferences interface' },
    { pattern: 'export interface UsePersonalizationReturn', name: 'UsePersonalizationReturn interface' },
    { pattern: 'export const PROFILE_CHARACTERISTICS', name: 'Profile characteristics constant' },
    { pattern: 'CEO', name: 'CEO profile type' },
    { pattern: 'CFO', name: 'CFO profile type' },
    { pattern: 'Risk_Manager', name: 'Risk_Manager profile type' },
    { pattern: 'Trader', name: 'Trader profile type' },
    { pattern: 'Operations', name: 'Operations profile type' }
  ], 'Profile Types');
}

function testProfileSetupComponent() {
  log('\n🧪 Testing ProfileSetup Component...', 'cyan');
  
  return testFileContent('src/components/profile/ProfileSetup.tsx', [
    { pattern: 'const ProfileSetup: React.FC<ProfileSetupProps>', name: 'ProfileSetup component definition' },
    { pattern: 'import { motion, AnimatePresence }', name: 'Framer Motion imports' },
    { pattern: 'UserProfile', name: 'Profile type imports' },
    { pattern: 'const WelcomeStep: React.FC', name: 'WelcomeStep component' },
    { pattern: 'const RoleSelectionStep: React.FC', name: 'RoleSelectionStep component' },
    { pattern: 'const KpiSelectionStep: React.FC', name: 'KpiSelectionStep component' },
    { pattern: 'const PreferencesStep: React.FC', name: 'PreferencesStep component' },
    { pattern: 'handleProfileSelect', name: 'Profile selection handler' },
    { pattern: 'handleKpiToggle', name: 'KPI toggle handler' },
    { pattern: 'handleComplete', name: 'Setup completion handler' }
  ], 'ProfileSetup Component');
}

function testUsePersonalizationHook() {
  log('\n🧪 Testing usePersonalization Hook...', 'cyan');
  
  return testFileContent('src/hooks/usePersonalization.ts', [
    { pattern: 'export const usePersonalization', name: 'usePersonalization hook export' },
    { pattern: 'UsePersonalizationReturn', name: 'Return type definition' },
    { pattern: 'const detectProfile = useCallback', name: 'detectProfile function' },
    { pattern: 'const configureProfile = useCallback', name: 'configureProfile function' },
    { pattern: 'const updatePreferences = useCallback', name: 'updatePreferences function' },
    { pattern: 'localStorage', name: 'Local storage integration' },
    { pattern: 'PROFILE_CACHE_TTL', name: 'Cache TTL configuration' },
    { pattern: 'apiCall', name: 'API call helper' },
    { pattern: 'isProfileSetupRequired', name: 'Setup required logic' },
    { pattern: 'recommendedKpis', name: 'Recommended KPIs logic' }
  ], 'usePersonalization Hook');
}

function testKpiBentoGridExtensions() {
  log('\n🧪 Testing KpiBentoGrid Extensions...', 'cyan');
  
  return testFileContent('src/components/dashboard/KpiBentoGrid.tsx', [
    { pattern: 'import { UserProfile, ProfileType }', name: 'Profile type imports' },
    { pattern: 'userProfile?: UserProfile', name: 'userProfile prop' },
    { pattern: 'profileType?: ProfileType', name: 'profileType prop' },
    { pattern: 'enableProfileFiltering?: boolean', name: 'enableProfileFiltering prop' },
    { pattern: 'const filteredKpis = useMemo', name: 'Profile filtering logic' },
    { pattern: 'profileRecommendations', name: 'Profile recommendations' },
    { pattern: 'userSelectedKpis', name: 'User selected KPIs logic' },
    { pattern: 'filteredKpis].sort', name: 'Filtered KPIs sorting' },
    { pattern: 'backward compatibility', name: 'Backward compatibility comment' }
  ], 'KpiBentoGrid Extensions');
}

function testUseKpisExtensions() {
  log('\n🧪 Testing useKpis Hook Extensions...', 'cyan');
  
  return testFileContent('src/hooks/useKpis.ts', [
    { pattern: 'import { UserProfile, ProfileType }', name: 'Profile type imports' },
    { pattern: 'userProfile?: UserProfile', name: 'userProfile prop' },
    { pattern: 'profileType?: ProfileType', name: 'profileType prop' },
    { pattern: 'enablePersonalization?: boolean', name: 'enablePersonalization prop' },
    { pattern: 'userId?: string', name: 'userId prop' },
    { pattern: '/api/personalized-kpis', name: 'Personalized endpoint' },
    { pattern: 'profile_type', name: 'Profile type parameter' },
    { pattern: 'enablePersonalization && userId', name: 'Personalization condition' },
    { pattern: 'backward compatibility', name: 'Backward compatibility comment' }
  ], 'useKpis Hook Extensions');
}

function testComponentIntegration() {
  log('\n🧪 Testing Component Integration...', 'cyan');
  
  const tests = [
    testFileExists('src/types/profile.ts', 'Profile Types'),
    testFileExists('src/components/profile/ProfileSetup.tsx', 'ProfileSetup Component'),
    testFileExists('src/hooks/usePersonalization.ts', 'usePersonalization Hook')
  ];
  
  const passed = tests.filter(Boolean).length;
  const total = tests.length;
  
  log(`${passed === total ? '✅' : '❌'} Component Integration: ${passed}/${total} files exist`, passed === total ? 'green' : 'red');
  return passed === total;
}

function testTypeScriptCompatibility() {
  log('\n🧪 Testing TypeScript Compatibility...', 'cyan');
  
  // Check for TypeScript-specific patterns
  const tsPatterns = [
    { file: 'src/types/profile.ts', pattern: 'export type ProfileType =', name: 'Union type definition' },
    { file: 'src/types/profile.ts', pattern: 'export interface', name: 'Interface definitions' },
    { file: 'src/components/profile/ProfileSetup.tsx', pattern: ': React.FC<', name: 'React FC typing' },
    { file: 'src/hooks/usePersonalization.ts', pattern: ': UsePersonalizationReturn', name: 'Hook return typing' }
  ];
  
  let passed = 0;
  
  tsPatterns.forEach(({ file, pattern, name }) => {
    const fullPath = path.join(__dirname, file);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      if (content.includes(pattern)) {
        log(`  ✅ ${name}: Found in ${file}`, 'green');
        passed++;
      } else {
        log(`  ❌ ${name}: Missing in ${file}`, 'red');
      }
    } else {
      log(`  ❌ ${name}: File ${file} not found`, 'red');
    }
  });
  
  const success = passed === tsPatterns.length;
  log(`${success ? '✅' : '❌'} TypeScript Compatibility: ${passed}/${tsPatterns.length} patterns found`, success ? 'green' : 'red');
  return success;
}

function main() {
  log('🚀 Starting Week 5 Frontend Components Tests', 'bright');
  log('=' .repeat(60), 'blue');
  
  const startTime = Date.now();
  
  const tests = [
    { name: 'Profile Types', fn: testProfileTypes },
    { name: 'ProfileSetup Component', fn: testProfileSetupComponent },
    { name: 'usePersonalization Hook', fn: testUsePersonalizationHook },
    { name: 'KpiBentoGrid Extensions', fn: testKpiBentoGridExtensions },
    { name: 'useKpis Extensions', fn: testUseKpisExtensions },
    { name: 'Component Integration', fn: testComponentIntegration },
    { name: 'TypeScript Compatibility', fn: testTypeScriptCompatibility }
  ];
  
  const results = {};
  
  tests.forEach(({ name, fn }) => {
    try {
      results[name] = fn();
    } catch (error) {
      log(`❌ ${name} test ERROR: ${error.message}`, 'red');
      results[name] = false;
    }
  });
  
  // Summary
  const totalTime = Date.now() - startTime;
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  log('\n' + '='.repeat(60), 'blue');
  log('📊 WEEK 5 FRONTEND COMPONENTS TEST SUMMARY', 'bright');
  log('='.repeat(60), 'blue');
  
  log(`⏱️  Total time: ${totalTime}ms`, 'cyan');
  log(`✅ Passed tests: ${passedTests}/${totalTests}`, passedTests === totalTests ? 'green' : 'yellow');
  
  Object.entries(results).forEach(([name, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    const color = result ? 'green' : 'red';
    log(`   - ${name}: ${status}`, color);
  });
  
  if (passedTests === totalTests) {
    log('\n🎉 ALL WEEK 5 FRONTEND COMPONENTS TESTS PASSED!', 'green');
    log('🎨 Profile-aware frontend components ready for integration', 'green');
    log('📋 Ready for Week 6: Testing & Validation', 'green');
    return 0;
  } else {
    log(`\n❌ ${totalTests - passedTests} TESTS FAILED!`, 'red');
    log('🔧 Please fix issues before proceeding to Week 6', 'red');
    return 1;
  }
}

// Run tests
process.exit(main());
