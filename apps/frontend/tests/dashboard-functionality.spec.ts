/**
 * Dashboard Functionality Tests
 * ============================
 * 
 * Tests to ensure dashboard KPIs load correctly, filters work,
 * and cache is fast. These tests prevent regressions.
 */

import { test, expect } from '@playwright/test';

test.describe('Dashboard KPI Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard
    await page.goto('http://localhost:3000');
    
    // Enter portal with correct password
    await page.getByRole('textbox').fill('datahero2025');
    await page.getByRole('button', { name: 'Entrar no Portal' }).click();
    
    // Wait for portal to load and click Dashboard KPIs
    await page.waitForTimeout(2000);
    await page.getByRole('button', { name: 'Dashboard KPIs' }).click();
    
    // Wait for dashboard to load
    await page.waitForTimeout(1000);
  });

  test('should load KPI cards with real data', async ({ page }) => {
    // Wait for KPIs to load (should be fast with cache)
    await page.waitForTimeout(5000);
    
    // Check that KPI cards are visible
    await expect(page.getByRole('heading', { name: 'Volume Total Negociado' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Ticket Médio' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Spread Médio' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Taxa de Conversão' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Taxa de Retenção' })).toBeVisible();
    
    // Check that values are displayed (not loading states)
    const volumeValue = page.locator('text=R$ 5.542.429.196').first();
    await expect(volumeValue).toBeVisible();
    
    const ticketValue = page.locator('text=R$ 234.085').first();
    await expect(ticketValue).toBeVisible();
    
    const spreadValue = page.locator('text=0.99%').first();
    await expect(spreadValue).toBeVisible();
    
    const conversionValue = page.locator('text=0.25%').first();
    await expect(conversionValue).toBeVisible();
    
    const retentionValue = page.locator('text=43.40%').first();
    await expect(retentionValue).toBeVisible();
  });

  test('should have working timeframe filters', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(5000);
    
    // Check initial filter is "7 dias"
    const timeframeFilter = page.getByRole('combobox').first();
    await expect(timeframeFilter).toContainText('7 dias');
    
    // Click to open dropdown
    await timeframeFilter.click();
    
    // Select "30 dias"
    await page.getByRole('option', { name: '30 dias' }).click();
    
    // Check that filter changed
    await expect(timeframeFilter).toContainText('30 dias');
    
    // Wait for API call to complete
    await page.waitForTimeout(2000);
    
    // KPIs should still be visible after filter change
    await expect(page.getByRole('heading', { name: 'Volume Total Negociado' })).toBeVisible();
    await expect(page.locator('text=R$ 5.542.429.196').first()).toBeVisible();
  });

  test('should have working currency filters', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(5000);
    
    // Check initial currency filter is "Todas"
    const currencyFilter = page.getByRole('combobox').nth(1);
    await expect(currencyFilter).toContainText('Todas');
    
    // Click to open dropdown
    await currencyFilter.click();
    
    // Check that options are available
    await expect(page.getByRole('option', { name: 'USD' })).toBeVisible();
    await expect(page.getByRole('option', { name: 'EUR' })).toBeVisible();
    
    // Select USD
    await page.getByRole('option', { name: 'USD' }).click();
    
    // Check that filter changed
    await expect(currencyFilter).toContainText('USD');
    
    // Wait for API call to complete
    await page.waitForTimeout(2000);
    
    // KPIs should still be visible after filter change
    await expect(page.getByRole('heading', { name: 'Volume Total Negociado' })).toBeVisible();
  });

  test('should have fast cache performance', async ({ page }) => {
    const startTime = Date.now();
    
    // Wait for KPIs to load
    await page.waitForSelector('text=Volume Total Negociado', { timeout: 10000 });
    await page.waitForSelector('text=R$ 5.542.429.196', { timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    
    // Should load in less than 8 seconds (cache should make it fast)
    expect(loadTime).toBeLessThan(8000);
    
    console.log(`KPIs loaded in ${loadTime}ms`);
  });

  test('should handle refresh button correctly', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(5000);
    
    // Click refresh button
    await page.getByRole('button', { name: 'Atualizar' }).click();
    
    // Wait for refresh to complete
    await page.waitForTimeout(3000);
    
    // KPIs should still be visible after refresh
    await expect(page.getByRole('heading', { name: 'Volume Total Negociado' })).toBeVisible();
    await expect(page.locator('text=R$ 5.542.429.196').first()).toBeVisible();
  });

  test('should not show TanStack Query Devtools icon', async ({ page }) => {
    // Wait for page to fully load
    await page.waitForTimeout(5000);
    
    // Check that there's no TanStack Query Devtools icon
    const devtoolsIcon = page.locator('[data-testid="react-query-devtools"]');
    await expect(devtoolsIcon).not.toBeVisible();
    
    // Also check for any floating dev icons
    const floatingIcons = page.locator('button[style*="position: fixed"]');
    const count = await floatingIcons.count();
    
    // Should not have suspicious floating buttons (devtools usually float)
    expect(count).toBeLessThan(2); // Allow for legitimate floating buttons
  });

  test('should show correct KPI count', async ({ page }) => {
    // Wait for KPIs to load
    await page.waitForTimeout(5000);
    
    // Count visible KPI cards
    const kpiCards = page.locator('[role="heading"][level="3"]');
    const count = await kpiCards.count();
    
    // Should show exactly 5 KPIs (as seen in console logs)
    expect(count).toBe(5);
  });

  test('should have proper error handling', async ({ page }) => {
    // This test checks that the page doesn't crash if API fails
    
    // Intercept API calls and make them fail
    await page.route('**/api/dashboard/snapshot**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' })
      });
    });
    
    // Reload page to trigger API call
    await page.reload();
    
    // Enter portal again
    await page.getByRole('textbox').fill('datahero2025');
    await page.getByRole('button', { name: 'Entrar no Portal' }).click();
    await page.waitForTimeout(2000);
    await page.getByRole('button', { name: 'Dashboard KPIs' }).click();
    
    // Wait for error handling
    await page.waitForTimeout(5000);
    
    // Page should not crash - should show some error state or empty state
    const body = page.locator('body');
    await expect(body).toBeVisible();
    
    // Should not show the successful KPI values
    await expect(page.locator('text=R$ 5.542.429.196')).not.toBeVisible();
  });
});
