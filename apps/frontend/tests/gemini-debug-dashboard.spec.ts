
import { test, expect } from '@playwright/test';

test('Navega para o dashboard e verifica a visibilidade dos cards', async ({ page }) => {
  await page.goto('http://localhost:3000');

  // Aguarda um seletor genérico do corpo do dashboard
  await page.waitForSelector('body');

  // Tira uma captura de tela para análise visual
  await page.screenshot({ path: 'playwright-report/debug-dashboard-screenshot.png' });

  // Adiciona uma verificação para a presença de algum conteúdo esperado no dashboard
  // Esta é uma suposição, pode precisar de ajuste
  const dashboardContainer = await page.locator('main').count();
  expect(dashboardContainer).toBeGreaterThan(0);
});
