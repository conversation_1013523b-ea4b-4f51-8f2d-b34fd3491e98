/**
 * Drawer E2E Tests with <PERSON><PERSON>
 * ================================
 * 
 * End-to-end tests for the KPI drawer functionality.
 * Tests complete user journeys, visual state persistence,
 * and cross-browser compatibility.
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.VITE_APP_URL || 'http://localhost:3000';
const TEST_TIMEOUT = 30000;

// Helper functions
async function waitForDashboardLoad(page: Page) {
  // Wait for dashboard to load completely
  await page.waitForSelector('[data-testid="dashboard-container"]', { timeout: TEST_TIMEOUT });
  await page.waitForLoadState('networkidle');
  
  // Wait for KPI cards to appear
  await page.waitForSelector('[data-testid="kpi-card"]', { timeout: TEST_TIMEOUT });
}

async function selectKpiCard(page: Page, kpiId: string) {
  // Find and click the KPI card
  const kpiCard = page.locator(`[data-testid="kpi-card"][data-kpi-id="${kpiId}"]`).first();
  await expect(kpiCard).toBeVisible();
  await kpiCard.click();
  
  // Wait for drawer to open
  await page.waitForSelector('[data-testid="kpi-drawer"]', { timeout: 5000 });
}

async function waitForDrawerContent(page: Page) {
  // Wait for drawer content to load
  await page.waitForSelector('[data-testid="drawer-content"]', { timeout: 5000 });
  await page.waitForSelector('[data-testid="history-table"]', { timeout: 5000 });
}

test.describe('KPI Drawer E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard
    await page.goto(BASE_URL);
    await waitForDashboardLoad(page);
  });

  test.describe('Complete User Journey', () => {
    test('should complete full drawer interaction flow', async ({ page }) => {
      // Step 1: Select a KPI card
      await selectKpiCard(page, 'total_volume');
      
      // Step 2: Verify drawer opens with correct content
      const drawer = page.locator('[data-testid="kpi-drawer"]');
      await expect(drawer).toBeVisible();
      
      // Step 3: Verify selected card has visual feedback
      const selectedCard = page.locator('[data-testid="kpi-card"][data-kpi-id="total_volume"]').first();
      await expect(selectedCard).toHaveClass(/selected|active/);
      
      // Step 4: Wait for drawer content to load
      await waitForDrawerContent(page);
      
      // Step 5: Verify history table appears
      const historyTable = page.locator('[data-testid="history-table"]');
      await expect(historyTable).toBeVisible();
      
      // Step 6: Verify data is loaded (not empty state)
      const tableRows = page.locator('[data-testid="history-table"] tbody tr');
      await expect(tableRows).toHaveCount.greaterThan(0);
      
      // Step 7: Close drawer
      const closeButton = page.locator('[data-testid="drawer-close"]');
      await closeButton.click();
      
      // Step 8: Verify drawer closes and card loses selection
      await expect(drawer).not.toBeVisible();
      await expect(selectedCard).not.toHaveClass(/selected|active/);
    });

    test('should handle multiple card selections correctly', async ({ page }) => {
      // Select first card
      await selectKpiCard(page, 'total_volume');
      
      let selectedCard = page.locator('[data-testid="kpi-card"][data-kpi-id="total_volume"]').first();
      await expect(selectedCard).toHaveClass(/selected|active/);
      
      // Select second card (should replace first)
      await selectKpiCard(page, 'average_spread');
      
      // First card should no longer be selected
      await expect(selectedCard).not.toHaveClass(/selected|active/);
      
      // Second card should be selected
      selectedCard = page.locator('[data-testid="kpi-card"][data-kpi-id="average_spread"]').first();
      await expect(selectedCard).toHaveClass(/selected|active/);
      
      // Drawer should show content for second KPI
      await waitForDrawerContent(page);
      const drawerTitle = page.locator('[data-testid="drawer-title"]');
      await expect(drawerTitle).toContainText('average_spread');
    });
  });

  test.describe('Timeframe Changes and Data Updates', () => {
    test('should update data when timeframe changes', async ({ page }) => {
      // Open drawer
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      // Get initial data
      const initialRows = await page.locator('[data-testid="history-table"] tbody tr').count();
      
      // Change timeframe
      const timeframeSelector = page.locator('[data-testid="timeframe-selector"]');
      await timeframeSelector.click();
      
      const monthOption = page.locator('[data-testid="timeframe-option-month"]');
      await monthOption.click();
      
      // Wait for loading state
      const loadingIndicator = page.locator('[data-testid="loading-indicator"]');
      await expect(loadingIndicator).toBeVisible();
      
      // Wait for data to update
      await expect(loadingIndicator).not.toBeVisible({ timeout: 10000 });
      
      // Verify data has updated
      const updatedRows = await page.locator('[data-testid="history-table"] tbody tr').count();
      // Data might be different, but table should still have content
      expect(updatedRows).toBeGreaterThan(0);
      
      // Verify timeframe selector shows new selection
      await expect(timeframeSelector).toContainText('30 dias');
    });

    test('should show loading states during updates', async ({ page }) => {
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      // Change timeframe and immediately check for loading state
      const timeframeSelector = page.locator('[data-testid="timeframe-selector"]');
      await timeframeSelector.click();
      
      const quarterOption = page.locator('[data-testid="timeframe-option-quarter"]');
      await quarterOption.click();
      
      // Should show loading indicator
      const loadingIndicator = page.locator('[data-testid="loading-indicator"]');
      await expect(loadingIndicator).toBeVisible();
      
      // Should show "Atualizando..." text
      const updatingText = page.locator('text=Atualizando');
      await expect(updatingText).toBeVisible();
      
      // Wait for loading to complete
      await expect(loadingIndicator).not.toBeVisible({ timeout: 10000 });
      await expect(updatingText).not.toBeVisible();
    });

    test('should handle rapid timeframe changes gracefully', async ({ page }) => {
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      const timeframeSelector = page.locator('[data-testid="timeframe-selector"]');
      
      // Rapidly change timeframes
      await timeframeSelector.click();
      await page.locator('[data-testid="timeframe-option-month"]').click();
      
      await timeframeSelector.click();
      await page.locator('[data-testid="timeframe-option-quarter"]').click();
      
      await timeframeSelector.click();
      await page.locator('[data-testid="timeframe-option-week"]').click();
      
      // Should eventually settle on the last selection
      await expect(timeframeSelector).toContainText('7 dias');
      
      // Data should load without errors
      const errorMessage = page.locator('[data-testid="error-message"]');
      await expect(errorMessage).not.toBeVisible();
      
      const historyTable = page.locator('[data-testid="history-table"]');
      await expect(historyTable).toBeVisible();
    });
  });

  test.describe('Visual State Persistence', () => {
    test('should maintain card selection visual state', async ({ page }) => {
      // Select card
      await selectKpiCard(page, 'total_volume');
      
      const selectedCard = page.locator('[data-testid="kpi-card"][data-kpi-id="total_volume"]').first();
      
      // Verify visual selection state
      await expect(selectedCard).toHaveClass(/selected|active/);
      
      // Check for visual indicators (border, shadow, etc.)
      const cardStyles = await selectedCard.evaluate(el => getComputedStyle(el));
      
      // Should have different styling when selected
      // (This would depend on the actual CSS implementation)
      expect(cardStyles.borderColor).not.toBe('rgb(0, 0, 0)'); // Not default black
      
      // Scroll page and verify selection persists
      await page.evaluate(() => window.scrollBy(0, 500));
      await expect(selectedCard).toHaveClass(/selected|active/);
      
      // Navigate away and back (if applicable)
      await page.goBack();
      await page.goForward();
      
      // Selection should persist (if state is maintained)
      await expect(selectedCard).toHaveClass(/selected|active/);
    });

    test('should show correct drawer content for selected card', async ({ page }) => {
      // Select specific KPI
      await selectKpiCard(page, 'average_spread');
      await waitForDrawerContent(page);
      
      // Verify drawer shows correct KPI information
      const drawerTitle = page.locator('[data-testid="drawer-title"]');
      await expect(drawerTitle).toContainText('Spread');
      
      // Verify history table has data relevant to the KPI
      const historyTable = page.locator('[data-testid="history-table"]');
      await expect(historyTable).toBeVisible();
      
      // Check that data format matches KPI type (e.g., percentage for spread)
      const firstValue = page.locator('[data-testid="history-table"] tbody tr:first-child td:nth-child(2)');
      const valueText = await firstValue.textContent();
      
      // Should contain appropriate formatting (%, $, etc.)
      expect(valueText).toMatch(/[\d,.]+(%)*/);
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept API calls and simulate network error
      await page.route('**/api/v1/kpis/*/history*', route => {
        route.abort('failed');
      });
      
      await selectKpiCard(page, 'total_volume');
      
      // Should show error message
      const errorMessage = page.locator('[data-testid="error-message"]');
      await expect(errorMessage).toBeVisible({ timeout: 10000 });
      await expect(errorMessage).toContainText(/Erro ao carregar/);
      
      // Should show retry button
      const retryButton = page.locator('[data-testid="retry-button"]');
      await expect(retryButton).toBeVisible();
      
      // Remove network error simulation
      await page.unroute('**/api/v1/kpis/*/history*');
      
      // Click retry
      await retryButton.click();
      
      // Should load successfully
      await waitForDrawerContent(page);
      const historyTable = page.locator('[data-testid="history-table"]');
      await expect(historyTable).toBeVisible();
    });

    test('should handle empty data gracefully', async ({ page }) => {
      // Intercept API calls and return empty data
      await page.route('**/api/v1/kpis/*/history*', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            kpi_id: 'test_kpi',
            kpi_name: 'Test KPI',
            timeframe: 'week',
            currency: 'all',
            total_records: 0,
            history_data: [],
            generated_at: new Date().toISOString()
          })
        });
      });
      
      await selectKpiCard(page, 'total_volume');
      
      // Should show empty state message
      const emptyMessage = page.locator('[data-testid="empty-state"]');
      await expect(emptyMessage).toBeVisible({ timeout: 10000 });
      await expect(emptyMessage).toContainText(/Nenhum dado histórico/);
    });
  });

  test.describe('Responsive Design', () => {
    test('should work correctly on mobile viewport', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await selectKpiCard(page, 'total_volume');
      
      // Drawer should adapt to mobile layout
      const drawer = page.locator('[data-testid="kpi-drawer"]');
      await expect(drawer).toBeVisible();
      
      // Should be full-width on mobile
      const drawerStyles = await drawer.evaluate(el => getComputedStyle(el));
      expect(parseInt(drawerStyles.width)).toBeGreaterThan(300);
      
      // Content should be scrollable
      const drawerContent = page.locator('[data-testid="drawer-content"]');
      await expect(drawerContent).toBeVisible();
    });

    test('should work correctly on tablet viewport', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      // Should maintain split-screen layout on tablet
      const drawer = page.locator('[data-testid="kpi-drawer"]');
      await expect(drawer).toBeVisible();
      
      // Both card and drawer should be visible
      const selectedCard = page.locator('[data-testid="kpi-card"][data-kpi-id="total_volume"]').first();
      await expect(selectedCard).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load drawer content within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('should handle multiple rapid interactions', async ({ page }) => {
      // Rapidly open and close drawer multiple times
      for (let i = 0; i < 3; i++) {
        await selectKpiCard(page, 'total_volume');
        
        const closeButton = page.locator('[data-testid="drawer-close"]');
        await closeButton.click();
        
        // Wait for drawer to close
        const drawer = page.locator('[data-testid="kpi-drawer"]');
        await expect(drawer).not.toBeVisible();
      }
      
      // Should still work correctly after rapid interactions
      await selectKpiCard(page, 'total_volume');
      await waitForDrawerContent(page);
      
      const historyTable = page.locator('[data-testid="history-table"]');
      await expect(historyTable).toBeVisible();
    });
  });
});
