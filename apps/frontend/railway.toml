# Railway configuration for DataHero4 Frontend
# Frontend service configuration for React/Vite + Caddy

[build]
builder = "dockerfile"
dockerfilePath = "Dockerfile"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 5

# Environment variables for frontend
[variables]
NODE_ENV = "production"
PORT = "3000"
PYTHONUNBUFFERED = "1"
# Backend URL for Caddy proxy - uses the existing Railway variable
BACKEND_URL = "https://backend-production-9857.up.railway.app"