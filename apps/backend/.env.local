# Railway Database Credentials for Local Testing
DATABASE_URL_LEARNING=postgresql://postgres.ojtguhogfphkwzvuynnm:<EMAIL>:5432/postgres
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/l2m_prod

# Database connection details
DB_LEARNING_HOST=aws-0-us-east-1.pooler.supabase.com
DB_LEARNING_NAME=postgres
DB_LEARNING_PASSWORD=bUdvah-gewho5-mawcan
DB_LEARNING_PORT=5432
DB_LEARNING_USER=postgres.ojtguhogfphkwzvuynnm

DB_CAMBIO_HOST=l2m-homol.c50woq6q6gnz.us-east-1.rds.amazonaws.com
DB_CAMBIO_NAME=l2m_prod
DB_CAMBIO_PASSWORD=DaB9cWeNfcNdxoTubNV96qeQ0sNGnhAZ4IwehAdb5ofEoCfMYM
DB_CAMBIO_USER=postgres

# Environment
RAILWAY_ENVIRONMENT=local