#!/bin/bash

# Week 1 Database Extensions Migration Script
# ==========================================
# 
# This script runs all Week 1 database extension migrations for the
# Phase 1 Hybrid Architecture implementation.
#
# Usage: ./run_migrations.sh
#
# Author: DataHero4 Team
# Date: 2025-01-21

set -e  # Exit on any error

echo "🚀 Starting Week 1 Database Extensions Migration"
echo "================================================"

# Set database environment variables for L2M database
export DB_LEARNING_HOST="l2m-homol.c50woq6q6gnz.us-east-1.rds.amazonaws.com"
export DB_LEARNING_PORT="5432"
export DB_LEARNING_USER="postgres"
export DB_LEARNING_PASSWORD="DaB9cWeNfcNdxoTubNV96qeQ0sNGnhAZ4IwehAdb5ofEoCfMYM"
export DB_LEARNING_NAME="l2m_prod"

echo "📡 Database Configuration:"
echo "   Host: $DB_LEARNING_HOST"
echo "   Port: $DB_LEARNING_PORT"
echo "   User: $DB_LEARNING_USER"
echo "   Database: $DB_LEARNING_NAME"
echo ""

# Check if poetry is available
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry not found. Please install Poetry first."
    exit 1
fi

# Check if we're in the correct directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Please run this script from the backend directory."
    exit 1
fi

echo "🔍 Running individual migrations..."
echo ""

# Migration 1: KPI Snapshots V2
echo "📋 Migration 1/4: KPI Snapshots V2"
echo "-----------------------------------"
poetry run python src/migrations/001_add_kpi_snapshots_v2.py
if [ $? -ne 0 ]; then
    echo "❌ Migration 1 failed"
    exit 1
fi
echo "✅ Migration 1 completed"
echo ""

# Migration 2: User Profiles
echo "📋 Migration 2/4: User Profiles"
echo "--------------------------------"
poetry run python src/migrations/002_add_user_profiles.py
if [ $? -ne 0 ]; then
    echo "❌ Migration 2 failed"
    exit 1
fi
echo "✅ Migration 2 completed"
echo ""

# Migration 3: Operational Costs
echo "📋 Migration 3/4: Operational Costs"
echo "------------------------------------"
poetry run python src/migrations/003_add_operational_costs.py
if [ $? -ne 0 ]; then
    echo "❌ Migration 3 failed"
    exit 1
fi
echo "✅ Migration 3 completed"
echo ""

# Migration 4: Transactions Processed At
echo "📋 Migration 4/4: Transactions Processed At"
echo "--------------------------------------------"
poetry run python src/migrations/004_add_processed_at_transactions.py
if [ $? -ne 0 ]; then
    echo "❌ Migration 4 failed"
    exit 1
fi
echo "✅ Migration 4 completed"
echo ""

echo "🎉 ALL WEEK 1 MIGRATIONS COMPLETED SUCCESSFULLY!"
echo "================================================"
echo ""
echo "📊 Summary:"
echo "   ✅ kpi_snapshots_v2 table created with partitioning"
echo "   ✅ user_profiles table created with 5 default profiles"
echo "   ✅ operational_costs table created with sample data"
echo "   ✅ transactions table extended with processed_at field"
echo ""
echo "🏗️ Hybrid architecture database extensions are ready!"
echo ""
echo "📋 Next steps:"
echo "   1. Verify all tables and indexes are created correctly"
echo "   2. Test KPI calculations with new table structures"
echo "   3. Proceed to Week 2 backend service implementations"
echo "   4. Implement SmartQueryRouter and PersonalizedCacheSystem"
echo ""
echo "🔧 To verify the migration results, you can run:"
echo "   poetry run python scripts/check_database_schema.py"
echo ""
