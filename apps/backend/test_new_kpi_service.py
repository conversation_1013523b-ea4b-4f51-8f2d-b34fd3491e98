#!/usr/bin/env python3
"""
Test script for new KPI service with SQLModel repository.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_kpi_service():
    """Test the new KPI service."""
    try:
        print("🧪 Testing new KPI service...")
        
        # Import the new service
        from src.services.kpi_service import get_kpi_service
        
        # Get service instance
        kpi_service = get_kpi_service()
        print("✅ KPI service initialized successfully")
        
        # Test getting available KPIs
        available_kpis = kpi_service.get_available_kpis(priority_only=True)
        print(f"✅ Available priority KPIs: {len(available_kpis)}")
        
        for kpi in available_kpis:
            print(f"  - {kpi.get('id')}: {kpi.get('name')}")
        
        # Test KPI metadata
        total_volume_meta = kpi_service.get_kpi_metadata("total_volume")
        if total_volume_meta:
            print(f"✅ KPI metadata for total_volume: {total_volume_meta.get('name')}")
        
        print("\n🎉 New KPI service is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing KPI service: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kpi_definitions():
    """Test KPI definitions configuration."""
    try:
        print("\n🧪 Testing KPI definitions...")
        
        from src.config.kpi_definitions import (
            KPI_DEFINITIONS,
            CRITICAL_KPIS,
            get_kpi_definition,
            get_timeframe_config,
            get_currency_config
        )
        
        print(f"✅ Total KPI definitions: {len(KPI_DEFINITIONS)}")
        print(f"✅ Critical KPIs: {len(CRITICAL_KPIS)}")
        
        # Test individual KPI definition
        total_volume = get_kpi_definition("total_volume")
        if total_volume:
            print(f"✅ Total volume KPI: {total_volume.get('name')}")
        
        # Test timeframe config
        week_config = get_timeframe_config("week")
        print(f"✅ Week timeframe: {week_config.get('name')}")
        
        # Test currency config
        usd_config = get_currency_config("usd")
        print(f"✅ USD currency: {usd_config.get('name')}")
        
        print("✅ KPI definitions working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing KPI definitions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repository_models():
    """Test repository and models."""
    try:
        print("\n🧪 Testing repository models...")
        
        from src.models.kpi_models import (
            Transaction,
            Client,
            KpiCalculationResult
        )
        
        # Test model creation
        result = KpiCalculationResult(
            kpi_id="test_kpi",
            title="Test KPI",
            description="Test description",
            current_value=100.0
        )
        
        print(f"✅ KPI result model: {result.title}")
        print("✅ Repository models working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing repository models: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing new KPI architecture...")
    
    success = True
    success &= test_kpi_definitions()
    success &= test_repository_models()
    success &= test_kpi_service()
    
    if success:
        print("\n🎉 All tests passed! New KPI architecture is ready.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)
