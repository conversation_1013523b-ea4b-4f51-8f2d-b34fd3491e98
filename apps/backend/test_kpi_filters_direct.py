"""
Teste direto de KPI filters
==========================

Verifica se os valores dos KPIs mudam com diferentes filtros.
"""

import httpx
import asyncio
import json


async def test_kpi_filters():
    """Testa se os valores dos KPIs mudam com diferentes filtros."""
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    client = httpx.AsyncClient(timeout=30.0)
    
    try:
        print("\n🔍 TESTANDO VALORES DOS KPIs COM DIFERENTES FILTROS\n")
        print("=" * 60)
        
        # Configurações de teste
        timeframes = ['1d', 'week', 'month']
        currencies = ['all', 'usd', 'eur']
        
        # Armazenar valores para comparação
        values_by_filter = {}
        
        for timeframe in timeframes:
            for currency in currencies:
                print(f"\n📊 Testando: timeframe={timeframe}, currency={currency}")
                print("-" * 40)
                
                # Fazer requisição
                response = await client.get(
                    base_url,
                    params={
                        "timeframe": timeframe,
                        "currency": currency,
                        "priority_only": True
                    }
                )
                
                if response.status_code != 200:
                    print(f"❌ Erro HTTP {response.status_code}")
                    continue
                
                data = response.json()
                kpis = data.get('kpis', [])
                
                # Armazenar valores
                filter_key = f"{timeframe}_{currency}"
                values_by_filter[filter_key] = {}
                
                print(f"KPIs retornados: {len(kpis)}")
                
                for kpi in kpis:
                    kpi_id = kpi.get('id')
                    current_value = kpi.get('currentValue')
                    change_percent = kpi.get('changePercent')
                    chart_data_len = len(kpi.get('chartData', []))
                    
                    values_by_filter[filter_key][kpi_id] = {
                        'currentValue': current_value,
                        'changePercent': change_percent,
                        'chartDataLen': chart_data_len
                    }
                    
                    print(f"\n  {kpi_id}:")
                    print(f"    - currentValue: {current_value}")
                    print(f"    - changePercent: {change_percent}")
                    print(f"    - chartData points: {chart_data_len}")
        
        # Análise de variação
        print("\n\n📊 ANÁLISE DE VARIAÇÃO DOS VALORES")
        print("=" * 60)
        
        # Verificar se valores mudam entre filtros
        kpi_ids = ['total_volume', 'average_ticket', 'average_spread']
        
        for kpi_id in kpi_ids:
            print(f"\n🔍 KPI: {kpi_id}")
            print("-" * 40)
            
            # Coletar todos os valores para este KPI
            all_values = []
            all_changes = []
            
            for filter_key, kpis_data in values_by_filter.items():
                if kpi_id in kpis_data:
                    value_data = kpis_data[kpi_id]
                    all_values.append(value_data['currentValue'])
                    all_changes.append(value_data['changePercent'])
                    
                    print(f"  {filter_key}: value={value_data['currentValue']}, change={value_data['changePercent']}")
            
            # Verificar se há variação
            unique_values = set(v for v in all_values if v is not None)
            unique_changes = set(c for c in all_changes if c is not None)
            
            if len(unique_values) <= 1:
                print(f"\n  ❌ PROBLEMA: currentValue não muda com filtros! Sempre: {unique_values}")
            else:
                print(f"\n  ✅ OK: currentValue varia com filtros: {unique_values}")
            
            if len(unique_changes) <= 1:
                print(f"  ❌ PROBLEMA: changePercent não muda com filtros! Sempre: {unique_changes}")
            else:
                print(f"  ✅ OK: changePercent varia com filtros: {unique_changes}")
        
        # Verificar dados específicos
        print("\n\n🔍 VERIFICAÇÃO DETALHADA")
        print("=" * 60)
        
        # Comparar week/all vs month/usd
        week_all = values_by_filter.get('week_all', {})
        month_usd = values_by_filter.get('month_usd', {})
        
        print("\nComparando week/all vs month/usd:")
        for kpi_id in kpi_ids:
            week_data = week_all.get(kpi_id, {})
            month_data = month_usd.get(kpi_id, {})
            
            print(f"\n{kpi_id}:")
            print(f"  week/all:  value={week_data.get('currentValue')}, change={week_data.get('changePercent')}")
            print(f"  month/usd: value={month_data.get('currentValue')}, change={month_data.get('changePercent')}")
            
            if week_data.get('currentValue') == month_data.get('currentValue'):
                print("  ⚠️ VALORES IDÊNTICOS!")
        
    finally:
        await client.aclose()


if __name__ == "__main__":
    asyncio.run(test_kpi_filters()) 