#!/usr/bin/env python3
"""
Debug específico para o KPI average_ticket que está retornando None.
"""

import os
import sys
from pathlib import Path

# Adicionar o diretório raiz ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Carregar variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()

def test_average_ticket_query():
    """Testa a query do average_ticket diretamente."""
    print("🔍 TESTANDO QUERY DO AVERAGE_TICKET")
    print("=" * 40)
    
    try:
        from src.services.kpi_service import get_kpi_service
        
        service = get_kpi_service()
        
        # Testar query dinâmica
        print("📊 Testando query dinâmica...")
        dynamic_query = service.query_manager.get_kpi_query("average_ticket")
        print(f"Query: {dynamic_query}")
        
        if dynamic_query:
            # Testar com diferentes filtros
            filters = [
                ("week", "all"),
                ("week", "USD"),
                ("month", "all")
            ]
            
            for timeframe, currency in filters:
                print(f"\n🔸 Testando {timeframe} + {currency}")
                
                # Obter filtros SQL
                timeframe_sql = service._get_timeframe_sql(timeframe)
                currency_sql = service._get_currency_sql(currency)
                
                print(f"   Timeframe SQL: {timeframe_sql}")
                print(f"   Currency SQL: {currency_sql}")
                
                # Executar query
                result = service._execute_query(dynamic_query, timeframe, currency)
                print(f"   Resultado: {result}")
        
        # Testar query hardcoded
        print(f"\n📊 Testando query hardcoded...")
        hardcoded_result = service._execute_hardcoded_query("average_ticket", "week", "all")
        print(f"Resultado hardcoded: {hardcoded_result}")
        
        # Testar query SQL direta
        print(f"\n📊 Testando query SQL direta...")
        test_direct_sql(service)
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()


def test_direct_sql(service):
    """Testa query SQL direta no banco."""
    try:
        from src.tools.db_utils import load_db_config, build_connection_string, get_engine
        from sqlalchemy import text
        
        # Conectar ao banco
        db_config = load_db_config(setor="cambio", cliente="L2M")
        connection_string = build_connection_string(db_config)
        engine = get_engine(connection_string)
        
        # Query simples para testar
        simple_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(valor_me) as non_null_values,
            AVG(valor_me) as average_value,
            MIN(valor_me) as min_value,
            MAX(valor_me) as max_value
        FROM boleta 
        WHERE valor_me IS NOT NULL 
        AND valor_me > 0
        """
        
        print("🔍 Query simples:")
        with engine.connect() as conn:
            result = conn.execute(text(simple_query))
            row = result.fetchone()
            if row:
                print(f"   Total records: {row[0]}")
                print(f"   Non-null values: {row[1]}")
                print(f"   Average: {row[2]}")
                print(f"   Min: {row[3]}")
                print(f"   Max: {row[4]}")
        
        # Query com filtros
        timeframe_sql = service._get_timeframe_sql("week")
        currency_sql = service._get_currency_sql("all")
        
        filtered_query = f"""
        SELECT 
            COUNT(*) as total_records,
            COUNT(valor_me) as non_null_values,
            AVG(valor_me) as average_value
        FROM boleta 
        WHERE valor_me IS NOT NULL 
        AND valor_me > 0
        AND ({timeframe_sql})
        AND ({currency_sql})
        """
        
        print(f"\n🔍 Query com filtros:")
        print(f"   Timeframe: {timeframe_sql}")
        print(f"   Currency: {currency_sql}")
        
        with engine.connect() as conn:
            result = conn.execute(text(filtered_query))
            row = result.fetchone()
            if row:
                print(f"   Total records: {row[0]}")
                print(f"   Non-null values: {row[1]}")
                print(f"   Average: {row[2]}")
        
    except Exception as e:
        print(f"❌ Erro na query direta: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Executa debug do average_ticket."""
    test_average_ticket_query()


if __name__ == "__main__":
    main()
