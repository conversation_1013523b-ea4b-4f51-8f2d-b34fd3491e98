#!/usr/bin/env python3
"""
Teste para verificar se os filtros de moeda e timeframe estão funcionando corretamente.
"""

import os
import sys
import time
import requests
from pathlib import Path

# Adicionar o diretório raiz ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Carregar variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()


def test_currency_filters():
    """Testa se os filtros de moeda produzem valores diferentes."""
    print("💰 TESTANDO FILTROS DE MOEDA")
    print("=" * 40)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    # Testar diferentes moedas
    currencies = ["all", "USD", "EUR"]
    results = {}
    
    for currency in currencies:
        print(f"\n🔸 Testando moeda: {currency}")
        
        params = {
            "timeframe": "week",
            "currency": currency,
            "priority_only": "true"
        }
        
        try:
            start_time = time.time()
            response = requests.get(base_url, params=params, timeout=30)
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                kpis = data.get("kpis", [])
                
                # Extrair valores dos KPIs principais
                kpi_values = {}
                for kpi in kpis:
                    kpi_id = kpi.get("id")
                    current_value = kpi.get("currentValue")
                    if kpi_id and current_value is not None:
                        kpi_values[kpi_id] = current_value
                
                results[currency] = {
                    "response_time_ms": response_time,
                    "kpi_count": len(kpis),
                    "values": kpi_values
                }
                
                print(f"✅ Sucesso: {len(kpis)} KPIs em {response_time:.0f}ms")
                print(f"   Total Volume: {kpi_values.get('total_volume', 'N/A')}")
                print(f"   Average Ticket: {kpi_values.get('average_ticket', 'N/A')}")
                
            else:
                print(f"❌ Erro HTTP {response.status_code}: {response.text}")
                results[currency] = {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Erro: {e}")
            results[currency] = {"error": str(e)}
    
    # Análise dos resultados
    print(f"\n{'='*40}")
    print("📊 ANÁLISE DOS FILTROS DE MOEDA")
    print(f"{'='*40}")
    
    # Verificar se os valores são diferentes
    if "all" in results and "USD" in results and "EUR" in results:
        all_values = results["all"].get("values", {})
        usd_values = results["USD"].get("values", {})
        eur_values = results["EUR"].get("values", {})
        
        print(f"\n🔍 Comparação de Valores:")
        for kpi_id in ["total_volume", "average_ticket"]:
            all_val = all_values.get(kpi_id)
            usd_val = usd_values.get(kpi_id)
            eur_val = eur_values.get(kpi_id)
            
            print(f"\n📈 {kpi_id}:")
            print(f"   ALL: {all_val}")
            print(f"   USD: {usd_val}")
            print(f"   EUR: {eur_val}")
            
            # Verificar se são diferentes
            values = [all_val, usd_val, eur_val]
            unique_values = set(v for v in values if v is not None)
            
            if len(unique_values) > 1:
                print(f"   ✅ FILTRO FUNCIONANDO - Valores diferentes!")
            elif len(unique_values) == 1:
                print(f"   ❌ PROBLEMA - Todos os valores são iguais!")
            else:
                print(f"   ⚠️  DADOS INSUFICIENTES")
    
    return results


def test_timeframe_filters():
    """Testa se os filtros de timeframe produzem valores diferentes."""
    print("\n⏰ TESTANDO FILTROS DE TIMEFRAME")
    print("=" * 40)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    # Testar diferentes timeframes
    timeframes = ["week", "month"]
    results = {}
    
    for timeframe in timeframes:
        print(f"\n🔸 Testando timeframe: {timeframe}")
        
        params = {
            "timeframe": timeframe,
            "currency": "all",
            "priority_only": "true"
        }
        
        try:
            start_time = time.time()
            response = requests.get(base_url, params=params, timeout=30)
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                kpis = data.get("kpis", [])
                
                # Extrair valores dos KPIs principais
                kpi_values = {}
                for kpi in kpis:
                    kpi_id = kpi.get("id")
                    current_value = kpi.get("currentValue")
                    if kpi_id and current_value is not None:
                        kpi_values[kpi_id] = current_value
                
                results[timeframe] = {
                    "response_time_ms": response_time,
                    "kpi_count": len(kpis),
                    "values": kpi_values
                }
                
                print(f"✅ Sucesso: {len(kpis)} KPIs em {response_time:.0f}ms")
                print(f"   Total Volume: {kpi_values.get('total_volume', 'N/A')}")
                print(f"   Average Ticket: {kpi_values.get('average_ticket', 'N/A')}")
                
            else:
                print(f"❌ Erro HTTP {response.status_code}: {response.text}")
                results[timeframe] = {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Erro: {e}")
            results[timeframe] = {"error": str(e)}
    
    # Análise dos resultados
    print(f"\n{'='*40}")
    print("📊 ANÁLISE DOS FILTROS DE TIMEFRAME")
    print(f"{'='*40}")
    
    # Verificar se os valores são diferentes
    if "week" in results and "month" in results:
        week_values = results["week"].get("values", {})
        month_values = results["month"].get("values", {})
        
        print(f"\n🔍 Comparação de Valores:")
        for kpi_id in ["total_volume", "average_ticket"]:
            week_val = week_values.get(kpi_id)
            month_val = month_values.get(kpi_id)
            
            print(f"\n📈 {kpi_id}:")
            print(f"   WEEK: {week_val}")
            print(f"   MONTH: {month_val}")
            
            # Verificar se são diferentes
            if week_val is not None and month_val is not None:
                if week_val != month_val:
                    print(f"   ✅ FILTRO FUNCIONANDO - Valores diferentes!")
                    # Verificar se month >= week (esperado)
                    if month_val >= week_val:
                        print(f"   ✅ LÓGICA CORRETA - Mês >= Semana")
                    else:
                        print(f"   ⚠️  ATENÇÃO - Mês < Semana (verificar)")
                else:
                    print(f"   ❌ PROBLEMA - Valores iguais!")
            else:
                print(f"   ⚠️  DADOS INSUFICIENTES")
    
    return results


def test_cache_invalidation():
    """Testa se o cache está sendo invalidado corretamente."""
    print("\n💾 TESTANDO INVALIDAÇÃO DE CACHE")
    print("=" * 35)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    params = {
        "timeframe": "week",
        "currency": "USD",
        "priority_only": "true"
    }
    
    # Primeira chamada
    print("🔄 Primeira chamada...")
    start_time = time.time()
    response1 = requests.get(base_url, params=params, timeout=30)
    time1 = (time.time() - start_time) * 1000
    
    # Segunda chamada (deve usar cache)
    print("🎯 Segunda chamada (cache hit esperado)...")
    start_time = time.time()
    response2 = requests.get(base_url, params=params, timeout=30)
    time2 = (time.time() - start_time) * 1000
    
    print(f"⏱️  Primeira chamada: {time1:.0f}ms")
    print(f"⏱️  Segunda chamada: {time2:.0f}ms")
    
    if time2 < time1 * 0.5:  # Segunda chamada deve ser pelo menos 50% mais rápida
        print("✅ Cache funcionando - Segunda chamada mais rápida")
    else:
        print("❌ Cache pode não estar funcionando - Tempos similares")
    
    # Mudar filtro (deve invalidar cache)
    print("\n🔄 Mudando filtro (deve invalidar cache)...")
    params["currency"] = "EUR"
    
    start_time = time.time()
    response3 = requests.get(base_url, params=params, timeout=30)
    time3 = (time.time() - start_time) * 1000
    
    print(f"⏱️  Terceira chamada (filtro diferente): {time3:.0f}ms")
    
    if time3 > time2 * 2:  # Deve ser mais lenta que cache hit
        print("✅ Cache invalidado corretamente - Terceira chamada mais lenta")
    else:
        print("⚠️  Cache pode não estar sendo invalidado corretamente")


def main():
    """Executa todos os testes de filtros."""
    print("🧪 TESTE COMPLETO DE FUNCIONALIDADE DOS FILTROS")
    print("=" * 60)
    
    # Verificar se backend está rodando
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend não está respondendo corretamente")
            return
    except:
        print("❌ Backend não está rodando. Execute o backend primeiro.")
        return
    
    print("✅ Backend está rodando\n")
    
    # Executar testes
    currency_results = test_currency_filters()
    timeframe_results = test_timeframe_filters()
    test_cache_invalidation()
    
    # Resumo final
    print(f"\n{'='*60}")
    print("🎯 RESUMO FINAL")
    print(f"{'='*60}")
    
    print("✅ Testes executados:")
    print("   - Filtros de moeda (ALL, USD, EUR)")
    print("   - Filtros de timeframe (week, month)")
    print("   - Invalidação de cache")
    
    print("\n💡 Se os valores são iguais para filtros diferentes:")
    print("   1. Verificar se as queries JSON têm os placeholders corretos")
    print("   2. Verificar se _get_currency_sql() e _get_timeframe_sql() estão corretos")
    print("   3. Verificar se o banco tem dados para diferentes moedas/períodos")


if __name__ == "__main__":
    main()
