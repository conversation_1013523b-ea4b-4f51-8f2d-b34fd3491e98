#!/usr/bin/env python3
"""
Week 6 Testing & Validation - Complete Test Suite Runner
========================================================

Comprehensive test runner for Week 6 hybrid architecture testing.
Executes unit tests, integration tests, and performance tests with detailed reporting.

Features:
- Unit tests with >90% coverage target
- Integration tests for 3-layer architecture
- Performance tests with specific metrics validation
- Detailed HTML and terminal reports
- CI/CD integration support
- Test result validation and metrics collection

Usage:
    python scripts/run_week6_tests.py [--fast] [--coverage] [--performance] [--report]

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import sys
import subprocess
import argparse
import time
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class Week6TestRunner:
    """Test runner for Week 6 testing suite."""
    
    def __init__(self, args):
        self.args = args
        self.project_root = project_root
        self.test_results = {
            'unit_tests': {},
            'integration_tests': {},
            'performance_tests': {},
            'coverage': {},
            'summary': {}
        }
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self):
        """Run complete Week 6 test suite."""
        print("🧪 WEEK 6 - TESTING & VALIDATION")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.start_time = time.time()
        
        try:
            # 1. Unit Tests
            if not self.args.fast:
                self.run_unit_tests()
            
            # 2. Integration Tests
            self.run_integration_tests()
            
            # 3. Performance Tests
            if self.args.performance:
                self.run_performance_tests()
            
            # 4. Coverage Report
            if self.args.coverage:
                self.generate_coverage_report()
            
            # 5. Generate Final Report
            if self.args.report:
                self.generate_final_report()
            
            self.end_time = time.time()
            self.print_summary()
            
            return self.validate_results()
            
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            return False
    
    def run_unit_tests(self):
        """Run unit tests for hybrid architecture components."""
        print("🔬 UNIT TESTS")
        print("-" * 30)
        
        unit_test_files = [
            "tests/unit/test_smart_query_router.py",
            "tests/unit/test_profile_detector.py", 
            "tests/unit/test_personalized_cache_system.py"
        ]
        
        for test_file in unit_test_files:
            print(f"Running {test_file}...")
            
            cmd = [
                "poetry", "run", "pytest",
                test_file,
                "-v",
                "--tb=short",
                "--disable-warnings"
            ]
            
            if self.args.coverage:
                cmd.extend([
                    "--cov=src",
                    "--cov-append",
                    "--cov-report=term-missing"
                ])
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            self.test_results['unit_tests'][test_file] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            if result.returncode == 0:
                print(f"  ✅ {test_file} - PASSED")
            else:
                print(f"  ❌ {test_file} - FAILED")
                if self.args.verbose:
                    print(f"     Error: {result.stderr}")
        
        print()
    
    def run_integration_tests(self):
        """Run integration tests for 3-layer architecture."""
        print("🔗 INTEGRATION TESTS")
        print("-" * 30)
        
        integration_test_files = [
            "tests/integration/test_hybrid_architecture_integration_real.py"
        ]
        
        for test_file in integration_test_files:
            print(f"Running {test_file}...")
            
            cmd = [
                "poetry", "run", "pytest",
                test_file,
                "-v",
                "--tb=short",
                "--disable-warnings",
                "-m", "not slow"  # Skip slow tests unless explicitly requested
            ]
            
            if self.args.coverage:
                cmd.extend([
                    "--cov=src",
                    "--cov-append"
                ])
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            self.test_results['integration_tests'][test_file] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            if result.returncode == 0:
                print(f"  ✅ {test_file} - PASSED")
            else:
                print(f"  ❌ {test_file} - FAILED")
                if self.args.verbose:
                    print(f"     Error: {result.stderr}")
        
        print()
    
    def run_performance_tests(self):
        """Run performance tests with specific metrics validation."""
        print("⚡ PERFORMANCE TESTS")
        print("-" * 30)
        
        performance_test_files = [
            "tests/performance/test_hybrid_architecture_performance.py"
        ]
        
        for test_file in performance_test_files:
            print(f"Running {test_file}...")
            
            cmd = [
                "poetry", "run", "pytest",
                test_file,
                "-v",
                "--tb=short",
                "--disable-warnings",
                "-m", "performance"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            self.test_results['performance_tests'][test_file] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            if result.returncode == 0:
                print(f"  ✅ {test_file} - PASSED")
                self.extract_performance_metrics(result.stdout)
            else:
                print(f"  ❌ {test_file} - FAILED")
                if self.args.verbose:
                    print(f"     Error: {result.stderr}")
        
        print()
    
    def generate_coverage_report(self):
        """Generate coverage report."""
        print("📊 COVERAGE REPORT")
        print("-" * 30)
        
        # Generate HTML coverage report
        cmd = [
            "poetry", "run", "coverage", "html",
            "--directory=htmlcov"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode == 0:
            print("  ✅ HTML coverage report generated: htmlcov/index.html")
        else:
            print("  ❌ Failed to generate HTML coverage report")
        
        # Get coverage percentage
        cmd = ["poetry", "run", "coverage", "report", "--format=json"]
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        if result.returncode == 0:
            try:
                coverage_data = json.loads(result.stdout)
                total_coverage = coverage_data['totals']['percent_covered']
                self.test_results['coverage']['total_percentage'] = total_coverage
                
                print(f"  📈 Total Coverage: {total_coverage:.1f}%")
                
                if total_coverage >= 90.0:
                    print("  ✅ Coverage target (>90%) achieved!")
                else:
                    print(f"  ⚠️  Coverage below 90% target")
                    
            except (json.JSONDecodeError, KeyError) as e:
                print(f"  ❌ Failed to parse coverage data: {e}")
        
        print()
    
    def extract_performance_metrics(self, stdout):
        """Extract performance metrics from test output."""
        # This is a simplified version - in practice, you'd parse the actual test output
        # or use pytest plugins to collect metrics
        
        metrics = {
            'cache_hit_rates': {},
            'response_times': {},
            'success_rates': {},
            'throughput': {}
        }
        
        # Parse stdout for performance metrics
        lines = stdout.split('\n')
        for line in lines:
            if 'Cache hit rate' in line:
                # Extract cache hit rate metrics
                pass
            elif 'Response time' in line:
                # Extract response time metrics
                pass
            elif 'Success rate' in line:
                # Extract success rate metrics
                pass
        
        self.test_results['performance_tests']['metrics'] = metrics
    
    def generate_final_report(self):
        """Generate final test report."""
        print("📋 FINAL REPORT")
        print("-" * 30)
        
        report_file = self.project_root / "test_reports" / f"week6_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        # Add summary information
        self.test_results['summary'] = {
            'start_time': self.start_time,
            'end_time': self.end_time,
            'total_duration': self.end_time - self.start_time if self.end_time else None,
            'timestamp': datetime.now().isoformat(),
            'args': vars(self.args)
        }
        
        # Write report
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"  📄 Test report saved: {report_file}")
        print()
    
    def print_summary(self):
        """Print test execution summary."""
        print("📊 SUMMARY")
        print("=" * 60)
        
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # Count results
        unit_passed = sum(1 for r in self.test_results['unit_tests'].values() if r['passed'])
        unit_total = len(self.test_results['unit_tests'])
        
        integration_passed = sum(1 for r in self.test_results['integration_tests'].values() if r['passed'])
        integration_total = len(self.test_results['integration_tests'])
        
        performance_passed = sum(1 for r in self.test_results['performance_tests'].values() if r['passed'])
        performance_total = len(self.test_results['performance_tests'])
        
        print(f"⏱️  Total Duration: {total_duration:.1f} seconds")
        print(f"🔬 Unit Tests: {unit_passed}/{unit_total} passed")
        print(f"🔗 Integration Tests: {integration_passed}/{integration_total} passed")
        print(f"⚡ Performance Tests: {performance_passed}/{performance_total} passed")
        
        if 'total_percentage' in self.test_results['coverage']:
            coverage = self.test_results['coverage']['total_percentage']
            print(f"📊 Coverage: {coverage:.1f}%")
        
        print()
    
    def validate_results(self):
        """Validate test results against Week 6 criteria."""
        print("✅ VALIDATION")
        print("-" * 30)
        
        validation_passed = True
        
        # Check unit test coverage
        if 'total_percentage' in self.test_results['coverage']:
            coverage = self.test_results['coverage']['total_percentage']
            if coverage >= 90.0:
                print(f"  ✅ Coverage target achieved: {coverage:.1f}% >= 90%")
            else:
                print(f"  ❌ Coverage below target: {coverage:.1f}% < 90%")
                validation_passed = False
        
        # Check all tests passed
        all_unit_passed = all(r['passed'] for r in self.test_results['unit_tests'].values())
        all_integration_passed = all(r['passed'] for r in self.test_results['integration_tests'].values())
        all_performance_passed = all(r['passed'] for r in self.test_results['performance_tests'].values())
        
        if all_unit_passed:
            print("  ✅ All unit tests passed")
        else:
            print("  ❌ Some unit tests failed")
            validation_passed = False
        
        if all_integration_passed:
            print("  ✅ All integration tests passed")
        else:
            print("  ❌ Some integration tests failed")
            validation_passed = False
        
        if self.args.performance:
            if all_performance_passed:
                print("  ✅ All performance tests passed")
            else:
                print("  ❌ Some performance tests failed")
                validation_passed = False
        
        print()
        
        if validation_passed:
            print("🎉 WEEK 6 TESTING & VALIDATION - SUCCESS!")
            print("All tests passed and targets achieved.")
        else:
            print("❌ WEEK 6 TESTING & VALIDATION - FAILED")
            print("Some tests failed or targets not met.")
        
        return validation_passed


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Week 6 Testing & Validation Suite")
    parser.add_argument("--fast", action="store_true", help="Skip unit tests for faster execution")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage reports")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--report", action="store_true", help="Generate detailed reports")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Default to all options if none specified
    if not any([args.fast, args.coverage, args.performance, args.report]):
        args.coverage = True
        args.performance = True
        args.report = True
    
    runner = Week6TestRunner(args)
    success = runner.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
