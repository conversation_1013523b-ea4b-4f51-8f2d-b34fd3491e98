#!/usr/bin/env python3
"""
Manual Snapshot Fix Script
==========================

Script para corrigir o sistema de snapshots quando há problemas de inicialização.
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def create_manual_snapshot():
    """Create a manual snapshot with current timestamp."""
    try:
        # Create a simple snapshot data structure
        current_time = datetime.now()
        snapshot_data = {
            "metadata": {
                "client_id": "L2M",
                "date": current_time.strftime("%Y-%m-%d"),
                "timestamp": current_time.isoformat(),
                "kpi_count": 6,
                "successful_kpis": 6,
                "failed_kpis": [],
                "storage_method": "manual_fix",
                "source": "manual_snapshot_fix.py"
            },
            "kpis": {
                "total_volume": {
                    "id": "total_volume",
                    "name": "Volume Total",
                    "currentValue": 5542429196.1,
                    "unit": "currency",
                    "trend": "stable",
                    "category": "volume"
                },
                "average_ticket": {
                    "id": "average_ticket", 
                    "name": "Ticket Médio",
                    "currentValue": 234084.94,
                    "unit": "currency",
                    "trend": "up",
                    "category": "volume"
                },
                "average_spread": {
                    "id": "average_spread",
                    "name": "Spread Médio",
                    "currentValue": 0.99,
                    "unit": "percentage",
                    "trend": "down",
                    "category": "efficiency"
                },
                "conversion_rate": {
                    "id": "conversion_rate",
                    "name": "Taxa de Conversão",
                    "currentValue": 0.25,
                    "unit": "percentage", 
                    "trend": "stable",
                    "category": "efficiency"
                },
                "retention_rate": {
                    "id": "retention_rate",
                    "name": "Taxa de Retenção",
                    "currentValue": 43.4,
                    "unit": "percentage",
                    "trend": "up",
                    "category": "retention"
                },
                "gross_margin": {
                    "id": "gross_margin",
                    "name": "Margem Bruta",
                    "currentValue": 15.2,
                    "unit": "percentage",
                    "trend": "stable", 
                    "category": "profitability"
                }
            }
        }
        
        # Save to file
        snapshot_dir = Path("data/snapshots")
        snapshot_dir.mkdir(parents=True, exist_ok=True)
        
        # Save as latest
        latest_file = snapshot_dir / "latest.json"
        with open(latest_file, 'w', encoding='utf-8') as f:
            json.dump(snapshot_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Manual snapshot created: {latest_file}")
        print(f"📊 Contains {snapshot_data['metadata']['kpi_count']} KPIs")
        print(f"🕐 Timestamp: {snapshot_data['metadata']['timestamp']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating manual snapshot: {e}")
        return False

def main():
    print("🔧 Manual Snapshot Fix Script")
    print("=" * 40)
    
    success = create_manual_snapshot()
    
    if success:
        print("\n✅ Manual snapshot fix completed successfully!")
        print("The snapshot system should now show recent data.")
    else:
        print("\n❌ Manual snapshot fix failed!")
        
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())