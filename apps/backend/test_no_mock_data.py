#!/usr/bin/env python3
"""
Test to verify that all mock data, fallback mechanisms, and placeholder implementations 
have been removed from the DataHero4 codebase.

This test implements the "fail fast and fail loud" philosophy by ensuring:
1. No mock data anywhere in the system
2. All failures are immediately visible and not masked
3. Real database queries for all functionality
4. Clear error messages when systems fail
5. No silent fallbacks that hide underlying issues
"""

import os
import sys
import requests
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
load_dotenv()


def test_no_mock_data_in_kpi_service():
    """Test that KPI service fails fast when data is unavailable."""
    print("🧪 TESTING: KPI Service Fail-Fast Behavior")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    # Test with invalid parameters that should cause immediate failure
    test_cases = [
        {
            "name": "Invalid Currency",
            "params": {"timeframe": "week", "currency": "INVALID_CURRENCY", "priority_only": "true"},
            "should_fail": True
        },
        {
            "name": "Invalid Timeframe", 
            "params": {"timeframe": "invalid_timeframe", "currency": "all", "priority_only": "true"},
            "should_fail": True
        },
        {
            "name": "Valid Parameters",
            "params": {"timeframe": "week", "currency": "all", "priority_only": "true"},
            "should_fail": False
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔸 Testing: {test_case['name']}")
        
        try:
            response = requests.get(base_url, params=test_case["params"], timeout=30)
            
            if test_case["should_fail"]:
                if response.status_code == 200:
                    data = response.json()
                    kpis = data.get("kpis", [])
                    
                    # Check if any KPIs have obviously mock values
                    for kpi in kpis:
                        value = kpi.get("currentValue")
                        if value is not None:
                            # Check for common mock patterns
                            if isinstance(value, (int, float)):
                                # Values like 1000, 5000, 10000 might be mock
                                if value in [1000, 5000, 10000, 100000, 1000000]:
                                    print(f"   ⚠️  Suspicious round number: {value}")
                                
                                # Check for obviously fake values
                                if value == 123.45 or value == 999.99:
                                    print(f"   ❌ MOCK DATA DETECTED: {value}")
                                    return False
                    
                    print(f"   ✅ No obvious mock data detected")
                else:
                    print(f"   ✅ Failed as expected: HTTP {response.status_code}")
            else:
                if response.status_code == 200:
                    print(f"   ✅ Succeeded as expected")
                else:
                    print(f"   ❌ Unexpected failure: HTTP {response.status_code}")
                    
        except requests.exceptions.RequestException as e:
            if test_case["should_fail"]:
                print(f"   ✅ Failed fast as expected: {e}")
            else:
                print(f"   ❌ Unexpected connection error: {e}")
    
    return True


def test_no_fallback_chart_data():
    """Test that chart data generation fails fast when no real data is available."""
    print("\n📊 TESTING: Chart Data Fail-Fast Behavior")
    print("=" * 50)
    
    # Test chart data endpoint (if it exists)
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    try:
        response = requests.get(base_url, params={
            "timeframe": "week",
            "currency": "all", 
            "priority_only": "true"
        }, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            kpis = data.get("kpis", [])
            
            for kpi in kpis:
                chart_data = kpi.get("chartData", [])
                
                if chart_data:
                    # Check for obviously generated/mock chart data
                    for point in chart_data:
                        value = point.get("value")
                        name = point.get("name")
                        
                        # Check for mock patterns
                        if name and ("Ponto" in name or "Mock" in name or "Test" in name):
                            print(f"   ❌ MOCK CHART DATA DETECTED: {name}")
                            return False
                        
                        # Check for obviously random values
                        if isinstance(value, (int, float)):
                            # Values with many decimal places might be random
                            if isinstance(value, float) and len(str(value).split('.')[-1]) > 4:
                                print(f"   ⚠️  Suspicious precision: {value}")
                
                print(f"   ✅ Chart data for {kpi.get('id')} appears real")
        
        return True
        
    except Exception as e:
        print(f"   ✅ Failed fast as expected: {e}")
        return True


def test_no_hardcoded_values():
    """Test that no hardcoded mock values are returned."""
    print("\n🔢 TESTING: No Hardcoded Values")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    # Known mock values that should NOT appear
    forbidden_values = [
        5542429196.1,  # Old hardcoded total_volume
        234084.94,      # Old hardcoded average_ticket
        0.99,           # Old hardcoded average_spread
        123.45,         # Common test value
        999.99,         # Common test value
        1000000,        # Round number
        2850000,        # Test value from frontend
    ]
    
    try:
        response = requests.get(base_url, params={
            "timeframe": "week",
            "currency": "all",
            "priority_only": "true"
        }, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            kpis = data.get("kpis", [])
            
            for kpi in kpis:
                value = kpi.get("currentValue")
                
                if value in forbidden_values:
                    print(f"   ❌ HARDCODED VALUE DETECTED: {value} in KPI {kpi.get('id')}")
                    return False
            
            print(f"   ✅ No hardcoded values detected in {len(kpis)} KPIs")
            return True
        else:
            print(f"   ✅ API failed fast: HTTP {response.status_code}")
            return True
            
    except Exception as e:
        print(f"   ✅ Failed fast as expected: {e}")
        return True


def test_error_visibility():
    """Test that errors are visible and not masked by fallbacks."""
    print("\n🚨 TESTING: Error Visibility")
    print("=" * 50)
    
    # Test with completely invalid parameters
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    try:
        response = requests.get(base_url, params={
            "timeframe": "completely_invalid",
            "currency": "nonexistent_currency",
            "priority_only": "invalid_boolean"
        }, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("kpis"):
                print(f"   ❌ ERROR MASKED: API returned data despite invalid parameters")
                return False
            else:
                print(f"   ✅ No data returned for invalid parameters")
                return True
        else:
            print(f"   ✅ Failed fast with HTTP {response.status_code}")
            return True
            
    except requests.exceptions.Timeout:
        print(f"   ❌ TIMEOUT: System may be hanging instead of failing fast")
        return False
    except Exception as e:
        print(f"   ✅ Failed fast as expected: {e}")
        return True


def main():
    """Run all tests to verify no mock data or fallback mechanisms exist."""
    print("🧪 COMPREHENSIVE TEST: No Mock Data or Fallback Mechanisms")
    print("=" * 70)
    
    # Check if backend is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend not responding correctly")
            return False
    except:
        print("❌ Backend not running. Start backend first:")
        print("   cd apps/backend && poetry run uvicorn src.interfaces.api:app --reload --host 0.0.0.0 --port 8000")
        return False
    
    print("✅ Backend is running\n")
    
    # Run all tests
    tests = [
        test_no_mock_data_in_kpi_service,
        test_no_fallback_chart_data,
        test_no_hardcoded_values,
        test_error_visibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print(f"\n{'='*70}")
    print("🎯 SUMMARY")
    print(f"{'='*70}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 SUCCESS: No mock data or fallback mechanisms detected!")
        print("✅ System implements 'fail fast and fail loud' philosophy")
        print("✅ All failures are immediately visible")
        print("✅ No silent degradation that hides problems")
        return True
    else:
        print(f"\n❌ FAILURE: {total - passed} tests failed")
        print("⚠️  Mock data or fallback mechanisms still present")
        print("⚠️  System may be hiding real issues")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
