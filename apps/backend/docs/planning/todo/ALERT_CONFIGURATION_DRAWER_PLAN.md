# Plano de Implementação: Configuração de Alertas no Drawer

## 🎯 Objetivo

Implementar no KPI Drawer:
1. **Botão de configuração de alertas** que abre um modal
2. **Caixa de alerta ativo** que aparece no topo quando há alertas disparados
3. **Modal de configuração** para definir thresholds personalizados

## 📋 Análise da Funcionalidade Atual

### **Sistema de Alertas Existente:**
- ✅ Thresholds pré-configurados em `kpi_definitions.py`
- ✅ Avaliação automática em `hybrid_kpi_service.py`
- ✅ Indicadores visuais nos cards (ícone + border)
- ✅ APIs para consultar alertas ativos (`/api/alerts/active`)

### **Drawer Atual:**
- ✅ Estrutura split-screen (45% card + 55% conteúdo)
- ✅ ActionButtons com botão "Configurar Alerta" (Bell icon)
- ✅ Sistema de tabs (settings, charts, edit)
- ✅ Chat input para configuração via linguagem natural

## 🏗️ Arquitetura da Solução

### **1. Componentes a Criar:**

```
apps/frontend/src/components/kpi-drawer/
├── AlertConfigModal.tsx          # 🆕 Modal de configuração
├── ActiveAlertBanner.tsx         # 🆕 Banner de alerta ativo
└── hooks/
    └── useAlertConfig.tsx        # 🆕 Hook para gerenciar alertas
```

### **2. APIs a Implementar:**

```
apps/backend/src/api/
├── alert_config_routes.py        # 🆕 Endpoints de configuração
└── models/
    └── alert_config.py           # 🆕 Modelos de dados
```

## 🎨 Design da Interface

### **1. Active Alert Banner (Topo do Drawer)**

```tsx
// Aparece no topo do drawer quando há alertas ativos
<div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-4">
  <div className="flex items-center">
    <AlertTriangle className="w-5 h-5 text-amber-500 mr-3" />
    <div>
      <p className="text-sm font-medium text-amber-800">
        Alerta Ativo: Volume Total
      </p>
      <p className="text-xs text-amber-700">
        Valor atual: $750K está abaixo do limite crítico de $1M
        <span className="ml-2 text-amber-600">
          Disparado há 2 horas
        </span>
      </p>
    </div>
  </div>
</div>
```

### **2. Alert Configuration Modal**

```tsx
// Modal com formulário de configuração
<Dialog>
  <DialogContent className="max-w-md">
    <DialogHeader>
      <DialogTitle>Configurar Alertas - {kpiName}</DialogTitle>
    </DialogHeader>
    
    <div className="space-y-4">
      {/* Toggle para ativar/desativar */}
      <div className="flex items-center justify-between">
        <Label>Alertas Ativos</Label>
        <Switch checked={alertsEnabled} />
      </div>
      
      {/* Threshold Warning */}
      <div>
        <Label>Limite de Aviso</Label>
        <Input type="number" placeholder="1000000" />
        <p className="text-xs text-gray-500">
          Alerta amarelo quando valor for menor que este limite
        </p>
      </div>
      
      {/* Threshold Critical */}
      <div>
        <Label>Limite Crítico</Label>
        <Input type="number" placeholder="500000" />
        <p className="text-xs text-gray-500">
          Alerta vermelho quando valor for menor que este limite
        </p>
      </div>
      
      {/* Canais de Notificação */}
      <div>
        <Label>Notificar via:</Label>
        <div className="flex gap-2 mt-2">
          <Checkbox id="email" />
          <Label htmlFor="email">Email</Label>
          <Checkbox id="dashboard" />
          <Label htmlFor="dashboard">Dashboard</Label>
        </div>
      </div>
    </div>
    
    <DialogFooter>
      <Button variant="outline">Cancelar</Button>
      <Button>Salvar Configuração</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

## 🔧 Implementação Técnica

### **Fase 1: Backend - APIs de Configuração (45min)**

#### **1.1 Modelo de Dados**
```python
# apps/backend/src/models/alert_config.py
@dataclass
class AlertConfig:
    kpi_id: str
    user_id: str
    enabled: bool
    warning_threshold: Optional[float]
    critical_threshold: Optional[float]
    notification_channels: List[str]
    created_at: datetime
    updated_at: datetime
```

#### **1.2 Endpoints**
```python
# apps/backend/src/api/alert_config_routes.py
@router.get("/api/alerts/config/{kpi_id}")
async def get_alert_config(kpi_id: str, user_id: str):
    """Buscar configuração de alerta para KPI específico"""

@router.post("/api/alerts/config/{kpi_id}")
async def save_alert_config(kpi_id: str, config: AlertConfig):
    """Salvar configuração de alerta personalizada"""

@router.get("/api/alerts/active/{kpi_id}")
async def get_active_alert(kpi_id: str):
    """Buscar alerta ativo para KPI específico"""
```

### **Fase 2: Frontend - Hook de Gerenciamento (30min)**

```tsx
// apps/frontend/src/components/kpi-drawer/hooks/useAlertConfig.tsx
export const useAlertConfig = (kpiId: string) => {
  const [config, setConfig] = useState<AlertConfig | null>(null);
  const [activeAlert, setActiveAlert] = useState<ActiveAlert | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadConfig = async () => {
    // Buscar configuração atual
  };

  const saveConfig = async (newConfig: AlertConfig) => {
    // Salvar nova configuração
  };

  const loadActiveAlert = async () => {
    // Buscar alerta ativo
  };

  return {
    config,
    activeAlert,
    isLoading,
    saveConfig,
    loadConfig,
    loadActiveAlert
  };
};
```

### **Fase 3: Frontend - Componentes (60min)**

#### **3.1 Active Alert Banner**
```tsx
// apps/frontend/src/components/kpi-drawer/ActiveAlertBanner.tsx
export const ActiveAlertBanner: React.FC<{
  alert: ActiveAlert;
  onDismiss?: () => void;
}> = ({ alert, onDismiss }) => {
  const getAlertColor = (type: 'warning' | 'critical') => {
    return type === 'critical' 
      ? 'border-red-400 bg-red-50 text-red-800'
      : 'border-amber-400 bg-amber-50 text-amber-800';
  };

  return (
    <div className={cn("border-l-4 p-4 mb-4", getAlertColor(alert.type))}>
      {/* Conteúdo do banner */}
    </div>
  );
};
```

#### **3.2 Alert Configuration Modal**
```tsx
// apps/frontend/src/components/kpi-drawer/AlertConfigModal.tsx
export const AlertConfigModal: React.FC<{
  kpiId: string;
  kpiName: string;
  isOpen: boolean;
  onClose: () => void;
}> = ({ kpiId, kpiName, isOpen, onClose }) => {
  const { config, saveConfig, isLoading } = useAlertConfig(kpiId);
  const [formData, setFormData] = useState<AlertConfigForm>({});

  const handleSave = async () => {
    await saveConfig(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {/* Formulário de configuração */}
    </Dialog>
  );
};
```

### **Fase 4: Integração no Drawer (30min)**

#### **4.1 Modificar KPIDrawerContent**
```tsx
// Adicionar no topo do drawer
{activeAlert && (
  <ActiveAlertBanner
    alert={activeAlert}
    onDismiss={() => setActiveAlert(null)}
  />
)}

// Modificar ActionButtons para abrir modal
<AlertConfigModal
  kpiId={kpiId}
  kpiName={kpiData.name}
  isOpen={showAlertModal}
  onClose={() => setShowAlertModal(false)}
/>
```

## 💻 Exemplos de Código Detalhados

### **1. AlertConfigModal com Radix UI**
```tsx
import * as Dialog from '@radix-ui/react-dialog';
import * as Form from '@radix-ui/react-form';
import { AlertTriangle, X, Bell } from 'lucide-react';

export const AlertConfigModal: React.FC<AlertConfigModalProps> = ({
  kpiId,
  kpiName,
  isOpen,
  onClose
}) => {
  const { config, saveConfig, isLoading } = useAlertConfig(kpiId);
  const [formData, setFormData] = useState({
    enabled: config?.enabled ?? true,
    warningThreshold: config?.warningThreshold ?? '',
    criticalThreshold: config?.criticalThreshold ?? '',
    notificationChannels: config?.notificationChannels ?? ['dashboard']
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    await saveConfig(formData);
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-6 w-full max-w-md z-50">
          <Dialog.Title className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
            <Bell className="w-5 h-5 text-blue-600" />
            Configurar Alertas
          </Dialog.Title>

          <Dialog.Description className="text-sm text-gray-600 mb-4">
            Configure os limites de alerta para <strong>{kpiName}</strong>
          </Dialog.Description>

          <Form.Root onSubmit={handleSubmit} className="space-y-4">
            {/* Toggle para ativar/desativar */}
            <div className="flex items-center justify-between">
              <Form.Label className="text-sm font-medium text-gray-700">
                Alertas Ativos
              </Form.Label>
              <Switch
                checked={formData.enabled}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, enabled: checked }))
                }
              />
            </div>

            {formData.enabled && (
              <>
                {/* Warning Threshold */}
                <Form.Field name="warningThreshold">
                  <Form.Label className="text-sm font-medium text-gray-700">
                    Limite de Aviso
                  </Form.Label>
                  <Form.Control asChild>
                    <Input
                      type="number"
                      placeholder="1000000"
                      value={formData.warningThreshold}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        warningThreshold: e.target.value
                      }))}
                    />
                  </Form.Control>
                  <Form.Message match="valueMissing" className="text-xs text-red-600">
                    Por favor, insira um valor para o limite de aviso
                  </Form.Message>
                </Form.Field>

                {/* Critical Threshold */}
                <Form.Field name="criticalThreshold">
                  <Form.Label className="text-sm font-medium text-gray-700">
                    Limite Crítico
                  </Form.Label>
                  <Form.Control asChild>
                    <Input
                      type="number"
                      placeholder="500000"
                      value={formData.criticalThreshold}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        criticalThreshold: e.target.value
                      }))}
                    />
                  </Form.Control>
                  <Form.Message match="valueMissing" className="text-xs text-red-600">
                    Por favor, insira um valor para o limite crítico
                  </Form.Message>
                </Form.Field>

                {/* Notification Channels */}
                <div>
                  <Form.Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Notificar via:
                  </Form.Label>
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2">
                      <Checkbox
                        checked={formData.notificationChannels.includes('email')}
                        onCheckedChange={(checked) => {
                          setFormData(prev => ({
                            ...prev,
                            notificationChannels: checked
                              ? [...prev.notificationChannels, 'email']
                              : prev.notificationChannels.filter(c => c !== 'email')
                          }));
                        }}
                      />
                      <span className="text-sm text-gray-700">Email</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <Checkbox
                        checked={formData.notificationChannels.includes('dashboard')}
                        onCheckedChange={(checked) => {
                          setFormData(prev => ({
                            ...prev,
                            notificationChannels: checked
                              ? [...prev.notificationChannels, 'dashboard']
                              : prev.notificationChannels.filter(c => c !== 'dashboard')
                          }));
                        }}
                      />
                      <span className="text-sm text-gray-700">Dashboard</span>
                    </label>
                  </div>
                </div>
              </>
            )}

            <div className="flex justify-end gap-3 pt-4">
              <Dialog.Close asChild>
                <Button variant="outline" type="button">
                  Cancelar
                </Button>
              </Dialog.Close>
              <Form.Submit asChild>
                <Button disabled={isLoading}>
                  {isLoading ? 'Salvando...' : 'Salvar Configuração'}
                </Button>
              </Form.Submit>
            </div>
          </Form.Root>

          <Dialog.Close asChild>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4"
            >
              <X className="w-4 h-4" />
            </Button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
```

### **2. ActiveAlertBanner Component**
```tsx
import { AlertTriangle, X, Clock } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export const ActiveAlertBanner: React.FC<ActiveAlertBannerProps> = ({
  alert,
  onDismiss
}) => {
  const getAlertStyles = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? 'border-red-400 bg-red-50 text-red-800'
      : 'border-amber-400 bg-amber-50 text-amber-800';
  };

  const getIcon = (type: 'warning' | 'critical') => {
    return type === 'critical'
      ? <AlertTriangle className="w-5 h-5 text-red-500" />
      : <AlertTriangle className="w-5 h-5 text-amber-500" />;
  };

  const formatTimeAgo = (triggeredAt: string) => {
    const now = new Date();
    const triggered = new Date(triggeredAt);
    const diffInHours = Math.floor((now.getTime() - triggered.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'há poucos minutos';
    if (diffInHours === 1) return 'há 1 hora';
    return `há ${diffInHours} horas`;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "border-l-4 p-4 mb-4 rounded-r-lg",
          getAlertStyles(alert.type)
        )}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            {getIcon(alert.type)}
            <div className="flex-1">
              <p className="text-sm font-medium">
                Alerta {alert.type === 'critical' ? 'Crítico' : 'de Aviso'}: {alert.kpiName}
              </p>
              <p className="text-xs mt-1">
                Valor atual: <strong>{alert.currentValue.toLocaleString()}</strong> está{' '}
                {alert.type === 'critical' ? 'abaixo do limite crítico' : 'no limite de aviso'} de{' '}
                <strong>{alert.threshold.toLocaleString()}</strong>
              </p>
              <div className="flex items-center gap-1 mt-2 text-xs opacity-75">
                <Clock className="w-3 h-3" />
                <span>Disparado {formatTimeAgo(alert.triggeredAt)}</span>
              </div>
            </div>
          </div>

          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-6 w-6 p-0 hover:bg-white/20"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
```

### **3. useAlertConfig Hook**
```tsx
import { useState, useEffect, useCallback } from 'react';

export const useAlertConfig = (kpiId: string) => {
  const [config, setConfig] = useState<AlertConfig | null>(null);
  const [activeAlert, setActiveAlert] = useState<ActiveAlert | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/alerts/config/${kpiId}?user_id=current_user`);

      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      } else {
        // Se não existe configuração, usar padrões do KPI
        const kpiDefaults = await fetch(`/api/kpis/${kpiId}/definition`);
        const kpiData = await kpiDefaults.json();

        setConfig({
          kpiId,
          userId: 'current_user',
          enabled: true,
          warningThreshold: kpiData.thresholds?.warning,
          criticalThreshold: kpiData.thresholds?.critical,
          notificationChannels: ['dashboard'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
    } catch (err) {
      setError('Erro ao carregar configuração de alerta');
      console.error('Error loading alert config:', err);
    } finally {
      setIsLoading(false);
    }
  }, [kpiId]);

  const saveConfig = useCallback(async (newConfig: Partial<AlertConfig>) => {
    try {
      setIsLoading(true);
      setError(null);

      const configToSave = {
        ...config,
        ...newConfig,
        kpiId,
        userId: 'current_user',
        updatedAt: new Date().toISOString()
      };

      const response = await fetch(`/api/alerts/config/${kpiId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configToSave),
      });

      if (response.ok) {
        const savedConfig = await response.json();
        setConfig(savedConfig.config);

        // Recarregar alerta ativo após salvar configuração
        await loadActiveAlert();
      } else {
        throw new Error('Erro ao salvar configuração');
      }
    } catch (err) {
      setError('Erro ao salvar configuração de alerta');
      console.error('Error saving alert config:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [config, kpiId]);

  const loadActiveAlert = useCallback(async () => {
    try {
      const response = await fetch(`/api/alerts/active/${kpiId}`);

      if (response.ok) {
        const data = await response.json();
        setActiveAlert(data.alert);
      } else {
        setActiveAlert(null);
      }
    } catch (err) {
      console.error('Error loading active alert:', err);
      setActiveAlert(null);
    }
  }, [kpiId]);

  // Carregar dados iniciais
  useEffect(() => {
    loadConfig();
    loadActiveAlert();
  }, [loadConfig, loadActiveAlert]);

  // Polling para alertas ativos (a cada 30 segundos)
  useEffect(() => {
    const interval = setInterval(loadActiveAlert, 30000);
    return () => clearInterval(interval);
  }, [loadActiveAlert]);

  return {
    config,
    activeAlert,
    isLoading,
    error,
    saveConfig,
    loadConfig,
    loadActiveAlert,
    dismissAlert: () => setActiveAlert(null)
  };
};
```

### **4. Modificação no ActionButtons**
```tsx
// apps/frontend/src/components/kpi-drawer/ActionButtons.tsx
import { useState } from 'react';
import { AlertConfigModal } from './AlertConfigModal';

export const ActionButtons: React.FC<Props> = ({ kpiId }) => {
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [hoveredIcon, setHoveredIcon] = useState<number | null>(null);

  const actions = [
    {
      icon: TrendingUp,
      label: 'Análise Avançada',
      description: 'Insights e correlações',
      tooltip: 'Análise',
      onClick: () => console.log('Análise avançada')
    },
    {
      icon: FileText,
      label: 'Gerar Relatório',
      description: 'Relatório detalhado em PDF',
      tooltip: 'Relatório',
      onClick: () => console.log('Gerar relatório')
    },
    {
      icon: Bell,
      label: 'Configurar Alerta',
      description: 'Notificações personalizadas',
      tooltip: 'Alerta',
      onClick: () => setShowAlertModal(true) // 🆕 Abre o modal
    },
    {
      icon: Settings,
      label: 'Editar Configurações',
      description: 'Parâmetros do indicador',
      tooltip: 'Config',
      onClick: () => console.log('Editar configurações')
    },
    {
      icon: Download,
      label: 'Exportar Dados',
      description: 'Download em CSV/Excel',
      tooltip: 'Exportar',
      onClick: () => console.log('Exportar dados')
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Ações Rápidas</h3>

      <div className="grid grid-cols-2 gap-4 relative">
        {actions.map((action, index) => {
          const Icon = action.icon;

          return (
            <Card
              key={index}
              className="p-4 cursor-pointer transition-all border-gray-200 hover:border-gray-300 hover:bg-gray-50"
            >
              <button
                className="w-full text-left"
                onClick={action.onClick} // 🆕 Adiciona onClick
              >
                <div
                  className="inline-flex items-center justify-center w-10 h-10 rounded-lg mb-3 bg-gray-100 text-gray-600 relative"
                  onMouseEnter={() => setHoveredIcon(index)}
                  onMouseLeave={() => setHoveredIcon(null)}
                >
                  <Icon className="w-4 h-4" />

                  {/* Tooltip */}
                  {hoveredIcon === index && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-50">
                      <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                        {action.tooltip}
                      </div>
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                    </div>
                  )}
                </div>
                <h4 className="font-medium text-gray-900 mb-1">
                  {action.label}
                </h4>
                <p className="text-xs text-gray-600">
                  {action.description}
                </p>
              </button>
            </Card>
          );
        })}
      </div>

      {/* 🆕 Modal de Configuração de Alertas */}
      <AlertConfigModal
        kpiId={kpiId}
        kpiName="Nome do KPI" // Será passado via props
        isOpen={showAlertModal}
        onClose={() => setShowAlertModal(false)}
      />
    </div>
  );
};
```

### **5. Integração no KPIDrawerContent**
```tsx
// apps/frontend/src/components/kpi-drawer/KPIDrawerContent.tsx
import { useAlertConfig } from './hooks/useAlertConfig';
import { ActiveAlertBanner } from './ActiveAlertBanner';

export const KPIDrawerContent: React.FC<Props> = ({
  kpiId,
  onClose,
  // ... outras props
}) => {
  const { activeAlert, dismissAlert } = useAlertConfig(kpiId);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6">
        <button onClick={onClose} className="float-right p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <X className="w-4 h-4 text-gray-400" />
        </button>
        <h2 className="text-xl font-bold text-gray-900 pr-8">
          {kpiData.description}
        </h2>
        <p className="text-gray-600 mt-2">
          {kpiData.interpretation}
        </p>
      </div>

      {/* 🆕 Banner de Alerta Ativo */}
      {activeAlert && (
        <div className="px-6">
          <ActiveAlertBanner
            alert={activeAlert}
            onDismiss={dismissAlert}
          />
        </div>
      )}

      {/* Resto do conteúdo... */}
      <ScrollArea className="flex-1">
        <div className="p-6">
          {/* Conteúdo existente */}
          <ActionButtons kpiId={kpiId} />
        </div>
      </ScrollArea>
    </div>
  );
};
```

## 📊 Estrutura de Dados

### **AlertConfig Interface**
```typescript
interface AlertConfig {
  kpiId: string;
  userId: string;
  enabled: boolean;
  warningThreshold?: number;
  criticalThreshold?: number;
  notificationChannels: ('email' | 'dashboard' | 'slack')[];
  createdAt: string;
  updatedAt: string;
}

interface ActiveAlert {
  id: string;
  kpiId: string;
  type: 'warning' | 'critical';
  message: string;
  currentValue: number;
  threshold: number;
  triggeredAt: string;
  isActive: boolean;
}
```

## 🔄 Fluxo de Funcionamento

### **1. Configuração de Alerta:**
1. Usuário clica no botão "Configurar Alerta" no ActionButtons
2. Modal abre com configuração atual (ou padrão)
3. Usuário ajusta thresholds e canais de notificação
4. Sistema salva configuração personalizada
5. Modal fecha e configuração fica ativa

### **2. Exibição de Alerta Ativo:**
1. Sistema avalia KPI contra thresholds personalizados
2. Se threshold ultrapassado, cria alerta ativo
3. Banner aparece no topo do drawer quando aberto
4. Banner mostra: tipo, valor atual, threshold, tempo decorrido
5. Usuário pode dispensar banner (mas alerta continua ativo)

## 🎨 Melhores Práticas Implementadas

### **Radix UI Dialog:**
- ✅ Uso do `@radix-ui/react-dialog` para acessibilidade
- ✅ Controle de foco automático (onOpenAutoFocus/onCloseAutoFocus)
- ✅ Fechamento com ESC key
- ✅ Portal para renderização fora do DOM tree
- ✅ Overlay com backdrop blur

### **Form Validation:**
- ✅ Validação em tempo real com `@radix-ui/react-form`
- ✅ Mensagens de erro contextuais
- ✅ Estados visuais (valid/invalid)
- ✅ Acessibilidade com aria-labels

### **UX Design:**
- ✅ Modal não-intrusivo (não bloqueia workflow)
- ✅ Feedback visual imediato
- ✅ Configuração progressiva (básico → avançado)
- ✅ Consistência com design system existente

## 🧪 Testes Necessários

### **Backend:**
- ✅ Salvar/carregar configuração de alerta
- ✅ Validação de thresholds (warning < critical)
- ✅ Integração com sistema de alertas existente
- ✅ Persistência de configurações por usuário

### **Frontend:**
- ✅ Abertura/fechamento do modal com animações
- ✅ Formulário de configuração responsivo
- ✅ Exibição do banner de alerta com timing correto
- ✅ Integração com ActionButtons
- ✅ Validação de campos em tempo real
- ✅ Estados de loading durante salvamento

### **Integração:**
- ✅ Sincronização entre configuração e alertas ativos
- ✅ Atualização em tempo real do banner
- ✅ Persistência entre sessões
- ✅ Compatibilidade com diferentes tipos de KPI

## 📈 Melhorias Futuras

### **Curto Prazo:**
- Histórico de alertas disparados no drawer
- Snooze de alertas por período determinado
- Configuração de frequência de verificação
- Templates de configuração rápida

### **Médio Prazo:**
- Alertas baseados em tendências (não só valores absolutos)
- Configuração de alertas compostos (múltiplos KPIs)
- Templates de configuração por perfil de usuário
- Notificações push no browser

### **Longo Prazo:**
- Machine learning para sugestão de thresholds
- Alertas contextuais baseados em padrões históricos
- Integração com sistemas externos (Slack, Teams)
- Dashboard de alertas centralizado

## 🎯 Critérios de Sucesso

### **Funcionalidade:**
- ✅ Botão de configuração funcional no drawer
- ✅ Modal de configuração intuitivo e responsivo
- ✅ Banner de alerta aparece quando threshold ultrapassado
- ✅ Configurações são persistidas e aplicadas corretamente
- ✅ Integração perfeita com sistema de alertas existente

### **UX/UI:**
- ✅ Interface consistente com design system do DataHero4
- ✅ Animações suaves e não-intrusivas
- ✅ Feedback visual claro para todas as ações
- ✅ Acessibilidade completa (keyboard navigation, screen readers)
- ✅ Responsividade em diferentes tamanhos de tela

### **Performance:**
- ✅ Carregamento rápido do modal (<200ms)
- ✅ Salvamento de configuração eficiente
- ✅ Atualização em tempo real sem lag
- ✅ Memória otimizada (cleanup adequado)

---

## 📋 Checklist de Implementação

### **Preparação (15min):**
- [ ] Instalar dependências: `@radix-ui/react-dialog`, `@radix-ui/react-form`
- [ ] Criar estrutura de pastas
- [ ] Definir interfaces TypeScript

### **Backend (45min):**
- [ ] Criar modelo `AlertConfig`
- [ ] Implementar endpoints de configuração
- [ ] Integrar com sistema de alertas existente
- [ ] Testes unitários das APIs

### **Frontend - Componentes (60min):**
- [ ] Criar `ActiveAlertBanner` component
- [ ] Criar `AlertConfigModal` component
- [ ] Criar `useAlertConfig` hook
- [ ] Implementar validação de formulário

### **Integração (30min):**
- [ ] Modificar `ActionButtons` para abrir modal
- [ ] Integrar banner no `KPIDrawerContent`
- [ ] Conectar com APIs do backend
- [ ] Testes de integração

### **Polimento (15min):**
- [ ] Ajustar animações e transições
- [ ] Verificar acessibilidade
- [ ] Testes em diferentes browsers
- [ ] Documentação de uso

---

**Tempo Estimado Total: 2h45min**
**Complexidade: Média**
**Prioridade: Alta**
**Dependências: Radix UI, Sistema de Alertas Existente**
