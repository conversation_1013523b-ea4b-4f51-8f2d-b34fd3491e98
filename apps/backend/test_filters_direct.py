#!/usr/bin/env python3
"""Test KPI filters directly"""

from src.tools.db_utils import load_db_config, build_connection_string, get_engine
from sqlalchemy import text
import sys

def test_filters():
    # Connect to database
    db_config = load_db_config(setor="cambio", cliente="L2M")
    connection_string = build_connection_string(db_config)
    engine = get_engine(connection_string)
    
    print("🔍 Testing KPI filters with different timeframes...\n")
    
    with engine.connect() as conn:
        # First, get the date range
        result = conn.execute(text("SELECT MIN(data_operacao), MAX(data_operacao) FROM boleta WHERE data_operacao IS NOT NULL"))
        min_date, max_date = result.fetchone()
        print(f"📅 Data range: {min_date} to {max_date}\n")
        
        # Test total volume with different timeframes (relative to max date)
        timeframes = {
            '1d': "data_operacao >= (SELECT MAX(data_operacao) FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)"
        }
        
        print("📊 Total Volume by Timeframe:")
        for name, filter_sql in timeframes.items():
            query = f'''
                SELECT COALESCE(SUM(valor_me), 0) as volume_total
                FROM boleta
                WHERE valor_me IS NOT NULL
                AND valor_me > 0
                AND ({filter_sql})
            '''
            result = conn.execute(text(query))
            value = result.fetchone()[0]
            print(f"  - {name}: {value:,.2f}")
        
        print("\n📈 Average Spread by Timeframe:")
        for name, filter_sql in timeframes.items():
            query = f'''
                SELECT AVG(
                    CASE
                        WHEN tipo_operacao = 'VENDA' THEN
                            ((taxa_cambio - taxa_base) / taxa_base) * 100
                        WHEN tipo_operacao = 'COMPRA' THEN
                            ((taxa_base - taxa_cambio) / taxa_base) * 100
                        ELSE 0
                    END
                ) as average_spread
                FROM boleta
                WHERE ({filter_sql})
                AND taxa_cambio IS NOT NULL
                AND taxa_base IS NOT NULL
                AND taxa_base > 0
            '''
            result = conn.execute(text(query))
            value = result.fetchone()[0] or 0
            print(f"  - {name}: {value:.4f}%")
        
        # Check data distribution relative to max date
        print("\n📅 Data Distribution (relative to newest data):")
        query = '''
            WITH max_date AS (SELECT MAX(data_operacao) as max_date FROM boleta)
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN b.data_operacao >= m.max_date THEN 1 END) as last_day,
                COUNT(CASE WHEN b.data_operacao >= m.max_date - INTERVAL '7 days' THEN 1 END) as last_week,
                COUNT(CASE WHEN b.data_operacao >= m.max_date - INTERVAL '30 days' THEN 1 END) as last_month
            FROM boleta b, max_date m
            WHERE b.valor_me IS NOT NULL AND b.valor_me > 0
        '''
        result = conn.execute(text(query))
        row = result.fetchone()
        print(f"  - Total records with value: {row[0]}")
        print(f"  - Last day (2025-02-14): {row[1]}")
        print(f"  - Last 7 days: {row[2]}")
        print(f"  - Last 30 days: {row[3]}")

if __name__ == "__main__":
    test_filters() 