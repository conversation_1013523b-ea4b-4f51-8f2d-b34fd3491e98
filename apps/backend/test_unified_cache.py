#!/usr/bin/env python3
"""
Script de teste para o novo sistema de cache unificado.
Demonstra a eficiência e funcionalidades.
"""

import time
import sys
sys.path.insert(0, '.')

from src.caching.unified_cache_system import get_unified_cache
from src.services.kpi_service import get_kpi_service


def test_cache_system():
    """Testa o sistema de cache unificado."""
    print("🧪 Testando Sistema de Cache Unificado\n")
    
    # Obter instância do cache
    cache = get_unified_cache()
    
    # Limpar cache para começar
    cache.clear()
    
    # Teste 1: Cache básico
    print("1️⃣ Teste de Cache Básico")
    print("-" * 40)
    
    # Set e get simples
    cache.set("kpi:value", 12345.67, kpi_id="total_volume", timeframe="week")
    value = cache.get("kpi:value", kpi_id="total_volume", timeframe="week")
    print(f"✅ Valor cacheado: {value}")
    
    # Miss de cache
    miss_value = cache.get("kpi:value", kpi_id="total_volume", timeframe="month")
    print(f"❌ Cache miss (diferente timeframe): {miss_value}")
    
    # Estatísticas
    stats = cache.get_stats()
    print(f"\n📊 Estatísticas: Hits={stats['hits']}, Misses={stats['misses']}, "
          f"Hit Rate={stats['hit_rate']}")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 2: TTL Dinâmico
    print("2️⃣ Teste de TTL Dinâmico")
    print("-" * 40)
    
    # Diferentes timeframes têm diferentes TTLs
    timeframes = ['1d', 'week', 'month', 'quarter']
    
    for tf in timeframes:
        cache.set("kpi:value", f"value_{tf}", kpi_id="test", timeframe=tf)
        # Verificar TTL configurado
        key = cache._generate_cache_key("kpi:value", kpi_id="test", timeframe=tf)
        entry = cache._cache.get(key)
        if entry:
            print(f"Timeframe {tf:8} -> TTL: {entry.ttl}s")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 3: Invalidação Seletiva
    print("3️⃣ Teste de Invalidação Seletiva")
    print("-" * 40)
    
    # Popular cache com múltiplos KPIs
    kpis = ['total_volume', 'average_spread', 'conversion_rate']
    for kpi in kpis:
        for tf in ['1d', 'week']:
            cache.set("kpi:value", f"{kpi}_{tf}", kpi_id=kpi, timeframe=tf)
    
    print(f"Cache populado com {len(cache._cache)} entradas")
    
    # Invalidar específico
    cache.invalidate("kpi:value", kpi_id="total_volume", timeframe="1d")
    print(f"Após invalidar total_volume/1d: {len(cache._cache)} entradas")
    
    # Invalidar por padrão
    cache.invalidate("kpi:value", pattern="average_spread")
    print(f"Após invalidar pattern average_spread: {len(cache._cache)} entradas")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 4: Performance
    print("4️⃣ Teste de Performance")
    print("-" * 40)
    
    # Limpar cache
    cache.clear()
    
    # Teste de escrita
    start = time.time()
    for i in range(1000):
        cache.set("kpi:value", i, kpi_id=f"kpi_{i}", timeframe="week")
    write_time = (time.time() - start) * 1000
    print(f"✍️  1000 escritas em {write_time:.2f}ms ({write_time/1000:.2f}ms por operação)")
    
    # Teste de leitura (hits)
    start = time.time()
    for i in range(1000):
        cache.get("kpi:value", kpi_id=f"kpi_{i}", timeframe="week")
    read_time = (time.time() - start) * 1000
    print(f"📖 1000 leituras (hits) em {read_time:.2f}ms ({read_time/1000:.2f}ms por operação)")
    
    # Estatísticas finais
    final_stats = cache.get_stats()
    print(f"\n📊 Estatísticas Finais:")
    print(f"   - Hit Rate: {final_stats['hit_rate']}")
    print(f"   - Total Requests: {final_stats['total_requests']}")
    print(f"   - Cache Size: {final_stats['size']}/{final_stats['max_size']}")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 5: KPI Service Refatorado
    print("5️⃣ Teste do KPI Service Refatorado")
    print("-" * 40)
    
    try:
        kpi_service = get_kpi_service()
        
        # Primeira chamada (cache miss)
        start = time.time()
        kpis1 = kpi_service.get_dashboard_kpis(
            timeframe="week",
            currency="all"
        )
        time1 = (time.time() - start) * 1000
        print(f"❌ Primeira chamada (cache miss): {len(kpis1)} KPIs em {time1:.2f}ms")
        
        # Segunda chamada (cache hit)
        start = time.time()
        kpis2 = kpi_service.get_dashboard_kpis(
            timeframe="week",
            currency="all"
        )
        time2 = (time.time() - start) * 1000
        print(f"✅ Segunda chamada (cache hit): {len(kpis2)} KPIs em {time2:.2f}ms")
        
        # Speedup
        if time1 > 0:
            speedup = time1 / time2
            print(f"🚀 Speedup: {speedup:.1f}x mais rápido com cache!")
        
        # Estatísticas detalhadas
        detailed_stats = kpi_service.get_cache_stats()
        print(f"\n📊 Estatísticas por Namespace:")
        for ns, stats in detailed_stats.get('by_namespace', {}).items():
            print(f"   - {ns}: {stats['count']} entradas, "
                  f"idade média {stats.get('avg_age', 0):.1f}s")
        
    except Exception as e:
        print(f"⚠️  Erro ao testar KPI Service: {e}")
    
    print("\n✅ Testes concluídos!")


def test_cache_comparison():
    """Compara o sistema antigo com o novo."""
    print("\n\n🔄 COMPARAÇÃO: Sistema Antigo vs Novo")
    print("=" * 60)
    
    # Simular sistema antigo (múltiplas camadas de cache)
    print("\n❌ SISTEMA ANTIGO (Múltiplas camadas):")
    print("- _kpi_cache = {} (cache simples)")
    print("- _cache_metadata = {} (metadados separados)")  
    print("- hierarchical_cache (L1, L2, L3)")
    print("- _cached_chart_data (cache específico)")
    print("- _cached_kpi_value (outro cache)")
    print("- Problemas: Sincronização, duplicação, complexidade")
    
    print("\n✅ SISTEMA NOVO (Unificado):")
    print("- UnifiedCacheSystem (única fonte de verdade)")
    print("- TTL dinâmico por tipo de dado")
    print("- Invalidação seletiva")
    print("- Namespace consistente")
    print("- Thread-safe e eficiente")
    
    print("\n📈 BENEFÍCIOS:")
    print("1. Performance: Menos overhead, acesso direto")
    print("2. Manutenibilidade: Código mais simples")
    print("3. Confiabilidade: Sem problemas de sincronização")
    print("4. Flexibilidade: TTL e invalidação granular")
    print("5. Observabilidade: Métricas centralizadas")


if __name__ == "__main__":
    test_cache_system()
    test_cache_comparison() 