"""
Unit Tests for ProfileDetector - DataHero4 Week 6
=================================================

Comprehensive unit tests for the ProfileDetector component of the hybrid architecture.
Tests profile detection logic, confidence scoring, fail-fast behavior, and analysis patterns.

Features tested:
- Profile detection based on usage patterns
- Confidence scoring and thresholds
- Query pattern analysis
- KPI usage analysis
- Timeframe preference analysis
- Fail-fast validation without fallbacks
- Embedding-based similarity matching

Author: DataHero4 Team
Date: 2025-01-21
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from src.services.profile_detector import ProfileDetector, get_profile_detector
from src.models.learning_models import QueryCache


class TestProfileDetector:
    """Test suite for ProfileDetector component."""

    @pytest.fixture
    def mock_query_cache(self):
        """Mock QueryCache for testing."""
        mock = Mock(spec=QueryCache)
        mock.get_user_query_history.return_value = [
            {
                'query': 'What is the current spread income?',
                'timestamp': datetime.now().isoformat(),
                'kpis_requested': ['spread_income_detailed'],
                'timeframe': 'week'
            },
            {
                'query': 'Show me operational margins',
                'timestamp': (datetime.now() - timedelta(days=1)).isoformat(),
                'kpis_requested': ['margem_liquida_operacional'],
                'timeframe': 'month'
            }
        ]
        mock.store_query.return_value = True
        return mock

    @pytest.fixture
    def detector(self, mock_query_cache):
        """Create ProfileDetector with mocked dependencies."""
        with patch('src.services.profile_detector.get_query_cache', return_value=mock_query_cache):
            return ProfileDetector()

    def test_detector_initialization(self, detector):
        """Test ProfileDetector initialization."""
        assert detector is not None
        assert hasattr(detector, 'query_cache')
        assert detector.confidence_threshold == 0.30

    @pytest.mark.parametrize("query_patterns,expected_profile,expected_confidence", [
        # CEO patterns - strategic queries
        (
            [
                "What is our overall performance this quarter?",
                "Show me high-level KPIs",
                "Strategic overview of spread income"
            ],
            "CEO",
            0.85
        ),
        # CFO patterns - financial analysis
        (
            [
                "What are our operational costs?",
                "Show me detailed margin analysis",
                "Cost per transaction breakdown"
            ],
            "CFO",
            0.80
        ),
        # Risk Manager patterns - real-time monitoring
        (
            [
                "Current processing times",
                "Real-time transaction monitoring",
                "Alert on processing delays"
            ],
            "Risk_Manager",
            0.75
        ),
        # Trader patterns - fast updates
        (
            [
                "Latest spread rates",
                "Real-time income data",
                "Quick market updates"
            ],
            "Trader",
            0.70
        ),
        # Operations patterns - efficiency focus
        (
            [
                "Transaction processing efficiency",
                "Operational cost analysis",
                "Process optimization metrics"
            ],
            "Operations",
            0.65
        )
    ])
    def test_detect_profile_by_query_patterns(self, detector, mock_query_cache, query_patterns, expected_profile, expected_confidence):
        """Test profile detection based on query patterns."""
        # Setup mock query history
        mock_history = []
        for i, query in enumerate(query_patterns):
            mock_history.append({
                'query': query,
                'timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
                'kpis_requested': ['test_kpi'],
                'timeframe': 'week'
            })
        
        mock_query_cache.get_user_query_history.return_value = mock_history

        result = detector.detect_profile("test_user", analysis_days=7)

        assert result is not None
        assert result['detected_profile'] == expected_profile
        assert result['confidence'] >= expected_confidence - 0.1  # Allow small variance
        assert 'analysis' in result
        assert 'reason' in result

    @pytest.mark.parametrize("kpi_usage,expected_profile", [
        # CEO KPIs
        ({'spread_income_detailed': 10, 'margem_liquida_operacional': 8}, "CEO"),
        # CFO KPIs
        ({'margem_liquida_operacional': 12, 'custo_por_transacao': 9}, "CFO"),
        # Risk Manager KPIs
        ({'tempo_processamento_medio': 15}, "Risk_Manager"),
        # Trader KPIs
        ({'spread_income_detailed': 20, 'tempo_processamento_medio': 5}, "Trader"),
        # Operations KPIs
        ({'custo_por_transacao': 10, 'tempo_processamento_medio': 8}, "Operations")
    ])
    def test_detect_profile_by_kpi_usage(self, detector, mock_query_cache, kpi_usage, expected_profile):
        """Test profile detection based on KPI usage patterns."""
        # Create mock history based on KPI usage
        mock_history = []
        for kpi_id, count in kpi_usage.items():
            for i in range(count):
                mock_history.append({
                    'query': f'Show me {kpi_id}',
                    'timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
                    'kpis_requested': [kpi_id],
                    'timeframe': 'week'
                })
        
        mock_query_cache.get_user_query_history.return_value = mock_history

        result = detector.detect_profile("test_user", analysis_days=30)

        assert result is not None
        assert result['detected_profile'] == expected_profile
        assert result['confidence'] >= 0.30

    @pytest.mark.parametrize("timeframe_preferences,expected_profile", [
        # CEO - longer timeframes
        ({'quarter': 5, 'month': 8, 'week': 2}, "CEO"),
        # CFO - mixed timeframes
        ({'month': 10, 'week': 5, 'quarter': 3}, "CFO"),
        # Risk Manager - short timeframes
        ({'hour': 15, 'day': 8, 'week': 2}, "Risk_Manager"),
        # Trader - very short timeframes
        ({'minute': 20, 'hour': 10, 'day': 3}, "Trader"),
        # Operations - operational timeframes
        ({'day': 12, 'week': 8, 'month': 3}, "Operations")
    ])
    def test_detect_profile_by_timeframe_preferences(self, detector, mock_query_cache, timeframe_preferences, expected_profile):
        """Test profile detection based on timeframe preferences."""
        # Create mock history based on timeframe preferences
        mock_history = []
        for timeframe, count in timeframe_preferences.items():
            for i in range(count):
                mock_history.append({
                    'query': f'Show me data for {timeframe}',
                    'timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
                    'kpis_requested': ['test_kpi'],
                    'timeframe': timeframe
                })
        
        mock_query_cache.get_user_query_history.return_value = mock_history

        result = detector.detect_profile("test_user", analysis_days=30)

        assert result is not None
        assert result['detected_profile'] == expected_profile

    def test_detect_profile_insufficient_data(self, detector, mock_query_cache):
        """Test profile detection with insufficient data."""
        # Setup minimal query history
        mock_query_cache.get_user_query_history.return_value = [
            {
                'query': 'test query',
                'timestamp': datetime.now().isoformat(),
                'kpis_requested': ['test_kpi'],
                'timeframe': 'week'
            }
        ]

        result = detector.detect_profile("test_user", analysis_days=30)

        assert result is not None
        assert result['confidence'] < 0.30  # Below threshold
        assert result['detected_profile'] is None or result['detected_profile'] == "Operations"  # Default

    def test_detect_profile_no_history(self, detector, mock_query_cache):
        """Test profile detection with no query history."""
        mock_query_cache.get_user_query_history.return_value = []

        result = detector.detect_profile("test_user", analysis_days=30)

        assert result is not None
        assert result['confidence'] == 0.0
        assert result['detected_profile'] is None
        assert result['reason'] == 'no_query_history'

    def test_detect_profile_fail_fast_validation(self, detector):
        """Test fail-fast validation for invalid parameters."""
        # Test empty user_id
        with pytest.raises(ValueError, match="user_id cannot be empty"):
            detector.detect_profile("", analysis_days=30)

        # Test invalid analysis_days
        with pytest.raises(ValueError, match="analysis_days must be positive"):
            detector.detect_profile("test_user", analysis_days=0)

        with pytest.raises(ValueError, match="analysis_days must be positive"):
            detector.detect_profile("test_user", analysis_days=-5)

    def test_analyze_query_patterns(self, detector):
        """Test query pattern analysis."""
        queries = [
            "What is our strategic performance?",
            "Show me high-level overview",
            "Executive dashboard summary"
        ]

        patterns = detector._analyze_query_patterns(queries)

        assert patterns is not None
        assert 'strategic_keywords' in patterns
        assert 'complexity_score' in patterns
        assert 'executive_focus' in patterns

    def test_analyze_kpi_usage(self, detector):
        """Test KPI usage analysis."""
        kpi_requests = [
            ['spread_income_detailed', 'margem_liquida_operacional'],
            ['spread_income_detailed'],
            ['margem_liquida_operacional', 'custo_por_transacao']
        ]

        usage = detector._analyze_kpi_usage(kpi_requests)

        assert usage is not None
        assert 'spread_income_detailed' in usage
        assert 'margem_liquida_operacional' in usage
        assert usage['spread_income_detailed'] == 2
        assert usage['margem_liquida_operacional'] == 2

    def test_analyze_timeframe_preferences(self, detector):
        """Test timeframe preference analysis."""
        timeframes = ['week', 'month', 'week', 'quarter', 'week']

        preferences = detector._analyze_timeframe_preferences(timeframes)

        assert preferences is not None
        assert 'week' in preferences
        assert 'month' in preferences
        assert preferences['week'] == 3
        assert preferences['month'] == 1

    def test_calculate_profile_confidence(self, detector):
        """Test profile confidence calculation."""
        analysis = {
            'query_patterns': {'strategic_keywords': 5, 'executive_focus': True},
            'kpi_usage': {'spread_income_detailed': 10, 'margem_liquida_operacional': 8},
            'timeframe_preferences': {'month': 8, 'quarter': 5},
            'behavior_score': 0.85
        }

        confidence = detector._calculate_profile_confidence("CEO", analysis)

        assert confidence is not None
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.5  # Should be high for CEO pattern

    def test_get_profile_recommendations(self, detector):
        """Test profile recommendations generation."""
        analysis = {
            'query_patterns': {'strategic_keywords': 3},
            'kpi_usage': {'spread_income_detailed': 5},
            'timeframe_preferences': {'week': 10},
            'behavior_score': 0.70
        }

        recommendations = detector._get_profile_recommendations("CEO", analysis)

        assert recommendations is not None
        assert 'suggested_kpis' in recommendations
        assert 'cache_strategy' in recommendations
        assert 'alternative_profiles' in recommendations

    def test_singleton_pattern(self):
        """Test ProfileDetector singleton pattern."""
        detector1 = get_profile_detector()
        detector2 = get_profile_detector()
        assert detector1 is detector2

    def test_embedding_similarity_matching(self, detector):
        """Test embedding-based similarity matching for queries."""
        with patch.object(detector, '_get_query_embedding') as mock_embedding:
            mock_embedding.return_value = [0.1, 0.2, 0.3, 0.4, 0.5]  # Mock embedding vector

            similarity = detector._calculate_query_similarity(
                "What is our spread income?",
                "Show me current spread revenue"
            )

            assert similarity is not None
            assert 0.0 <= similarity <= 1.0

    def test_profile_detection_caching(self, detector, mock_query_cache):
        """Test caching of profile detection results."""
        # First call
        result1 = detector.detect_profile("test_user", analysis_days=30)
        
        # Second call should use cached result
        result2 = detector.detect_profile("test_user", analysis_days=30)

        assert result1 == result2
        # Query cache should only be called once due to caching
        assert mock_query_cache.get_user_query_history.call_count <= 2

    def test_error_handling_and_logging(self, detector, mock_query_cache, caplog):
        """Test error handling and logging in detector."""
        # Setup query cache to raise exception
        mock_query_cache.get_user_query_history.side_effect = Exception("Database error")

        result = detector.detect_profile("test_user", analysis_days=30)

        # Should handle error gracefully
        assert result is not None
        assert result['confidence'] == 0.0
        assert result['detected_profile'] is None
        assert "Database error" in caplog.text

    @pytest.mark.parametrize("user_id,analysis_days,expected_calls", [
        ("user1", 7, 1),
        ("user2", 30, 1),
        ("user3", 90, 1)
    ])
    def test_detection_with_different_parameters(self, detector, mock_query_cache, user_id, analysis_days, expected_calls):
        """Test detection with different parameter combinations."""
        detector.detect_profile(user_id, analysis_days)
        
        assert mock_query_cache.get_user_query_history.call_count == expected_calls
        call_args = mock_query_cache.get_user_query_history.call_args
        assert call_args[0][0] == user_id  # user_id parameter
        assert call_args[1]['days'] == analysis_days  # analysis_days parameter
