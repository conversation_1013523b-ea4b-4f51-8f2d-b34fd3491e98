"""
Unit Tests for PersonalizedCacheSystem - DataHero4 Week 6
=========================================================

Comprehensive unit tests for the PersonalizedCacheSystem component of the hybrid architecture.
Tests profile-aware caching, TTL management, cache hit/miss patterns, and performance metrics.

Features tested:
- Profile-aware cache TTL strategies
- Cache hit/miss patterns by profile
- Namespace-based cache organization
- Cache invalidation and cleanup
- Performance metrics collection
- Concurrent access patterns
- Memory usage optimization

Author: DataHero4 Team
Date: 2025-01-21
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from src.caching.personalized_cache_system import PersonalizedCacheSystem, get_personalized_cache


class TestPersonalizedCacheSystem:
    """Test suite for PersonalizedCacheSystem component."""

    @pytest.fixture
    def cache_system(self):
        """Create PersonalizedCacheSystem instance for testing."""
        return PersonalizedCacheSystem()

    @pytest.fixture
    def sample_data(self):
        """Sample data for cache testing."""
        return {
            'kpi_id': 'spread_income_detailed',
            'value': 1500.75,
            'timestamp': datetime.now().isoformat(),
            'source': 'test'
        }

    def test_cache_system_initialization(self, cache_system):
        """Test PersonalizedCacheSystem initialization."""
        assert cache_system is not None
        assert hasattr(cache_system, '_cache')
        assert hasattr(cache_system, '_profile_ttl_map')
        assert hasattr(cache_system, '_metrics')

    @pytest.mark.parametrize("profile_type,expected_ttl", [
        ("CEO", 3600),
        ("CFO", 1800),
        ("Risk_Manager", 300),
        ("Trader", 60),
        ("Operations", 900),
        ("Unknown", 600)  # Default TTL
    ])
    def test_get_profile_ttl(self, cache_system, profile_type, expected_ttl):
        """Test profile-specific TTL calculation."""
        ttl = cache_system._get_profile_ttl(profile_type)
        assert ttl == expected_ttl

    def test_set_and_get_personalized_cache(self, cache_system, sample_data):
        """Test basic set and get operations."""
        namespace = "test:kpi"
        user_id = "test_user"
        profile_type = "CEO"

        # Set data
        result = cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type
        )
        assert result is True

        # Get data
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is not None
        assert cached_data['kpi_id'] == sample_data['kpi_id']
        assert cached_data['value'] == sample_data['value']

    def test_cache_miss(self, cache_system):
        """Test cache miss scenario."""
        cached_data = cache_system.get_personalized(
            namespace="nonexistent:namespace",
            user_id="nonexistent_user",
            profile_type="CEO"
        )
        assert cached_data is None

    def test_cache_ttl_expiration(self, cache_system, sample_data):
        """Test cache TTL expiration."""
        namespace = "test:ttl"
        user_id = "test_user"
        profile_type = "Trader"  # Short TTL (60 seconds)

        # Set data with short TTL
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type,
            ttl=1  # 1 second TTL for testing
        )

        # Immediately get data (should hit)
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is not None

        # Wait for TTL expiration
        time.sleep(1.1)

        # Get data after expiration (should miss)
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is None

    def test_cache_invalidation_specific_key(self, cache_system, sample_data):
        """Test cache invalidation for specific key."""
        namespace = "test:invalidation"
        user_id = "test_user"
        profile_type = "CEO"

        # Set data
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type
        )

        # Verify data exists
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is not None

        # Invalidate specific key
        result = cache_system.invalidate_user_cache(user_id, namespace)
        assert result is True

        # Verify data is gone
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is None

    def test_cache_invalidation_all_user_data(self, cache_system, sample_data):
        """Test cache invalidation for all user data."""
        user_id = "test_user"
        profile_type = "CFO"

        # Set multiple data entries for user
        namespaces = ["kpi:spread", "kpi:margin", "kpi:cost"]
        for namespace in namespaces:
            cache_system.set_personalized(
                namespace=namespace,
                user_id=user_id,
                value=sample_data,
                profile_type=profile_type
            )

        # Verify all data exists
        for namespace in namespaces:
            cached_data = cache_system.get_personalized(
                namespace=namespace,
                user_id=user_id,
                profile_type=profile_type
            )
            assert cached_data is not None

        # Invalidate all user data
        result = cache_system.invalidate_user_cache(user_id)
        assert result is True

        # Verify all data is gone
        for namespace in namespaces:
            cached_data = cache_system.get_personalized(
                namespace=namespace,
                user_id=user_id,
                profile_type=profile_type
            )
            assert cached_data is None

    def test_cache_metrics_collection(self, cache_system, sample_data):
        """Test cache metrics collection."""
        namespace = "test:metrics"
        user_id = "test_user"
        profile_type = "Operations"

        # Initial metrics
        initial_metrics = cache_system.get_cache_metrics()
        assert 'hits' in initial_metrics
        assert 'misses' in initial_metrics
        assert 'sets' in initial_metrics

        # Cache miss
        cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )

        # Cache set
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type
        )

        # Cache hit
        cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )

        # Check updated metrics
        final_metrics = cache_system.get_cache_metrics()
        assert final_metrics['misses'] == initial_metrics['misses'] + 1
        assert final_metrics['sets'] == initial_metrics['sets'] + 1
        assert final_metrics['hits'] == initial_metrics['hits'] + 1

    def test_cache_hit_rate_calculation(self, cache_system, sample_data):
        """Test cache hit rate calculation."""
        namespace = "test:hitrate"
        user_id = "test_user"
        profile_type = "Risk_Manager"

        # Set data
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type
        )

        # Generate hits and misses
        for i in range(5):
            # Hit
            cache_system.get_personalized(
                namespace=namespace,
                user_id=user_id,
                profile_type=profile_type
            )
            # Miss
            cache_system.get_personalized(
                namespace=f"miss:{i}",
                user_id=user_id,
                profile_type=profile_type
            )

        metrics = cache_system.get_cache_metrics()
        hit_rate = cache_system.get_hit_rate()
        
        expected_hit_rate = metrics['hits'] / (metrics['hits'] + metrics['misses'])
        assert abs(hit_rate - expected_hit_rate) < 0.01  # Allow small floating point variance

    @pytest.mark.parametrize("profile_type,expected_priority", [
        ("CEO", "high"),
        ("CFO", "high"),
        ("Risk_Manager", "critical"),
        ("Trader", "critical"),
        ("Operations", "medium")
    ])
    def test_cache_priority_by_profile(self, cache_system, profile_type, expected_priority):
        """Test cache priority assignment by profile."""
        priority = cache_system._get_cache_priority(profile_type)
        assert priority == expected_priority

    def test_cache_namespace_organization(self, cache_system, sample_data):
        """Test cache organization by namespace."""
        user_id = "test_user"
        profile_type = "CEO"

        # Set data in different namespaces
        namespaces = {
            "kpi:spread": {"type": "kpi", "id": "spread_income"},
            "bcb:usd_brl": {"type": "bcb", "rate": 5.25},
            "profile:config": {"type": "profile", "settings": {}}
        }

        for namespace, data in namespaces.items():
            cache_system.set_personalized(
                namespace=namespace,
                user_id=user_id,
                value=data,
                profile_type=profile_type
            )

        # Verify namespace separation
        for namespace, expected_data in namespaces.items():
            cached_data = cache_system.get_personalized(
                namespace=namespace,
                user_id=user_id,
                profile_type=profile_type
            )
            assert cached_data is not None
            assert cached_data['type'] == expected_data['type']

    def test_concurrent_cache_access(self, cache_system, sample_data):
        """Test concurrent cache access patterns."""
        import threading
        import queue

        namespace = "test:concurrent"
        profile_type = "Trader"
        results = queue.Queue()

        def cache_worker(user_id, data):
            try:
                # Set data
                cache_system.set_personalized(
                    namespace=namespace,
                    user_id=user_id,
                    value=data,
                    profile_type=profile_type
                )
                
                # Get data
                cached_data = cache_system.get_personalized(
                    namespace=namespace,
                    user_id=user_id,
                    profile_type=profile_type
                )
                
                results.put(('success', user_id, cached_data is not None))
            except Exception as e:
                results.put(('error', user_id, str(e)))

        # Create multiple threads
        threads = []
        for i in range(10):
            user_id = f"user_{i}"
            data = {**sample_data, 'user_id': user_id}
            thread = threading.Thread(target=cache_worker, args=(user_id, data))
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # Check results
        success_count = 0
        while not results.empty():
            status, user_id, success = results.get()
            if status == 'success' and success:
                success_count += 1

        assert success_count == 10  # All operations should succeed

    def test_cache_memory_usage_tracking(self, cache_system):
        """Test cache memory usage tracking."""
        initial_size = cache_system.get_cache_size()
        
        # Add data to cache
        for i in range(100):
            cache_system.set_personalized(
                namespace=f"test:memory:{i}",
                user_id=f"user_{i}",
                value={'data': f'test_data_{i}' * 100},  # Larger data
                profile_type="CEO"
            )

        final_size = cache_system.get_cache_size()
        assert final_size > initial_size

    def test_cache_cleanup_expired_entries(self, cache_system, sample_data):
        """Test cleanup of expired cache entries."""
        namespace = "test:cleanup"
        user_id = "test_user"
        profile_type = "Trader"

        # Set data with very short TTL
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type,
            ttl=1  # 1 second
        )

        # Wait for expiration
        time.sleep(1.1)

        # Trigger cleanup
        cleaned_count = cache_system.cleanup_expired()
        assert cleaned_count >= 0  # Should clean up at least the expired entry

    def test_singleton_pattern(self):
        """Test PersonalizedCacheSystem singleton pattern."""
        cache1 = get_personalized_cache()
        cache2 = get_personalized_cache()
        assert cache1 is cache2

    def test_cache_key_generation(self, cache_system):
        """Test cache key generation consistency."""
        namespace = "test:key"
        user_id = "test_user"
        profile_type = "CEO"

        key1 = cache_system._generate_cache_key(namespace, user_id, profile_type)
        key2 = cache_system._generate_cache_key(namespace, user_id, profile_type)
        
        assert key1 == key2
        assert user_id in key1
        assert namespace in key1

    def test_cache_profile_aware_ttl_override(self, cache_system, sample_data):
        """Test TTL override with profile-aware defaults."""
        namespace = "test:ttl_override"
        user_id = "test_user"
        profile_type = "CEO"  # Default TTL: 3600

        # Set with custom TTL
        custom_ttl = 120
        cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=sample_data,
            profile_type=profile_type,
            ttl=custom_ttl
        )

        # Verify custom TTL is used (we can't directly test TTL, but we can verify the data is set)
        cached_data = cache_system.get_personalized(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type
        )
        assert cached_data is not None

    def test_error_handling_invalid_data(self, cache_system):
        """Test error handling for invalid data."""
        namespace = "test:error"
        user_id = "test_user"
        profile_type = "CEO"

        # Test with None value
        result = cache_system.set_personalized(
            namespace=namespace,
            user_id=user_id,
            value=None,
            profile_type=profile_type
        )
        # Should handle None gracefully
        assert result is True or result is False

        # Test with empty namespace
        with pytest.raises(ValueError):
            cache_system.set_personalized(
                namespace="",
                user_id=user_id,
                value={'test': 'data'},
                profile_type=profile_type
            )

        # Test with empty user_id
        with pytest.raises(ValueError):
            cache_system.set_personalized(
                namespace=namespace,
                user_id="",
                value={'test': 'data'},
                profile_type=profile_type
            )
