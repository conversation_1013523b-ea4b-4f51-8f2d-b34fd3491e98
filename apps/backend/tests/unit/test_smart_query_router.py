"""
Unit Tests for SmartQueryRouter - DataHero4 Week 6
==================================================

Comprehensive unit tests for the SmartQueryRouter component of the hybrid architecture.
Tests routing logic, profile-aware caching, fail-fast behavior, and KPI routing.

Features tested:
- Multi-layer routing (snapshot, cache, direct)
- Profile-aware routing strategies
- Fail-fast validation without fallbacks
- KPI routing with profile optimization
- BCB API integration
- Cache hit/miss patterns
- Error handling and recovery

Author: DataHero4 Team
Date: 2025-01-21
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from src.services.smart_query_router import SmartQueryRouter, get_smart_query_router
from src.services.profile_aware_snapshot_service import ProfileAwareSnapshotService
from src.caching.personalized_cache_system import PersonalizedCacheSystem
from src.services.profile_detector import ProfileDetector
from src.services.banco_central_api import BancoCentralAPI


class TestSmartQueryRouter:
    """Test suite for SmartQueryRouter component."""

    @pytest.fixture
    def mock_snapshot_service(self):
        """Mock ProfileAwareSnapshotService."""
        mock = Mock(spec=ProfileAwareSnapshotService)
        mock.get_profile_snapshot.return_value = {
            'kpi_id': 'test_kpi',
            'value': 1000.0,
            'source': 'snapshot',
            'timestamp': datetime.now().isoformat()
        }
        return mock

    @pytest.fixture
    def mock_cache_system(self):
        """Mock PersonalizedCacheSystem."""
        mock = Mock(spec=PersonalizedCacheSystem)
        mock.get_personalized.return_value = None  # Cache miss by default
        mock.set_personalized.return_value = True
        mock.invalidate_user_cache.return_value = True
        return mock

    @pytest.fixture
    def mock_profile_detector(self):
        """Mock ProfileDetector."""
        mock = Mock(spec=ProfileDetector)
        mock.detect_profile.return_value = {
            'detected_profile': 'CEO',
            'confidence': 0.85,
            'reason': 'strategic_queries',
            'analysis': {}
        }
        return mock

    @pytest.fixture
    def mock_bcb_api(self):
        """Mock BancoCentralAPI."""
        mock = Mock(spec=BancoCentralAPI)
        mock.get_market_data_summary.return_value = {
            'usd_brl_quotation': {'rate': 5.25, 'date': '21/01/2025'},
            'selic_rate': {'rate': 10.75, 'date': '21/01/2025'}
        }
        mock.health_check.return_value = {'status': 'healthy'}
        return mock

    @pytest.fixture
    def router(self, mock_snapshot_service, mock_cache_system, mock_profile_detector, mock_bcb_api):
        """Create SmartQueryRouter with mocked dependencies."""
        with patch('src.services.smart_query_router.ProfileAwareSnapshotService', return_value=mock_snapshot_service), \
             patch('src.services.smart_query_router.get_personalized_cache', return_value=mock_cache_system), \
             patch('src.services.smart_query_router.get_profile_detector', return_value=mock_profile_detector), \
             patch('src.services.smart_query_router.get_banco_central_api', return_value=mock_bcb_api):
            return SmartQueryRouter()

    def test_router_initialization(self, router):
        """Test SmartQueryRouter initialization."""
        assert router is not None
        assert hasattr(router, 'snapshot_service')
        assert hasattr(router, 'cache_system')
        assert hasattr(router, 'profile_detector')
        assert hasattr(router, 'bcb_api')

    @pytest.mark.parametrize("profile_type,expected_strategy", [
        ("CEO", "snapshot"),
        ("CFO", "snapshot"),
        ("Risk_Manager", "direct"),
        ("Trader", "cache"),
        ("Operations", "cache")
    ])
    def test_get_routing_strategy(self, router, profile_type, expected_strategy):
        """Test routing strategy selection based on profile."""
        strategy = router._get_routing_strategy(profile_type)
        assert strategy == expected_strategy

    @pytest.mark.parametrize("profile_type,expected_ttl", [
        ("CEO", 3600),
        ("CFO", 1800),
        ("Risk_Manager", 300),
        ("Trader", 60),
        ("Operations", 900)
    ])
    def test_get_cache_ttl(self, router, profile_type, expected_ttl):
        """Test cache TTL calculation based on profile."""
        ttl = router._get_cache_ttl(profile_type)
        assert ttl == expected_ttl

    def test_route_kpi_request_snapshot_layer(self, router, mock_snapshot_service):
        """Test KPI routing through snapshot layer (CEO/CFO profiles)."""
        result = router.route_kpi_request(
            kpi_id="spread_income_detailed",
            client_id="L2M",
            user_id="ceo_user",
            timeframe="week",
            currency="BRL",
            profile_type="CEO"
        )

        assert result is not None
        assert result.get('source') == 'snapshot'
        assert 'routing_metadata' in result
        mock_snapshot_service.get_profile_snapshot.assert_called_once()

    def test_route_kpi_request_cache_layer(self, router, mock_cache_system):
        """Test KPI routing through cache layer (Trader/Operations profiles)."""
        # Setup cache hit
        mock_cache_system.get_personalized.return_value = {
            'kpi_id': 'spread_income_detailed',
            'value': 2000.0,
            'source': 'cache',
            'timestamp': datetime.now().isoformat()
        }

        result = router.route_kpi_request(
            kpi_id="spread_income_detailed",
            client_id="L2M",
            user_id="trader_user",
            timeframe="week",
            currency="BRL",
            profile_type="Trader"
        )

        assert result is not None
        assert result.get('source') == 'cache'
        assert result.get('cache_hit') is True
        mock_cache_system.get_personalized.assert_called()

    def test_route_kpi_request_direct_layer(self, router):
        """Test KPI routing through direct layer (Risk_Manager profile)."""
        with patch.object(router, '_execute_direct_query') as mock_direct:
            mock_direct.return_value = {
                'kpi_id': 'tempo_processamento_medio',
                'value': 1.5,
                'source': 'direct',
                'timestamp': datetime.now().isoformat()
            }

            result = router.route_kpi_request(
                kpi_id="tempo_processamento_medio",
                client_id="L2M",
                user_id="risk_user",
                timeframe="week",
                currency="BRL",
                profile_type="Risk_Manager"
            )

            assert result is not None
            assert result.get('source') == 'direct'
            mock_direct.assert_called_once()

    def test_route_kpi_request_fail_fast_validation(self, router):
        """Test fail-fast validation for invalid parameters."""
        # Test missing required parameters
        with pytest.raises(ValueError, match="kpi_id is required"):
            router.route_kpi_request(
                kpi_id="",
                client_id="L2M",
                user_id="test_user",
                profile_type="CEO"
            )

        with pytest.raises(ValueError, match="client_id is required"):
            router.route_kpi_request(
                kpi_id="test_kpi",
                client_id="",
                user_id="test_user",
                profile_type="CEO"
            )

        with pytest.raises(ValueError, match="user_id is required"):
            router.route_kpi_request(
                kpi_id="test_kpi",
                client_id="L2M",
                user_id="",
                profile_type="CEO"
            )

    def test_route_kpi_request_invalid_profile(self, router):
        """Test routing with invalid profile type."""
        result = router.route_kpi_request(
            kpi_id="test_kpi",
            client_id="L2M",
            user_id="test_user",
            profile_type="INVALID_PROFILE"
        )

        # Should default to cache strategy
        assert result is not None
        assert 'routing_metadata' in result
        assert result['routing_metadata']['strategy'] == 'cache'

    def test_get_bcb_market_data_cache_hit(self, router, mock_cache_system):
        """Test BCB market data with cache hit."""
        # Setup cache hit
        cached_data = {
            'usd_brl_quotation': {'rate': 5.20, 'date': '20/01/2025'},
            'selic_rate': {'rate': 10.50, 'date': '20/01/2025'}
        }
        mock_cache_system.get_personalized.return_value = cached_data

        result = router.get_bcb_market_data(
            user_id="test_user",
            profile_type="CEO"
        )

        assert result is not None
        assert result.get('cache_hit') is True
        assert result.get('source') == 'cache'
        assert 'usd_brl_quotation' in result

    def test_get_bcb_market_data_cache_miss(self, router, mock_cache_system, mock_bcb_api):
        """Test BCB market data with cache miss."""
        # Setup cache miss
        mock_cache_system.get_personalized.return_value = None

        result = router.get_bcb_market_data(
            user_id="test_user",
            profile_type="CFO"
        )

        assert result is not None
        assert result.get('cache_hit') is False
        assert result.get('source') == 'bcb_api'
        mock_bcb_api.get_market_data_summary.assert_called_once()
        mock_cache_system.set_personalized.assert_called()

    def test_get_bcb_market_data_api_failure(self, router, mock_cache_system, mock_bcb_api):
        """Test BCB market data with API failure (fail-fast)."""
        # Setup cache miss and API failure
        mock_cache_system.get_personalized.return_value = None
        mock_bcb_api.get_market_data_summary.side_effect = Exception("BCB API unavailable")

        result = router.get_bcb_market_data(
            user_id="test_user",
            profile_type="Trader"
        )

        assert result is not None
        assert result.get('error') == 'bcb_unavailable'
        assert result.get('fail_fast') is True
        assert 'message' in result

    def test_get_routing_stats(self, router):
        """Test routing statistics collection."""
        stats = router.get_routing_stats()

        assert stats is not None
        assert 'routing_strategies' in stats
        assert 'cache_strategies' in stats
        assert 'profile_mappings' in stats
        assert len(stats['routing_strategies']) == 3  # snapshot, cache, direct

    def test_invalidate_user_data(self, router, mock_cache_system):
        """Test user data invalidation."""
        router.invalidate_user_data("test_user", "test_kpi")
        mock_cache_system.invalidate_user_cache.assert_called_with("test_user", "kpi:value:kpi_id:test_kpi")

        router.invalidate_user_data("test_user")
        mock_cache_system.invalidate_user_cache.assert_called_with("test_user")

    def test_singleton_pattern(self):
        """Test SmartQueryRouter singleton pattern."""
        router1 = get_smart_query_router()
        router2 = get_smart_query_router()
        assert router1 is router2

    @pytest.mark.parametrize("kpi_id,profile_type,expected_calls", [
        ("spread_income_detailed", "CEO", 1),
        ("margem_liquida_operacional", "CFO", 1),
        ("tempo_processamento_medio", "Risk_Manager", 1),
        ("custo_por_transacao", "Operations", 1)
    ])
    def test_profile_kpi_routing_patterns(self, router, kpi_id, profile_type, expected_calls):
        """Test routing patterns for different KPI-profile combinations."""
        with patch.object(router, '_route_to_appropriate_layer') as mock_route:
            mock_route.return_value = {'kpi_id': kpi_id, 'value': 100.0}

            router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id="test_user",
                profile_type=profile_type
            )

            assert mock_route.call_count == expected_calls

    def test_concurrent_routing_requests(self, router):
        """Test concurrent KPI routing requests."""
        async def make_request(user_id, profile_type):
            return router.route_kpi_request(
                kpi_id="spread_income_detailed",
                client_id="L2M",
                user_id=user_id,
                profile_type=profile_type
            )

        # Simulate concurrent requests
        tasks = [
            make_request("user1", "CEO"),
            make_request("user2", "CFO"),
            make_request("user3", "Trader")
        ]

        # Note: Since router methods are not async, we test thread safety
        results = []
        for task in tasks:
            result = task
            results.append(result)

        assert len(results) == 3
        assert all(result is not None for result in results)

    def test_error_handling_and_logging(self, router, caplog):
        """Test error handling and logging in router."""
        with patch.object(router.snapshot_service, 'get_profile_snapshot', side_effect=Exception("Snapshot error")):
            result = router.route_kpi_request(
                kpi_id="test_kpi",
                client_id="L2M",
                user_id="test_user",
                profile_type="CEO"
            )

            # Should handle error gracefully and log it
            assert "error" in result
            assert "Snapshot error" in caplog.text
