"""
Performance Tests for Hybrid Architecture - DataHero4 Week 6
============================================================

Comprehensive performance tests for the hybrid architecture with specific metrics validation.
Tests performance targets, cache hit rates, response times, and concurrent user scenarios.

Performance Targets:
- Cache hit rate >80% for cache-optimized profiles
- Response time <500ms for snapshot layer
- Response time <100ms for cache layer (hits)
- Response time <1000ms for direct layer
- Concurrent users >95% success rate
- Memory usage within acceptable limits

Author: DataHero4 Team
Date: 2025-01-21
"""

import pytest
import time
import statistics
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta

from src.services.smart_query_router import get_smart_query_router
from src.services.hybrid_kpi_service import get_hybrid_kpi_service
from src.caching.personalized_cache_system import get_personalized_cache


class PerformanceMetrics:
    """Helper class to collect and analyze performance metrics."""
    
    def __init__(self):
        self.response_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        self.errors = 0
        self.start_time = None
        self.end_time = None
    
    def start_timing(self):
        self.start_time = time.time()
    
    def end_timing(self):
        self.end_time = time.time()
    
    def add_response_time(self, response_time_ms):
        self.response_times.append(response_time_ms)
    
    def add_cache_hit(self):
        self.cache_hits += 1
    
    def add_cache_miss(self):
        self.cache_misses += 1
    
    def add_error(self):
        self.errors += 1
    
    def get_cache_hit_rate(self):
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0
    
    def get_avg_response_time(self):
        return statistics.mean(self.response_times) if self.response_times else 0.0
    
    def get_p95_response_time(self):
        return statistics.quantiles(self.response_times, n=20)[18] if len(self.response_times) >= 20 else max(self.response_times) if self.response_times else 0.0
    
    def get_success_rate(self):
        total = len(self.response_times) + self.errors
        return len(self.response_times) / total if total > 0 else 0.0
    
    def get_total_duration(self):
        return (self.end_time - self.start_time) if self.start_time and self.end_time else 0.0


class TestHybridArchitecturePerformance:
    """Performance test suite for hybrid architecture."""

    @pytest.fixture(scope="class")
    def router(self):
        """Get SmartQueryRouter instance."""
        return get_smart_query_router()

    @pytest.fixture(scope="class")
    def hybrid_service(self):
        """Get HybridKpiService instance."""
        return get_hybrid_kpi_service()

    @pytest.fixture(scope="class")
    def cache_system(self):
        """Get PersonalizedCacheSystem instance."""
        return get_personalized_cache()

    @pytest.fixture
    def performance_profiles(self):
        """Profiles with performance expectations."""
        return {
            "CEO": {
                "strategy": "snapshot",
                "target_response_time_ms": 500,
                "cache_hit_rate_target": 0.60  # Lower for snapshot
            },
            "CFO": {
                "strategy": "snapshot", 
                "target_response_time_ms": 500,
                "cache_hit_rate_target": 0.60
            },
            "Risk_Manager": {
                "strategy": "direct",
                "target_response_time_ms": 1000,
                "cache_hit_rate_target": 0.30  # Lower for direct
            },
            "Trader": {
                "strategy": "cache",
                "target_response_time_ms": 100,
                "cache_hit_rate_target": 0.80  # High for cache
            },
            "Operations": {
                "strategy": "cache",
                "target_response_time_ms": 100,
                "cache_hit_rate_target": 0.80
            }
        }

    @pytest.mark.performance
    def test_cache_hit_rate_targets_by_profile(self, router, cache_system, performance_profiles):
        """Test cache hit rate targets for each profile."""
        kpi_id = "spread_income_detailed"
        requests_per_profile = 20
        
        for profile_type, config in performance_profiles.items():
            if config["cache_hit_rate_target"] < 0.50:
                continue  # Skip profiles not optimized for caching
                
            user_id = f"cache_perf_user_{profile_type.lower()}"
            metrics = PerformanceMetrics()
            
            # Clear existing cache
            cache_system.invalidate_user_cache(user_id)
            
            # First request to populate cache
            router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=profile_type
            )
            
            # Subsequent requests to test cache hits
            for i in range(requests_per_profile):
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                if result and not result.get('error'):
                    if result.get('cache_hit', False):
                        metrics.add_cache_hit()
                    else:
                        metrics.add_cache_miss()
                else:
                    metrics.add_error()
            
            hit_rate = metrics.get_cache_hit_rate()
            target_rate = config["cache_hit_rate_target"]
            
            assert hit_rate >= target_rate, \
                f"Profile {profile_type}: Cache hit rate {hit_rate:.2f} below target {target_rate:.2f}"

    @pytest.mark.performance
    def test_response_time_targets_by_layer(self, router, performance_profiles):
        """Test response time targets for each routing layer."""
        kpi_id = "spread_income_detailed"
        samples_per_profile = 10
        
        for profile_type, config in performance_profiles.items():
            user_id = f"response_time_user_{profile_type.lower()}"
            metrics = PerformanceMetrics()
            
            # Warm up cache for cache-optimized profiles
            if config["strategy"] == "cache":
                router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
            
            # Measure response times
            for i in range(samples_per_profile):
                start_time = time.time()
                
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                end_time = time.time()
                response_time_ms = (end_time - start_time) * 1000
                
                if result and not result.get('error'):
                    metrics.add_response_time(response_time_ms)
                else:
                    metrics.add_error()
            
            avg_response_time = metrics.get_avg_response_time()
            target_time = config["target_response_time_ms"]
            
            assert avg_response_time <= target_time, \
                f"Profile {profile_type} ({config['strategy']}): Avg response time {avg_response_time:.1f}ms exceeds target {target_time}ms"

    @pytest.mark.performance
    def test_concurrent_users_success_rate(self, router, performance_profiles):
        """Test concurrent users with >95% success rate target."""
        concurrent_users = 50
        requests_per_user = 5
        target_success_rate = 0.95
        
        def user_workload(user_index):
            profile_type = list(performance_profiles.keys())[user_index % len(performance_profiles)]
            user_id = f"concurrent_user_{user_index}"
            user_metrics = PerformanceMetrics()
            
            for request_index in range(requests_per_user):
                start_time = time.time()
                
                try:
                    result = router.route_kpi_request(
                        kpi_id="spread_income_detailed",
                        client_id="L2M",
                        user_id=user_id,
                        timeframe="week",
                        currency="BRL",
                        profile_type=profile_type
                    )
                    
                    end_time = time.time()
                    response_time_ms = (end_time - start_time) * 1000
                    
                    if result and not result.get('error'):
                        user_metrics.add_response_time(response_time_ms)
                        if result.get('cache_hit', False):
                            user_metrics.add_cache_hit()
                        else:
                            user_metrics.add_cache_miss()
                    else:
                        user_metrics.add_error()
                        
                except Exception as e:
                    user_metrics.add_error()
            
            return {
                'user_index': user_index,
                'profile_type': profile_type,
                'success_rate': user_metrics.get_success_rate(),
                'avg_response_time': user_metrics.get_avg_response_time(),
                'cache_hit_rate': user_metrics.get_cache_hit_rate()
            }
        
        # Execute concurrent workload
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(user_workload, i) for i in range(concurrent_users)]
            results = [future.result() for future in as_completed(futures)]
        
        # Analyze results
        total_success_rates = [r['success_rate'] for r in results]
        overall_success_rate = statistics.mean(total_success_rates)
        
        assert overall_success_rate >= target_success_rate, \
            f"Overall success rate {overall_success_rate:.3f} below target {target_success_rate:.3f}"
        
        # Validate per-profile performance
        profile_results = {}
        for result in results:
            profile = result['profile_type']
            if profile not in profile_results:
                profile_results[profile] = []
            profile_results[profile].append(result)
        
        for profile_type, profile_data in profile_results.items():
            profile_success_rates = [r['success_rate'] for r in profile_data]
            profile_avg_success = statistics.mean(profile_success_rates)
            
            assert profile_avg_success >= target_success_rate, \
                f"Profile {profile_type} success rate {profile_avg_success:.3f} below target"

    @pytest.mark.performance
    def test_memory_usage_under_load(self, router, performance_profiles):
        """Test memory usage remains within acceptable limits under load."""
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        max_memory_increase_mb = 100  # Maximum 100MB increase
        
        # Generate sustained load
        requests_count = 200
        for i in range(requests_count):
            profile_type = list(performance_profiles.keys())[i % len(performance_profiles)]
            user_id = f"memory_test_user_{i}"
            
            router.route_kpi_request(
                kpi_id="spread_income_detailed",
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=profile_type
            )
            
            # Check memory every 50 requests
            if i % 50 == 0:
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                assert memory_increase <= max_memory_increase_mb, \
                    f"Memory usage increased by {memory_increase:.1f}MB, exceeds {max_memory_increase_mb}MB limit"

    @pytest.mark.performance
    def test_cache_performance_degradation(self, cache_system):
        """Test cache performance doesn't degrade with size."""
        namespace = "perf_test"
        profile_type = "Trader"
        
        # Measure baseline performance
        baseline_times = []
        for i in range(10):
            user_id = f"baseline_user_{i}"
            data = {'test': f'data_{i}'}
            
            start_time = time.time()
            cache_system.set_personalized(namespace, user_id, data, profile_type)
            cached_data = cache_system.get_personalized(namespace, user_id, profile_type)
            end_time = time.time()
            
            baseline_times.append((end_time - start_time) * 1000)  # ms
        
        baseline_avg = statistics.mean(baseline_times)
        
        # Add significant cache load
        for i in range(1000):
            user_id = f"load_user_{i}"
            data = {'test': f'load_data_{i}' * 100}  # Larger data
            cache_system.set_personalized(namespace, user_id, data, profile_type)
        
        # Measure performance after load
        loaded_times = []
        for i in range(10):
            user_id = f"loaded_user_{i}"
            data = {'test': f'data_{i}'}
            
            start_time = time.time()
            cache_system.set_personalized(namespace, user_id, data, profile_type)
            cached_data = cache_system.get_personalized(namespace, user_id, profile_type)
            end_time = time.time()
            
            loaded_times.append((end_time - start_time) * 1000)  # ms
        
        loaded_avg = statistics.mean(loaded_times)
        
        # Performance shouldn't degrade by more than 50%
        degradation_ratio = loaded_avg / baseline_avg
        assert degradation_ratio <= 1.5, \
            f"Cache performance degraded by {(degradation_ratio - 1) * 100:.1f}%, exceeds 50% limit"

    @pytest.mark.performance
    def test_profile_switching_performance(self, router):
        """Test performance when users switch between profiles."""
        user_id = "profile_switching_user"
        kpi_id = "spread_income_detailed"
        profiles = ["CEO", "Trader", "Operations"]
        
        switching_times = []
        
        for i in range(30):  # 10 switches per profile
            current_profile = profiles[i % len(profiles)]
            next_profile = profiles[(i + 1) % len(profiles)]
            
            # Make request with current profile
            router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=current_profile
            )
            
            # Switch to next profile and measure time
            start_time = time.time()
            result = router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=next_profile
            )
            end_time = time.time()
            
            if result and not result.get('error'):
                switching_times.append((end_time - start_time) * 1000)  # ms
        
        avg_switching_time = statistics.mean(switching_times)
        max_switching_time = 1000  # 1 second max
        
        assert avg_switching_time <= max_switching_time, \
            f"Profile switching avg time {avg_switching_time:.1f}ms exceeds {max_switching_time}ms limit"

    @pytest.mark.performance
    def test_sustained_throughput(self, router, performance_profiles):
        """Test sustained throughput over time."""
        duration_seconds = 60  # 1 minute test
        target_rps = 20  # 20 requests per second
        
        metrics = PerformanceMetrics()
        metrics.start_timing()
        
        request_count = 0
        start_time = time.time()
        
        while (time.time() - start_time) < duration_seconds:
            profile_type = list(performance_profiles.keys())[request_count % len(performance_profiles)]
            user_id = f"throughput_user_{request_count % 10}"  # Reuse users for caching
            
            request_start = time.time()
            result = router.route_kpi_request(
                kpi_id="spread_income_detailed",
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=profile_type
            )
            request_end = time.time()
            
            if result and not result.get('error'):
                metrics.add_response_time((request_end - request_start) * 1000)
                if result.get('cache_hit', False):
                    metrics.add_cache_hit()
                else:
                    metrics.add_cache_miss()
            else:
                metrics.add_error()
            
            request_count += 1
            
            # Rate limiting to maintain target RPS
            expected_time = start_time + request_count / target_rps
            current_time = time.time()
            if current_time < expected_time:
                time.sleep(expected_time - current_time)
        
        metrics.end_timing()
        
        actual_duration = metrics.get_total_duration()
        actual_rps = request_count / actual_duration
        success_rate = metrics.get_success_rate()
        
        assert actual_rps >= target_rps * 0.9, \
            f"Actual RPS {actual_rps:.1f} below 90% of target {target_rps}"
        
        assert success_rate >= 0.95, \
            f"Success rate {success_rate:.3f} below 95% during sustained load"

    @pytest.mark.performance
    def test_p95_response_time_targets(self, router, performance_profiles):
        """Test P95 response time targets for each profile."""
        kpi_id = "spread_income_detailed"
        samples_per_profile = 100  # Need enough samples for P95
        
        for profile_type, config in performance_profiles.items():
            user_id = f"p95_user_{profile_type.lower()}"
            metrics = PerformanceMetrics()
            
            # Warm up for cache profiles
            if config["strategy"] == "cache":
                router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
            
            # Collect samples
            for i in range(samples_per_profile):
                start_time = time.time()
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                end_time = time.time()
                
                if result and not result.get('error'):
                    metrics.add_response_time((end_time - start_time) * 1000)
                else:
                    metrics.add_error()
            
            p95_time = metrics.get_p95_response_time()
            target_time = config["target_response_time_ms"]
            p95_target = target_time * 2  # P95 can be 2x average target
            
            assert p95_time <= p95_target, \
                f"Profile {profile_type}: P95 response time {p95_time:.1f}ms exceeds target {p95_target}ms"
