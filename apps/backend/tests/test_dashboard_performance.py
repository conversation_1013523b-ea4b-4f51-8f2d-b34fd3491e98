"""
Test Dashboard Performance
=========================

Testes específicos para medir e validar a performance do dashboard.
"""

import pytest
import asyncio
import time
import statistics
from typing import List, Dict, Any
import httpx
from datetime import datetime

# Base URL da API
API_BASE_URL = "http://localhost:8000"


class DashboardPerformanceTester:
    """Classe para testar performance do dashboard."""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.results: List[Dict[str, Any]] = []
    
    async def close(self):
        """Fecha o cliente HTTP."""
        await self.client.aclose()
    
    async def measure_kpi_load_time(
        self, 
        timeframe: str = "week", 
        currency: str = "all",
        iterations: int = 5
    ) -> Dict[str, Any]:
        """
        Mede o tempo de carregamento dos KPIs do dashboard.
        
        Args:
            timeframe: Período temporal (1d, week, month, quarter)
            currency: Filtro de moeda (all, usd, eur, gbp)
            iterations: Número de iterações para média
            
        Returns:
            Estatísticas de performance
        """
        load_times = []
        kpi_counts = []
        errors = []
        
        for i in range(iterations):
            try:
                start_time = time.time()
                
                response = await self.client.get(
                    f"{self.base_url}/api/dashboard/kpis",
                    params={
                        "sector": "cambio",
                        "client_id": "L2M",
                        "timeframe": timeframe,
                        "priority_only": "true",
                        "currency": currency,
                        "force_refresh": str(i == 0)  # Force refresh na primeira iteração
                    }
                )
                
                end_time = time.time()
                load_time = (end_time - start_time) * 1000  # Em ms
                
                if response.status_code == 200:
                    data = response.json()
                    load_times.append(load_time)
                    kpi_counts.append(len(data.get("kpis", [])))
                    
                    # Log detalhado para primeira iteração
                    if i == 0:
                        print(f"\n📊 KPIs carregados: {len(data.get('kpis', []))}")
                        print(f"⏱️  Tempo de processamento (backend): {data.get('processing_time_ms', 'N/A')}ms")
                        print(f"🔄 Cache refreshed: {data.get('cache_refreshed', False)}")
                else:
                    errors.append(f"HTTP {response.status_code}: {response.text}")
                    
            except Exception as e:
                errors.append(str(e))
            
            # Pequena pausa entre iterações
            if i < iterations - 1:
                await asyncio.sleep(0.5)
        
        # Calcular estatísticas
        if load_times:
            stats = {
                "timeframe": timeframe,
                "currency": currency,
                "iterations": iterations,
                "successful_calls": len(load_times),
                "failed_calls": len(errors),
                "avg_load_time_ms": statistics.mean(load_times),
                "min_load_time_ms": min(load_times),
                "max_load_time_ms": max(load_times),
                "std_dev_ms": statistics.stdev(load_times) if len(load_times) > 1 else 0,
                "avg_kpi_count": statistics.mean(kpi_counts) if kpi_counts else 0,
                "errors": errors,
                "timestamp": datetime.now().isoformat()
            }
        else:
            stats = {
                "timeframe": timeframe,
                "currency": currency,
                "iterations": iterations,
                "successful_calls": 0,
                "failed_calls": len(errors),
                "errors": errors,
                "timestamp": datetime.now().isoformat()
            }
        
        self.results.append(stats)
        return stats
    
    async def test_filter_performance(self) -> List[Dict[str, Any]]:
        """
        Testa a performance com diferentes combinações de filtros.
        
        Returns:
            Lista de resultados de performance
        """
        test_cases = [
            # (timeframe, currency)
            ("1d", "all"),
            ("week", "all"),
            ("month", "all"),
            ("quarter", "all"),
            ("week", "usd"),
            ("week", "eur"),
            ("week", "gbp"),
        ]
        
        results = []
        
        print("\n🚀 Iniciando testes de performance do dashboard...")
        print("=" * 60)
        
        for timeframe, currency in test_cases:
            print(f"\n📊 Testando: timeframe={timeframe}, currency={currency}")
            
            result = await self.measure_kpi_load_time(
                timeframe=timeframe,
                currency=currency,
                iterations=3
            )
            
            results.append(result)
            
            # Imprimir resumo
            if result["successful_calls"] > 0:
                print(f"✅ Tempo médio: {result['avg_load_time_ms']:.2f}ms")
                print(f"   Min: {result['min_load_time_ms']:.2f}ms, Max: {result['max_load_time_ms']:.2f}ms")
            else:
                print(f"❌ Todas as chamadas falharam: {result['errors']}")
        
        return results
    
    async def test_cache_effectiveness(self) -> Dict[str, Any]:
        """
        Testa a efetividade do cache.
        
        Returns:
            Estatísticas sobre o cache
        """
        print("\n🔍 Testando efetividade do cache...")
        print("=" * 60)
        
        # Primeira chamada com force_refresh
        print("\n1️⃣ Primeira chamada (force_refresh=true)...")
        start = time.time()
        response1 = await self.client.get(
            f"{self.base_url}/api/dashboard/kpis",
            params={
                "sector": "cambio",
                "client_id": "L2M",
                "timeframe": "week",
                "priority_only": "true",
                "currency": "all",
                "force_refresh": "true"
            }
        )
        time1 = (time.time() - start) * 1000
        
        # Segunda chamada sem force_refresh (deve usar cache)
        print("2️⃣ Segunda chamada (usando cache)...")
        start = time.time()
        response2 = await self.client.get(
            f"{self.base_url}/api/dashboard/kpis",
            params={
                "sector": "cambio",
                "client_id": "L2M",
                "timeframe": "week",
                "priority_only": "true",
                "currency": "all",
                "force_refresh": "false"
            }
        )
        time2 = (time.time() - start) * 1000
        
        cache_stats = {
            "first_call_ms": time1,
            "cached_call_ms": time2,
            "speed_improvement": f"{(time1 / time2):.2f}x" if time2 > 0 else "N/A",
            "cache_hit_percentage": ((time1 - time2) / time1 * 100) if time1 > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"\n📊 Resultados do cache:")
        print(f"   Primeira chamada: {time1:.2f}ms")
        print(f"   Chamada com cache: {time2:.2f}ms")
        print(f"   Melhoria: {cache_stats['speed_improvement']}")
        print(f"   Taxa de cache hit: {cache_stats['cache_hit_percentage']:.2f}%")
        
        return cache_stats
    
    def generate_report(self) -> str:
        """
        Gera um relatório de performance.
        
        Returns:
            Relatório formatado
        """
        report = []
        report.append("# 📊 Relatório de Performance do Dashboard")
        report.append(f"\n📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("\n## 📈 Resumo dos Testes\n")
        
        # Média geral
        all_times = []
        for result in self.results:
            if result["successful_calls"] > 0:
                all_times.append(result["avg_load_time_ms"])
        
        if all_times:
            report.append(f"- **Tempo médio geral**: {statistics.mean(all_times):.2f}ms")
            report.append(f"- **Tempo mínimo**: {min(all_times):.2f}ms")
            report.append(f"- **Tempo máximo**: {max(all_times):.2f}ms")
            report.append(f"- **Total de testes**: {len(self.results)}")
        
        # Detalhes por filtro
        report.append("\n## 🔍 Detalhes por Filtro\n")
        
        for result in self.results:
            report.append(f"\n### Timeframe: {result['timeframe']}, Currency: {result['currency']}")
            
            if result["successful_calls"] > 0:
                report.append(f"- ✅ Chamadas bem-sucedidas: {result['successful_calls']}/{result['iterations']}")
                report.append(f"- ⏱️  Tempo médio: {result['avg_load_time_ms']:.2f}ms")
                report.append(f"- 📊 KPIs carregados: {result['avg_kpi_count']:.0f}")
                
                # Classificar performance
                avg_time = result['avg_load_time_ms']
                if avg_time < 1000:
                    performance = "🟢 Excelente"
                elif avg_time < 3000:
                    performance = "🟡 Aceitável"
                elif avg_time < 5000:
                    performance = "🟠 Lento"
                else:
                    performance = "🔴 Muito Lento"
                
                report.append(f"- **Performance**: {performance}")
            else:
                report.append(f"- ❌ Todas as chamadas falharam")
                report.append(f"- Erros: {', '.join(result['errors'])}")
        
        # Recomendações
        report.append("\n## 💡 Recomendações\n")
        
        avg_time = statistics.mean(all_times) if all_times else 20000
        
        if avg_time > 5000:
            report.append("### 🚨 Performance Crítica Detectada!\n")
            report.append("1. **Implementar cache mais agressivo**: TTL atual muito curto")
            report.append("2. **Otimizar queries SQL**: Usar índices e query paralela")
            report.append("3. **Pré-calcular dados de gráficos**: Evitar cálculos em tempo real")
            report.append("4. **Implementar paginação**: Carregar KPIs sob demanda")
            report.append("5. **Usar snapshot pré-calculado**: Sistema já existe mas não está sendo usado")
        elif avg_time > 3000:
            report.append("### ⚠️ Performance Precisa de Melhorias\n")
            report.append("1. **Aumentar TTL do cache**: De 5min para 30min")
            report.append("2. **Implementar cache warming**: Pré-carregar dados críticos")
            report.append("3. **Otimizar queries mais lentas**: Identificar gargalos")
        else:
            report.append("### ✅ Performance Adequada\n")
            report.append("1. **Monitorar continuamente**: Manter métricas")
            report.append("2. **Considerar CDN**: Para assets estáticos")
        
        return "\n".join(report)


async def main():
    """Função principal para executar os testes."""
    tester = DashboardPerformanceTester()
    
    try:
        # Executar testes de filtros
        await tester.test_filter_performance()
        
        # Testar efetividade do cache
        await tester.test_cache_effectiveness()
        
        # Gerar relatório
        report = tester.generate_report()
        
        # Salvar relatório
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dashboard_performance_report_{timestamp}.md"
        
        with open(filename, "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"\n📄 Relatório salvo em: {filename}")
        print("\n" + "=" * 60)
        print(report)
        
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main()) 