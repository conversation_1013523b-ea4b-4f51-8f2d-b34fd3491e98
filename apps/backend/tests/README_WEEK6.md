# Week 6 - Testing & Validation 🧪

Comprehensive testing suite for the DataHero4 hybrid architecture implementation.

## Overview

This testing suite validates the **3-layer hybrid architecture** with profile-aware routing, caching optimization, and fail-fast behavior. All tests follow the **"fail fast and fail loud"** philosophy with **no mock data or fallback mechanisms**.

## Testing Objectives

### ✅ **Completed Objectives**

1. **Unit Tests (>90% Coverage)**
   - SmartQueryRouter routing strategies
   - ProfileDetector pattern analysis
   - PersonalizedCacheSystem TTL management

2. **Integration Tests (3-Layer Architecture)**
   - Snapshot → Cache → Direct routing validation
   - Profile-aware routing end-to-end
   - Cache hit rates >80% for optimized profiles

3. **Performance Tests (Specific Metrics)**
   - Response time targets by layer
   - Concurrent users >95% success rate
   - Memory usage monitoring
   - Sustained throughput validation

## Architecture Under Test

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Snapshot      │    │      Cache       │    │     Direct      │
│   Layer         │    │      Layer       │    │     Layer       │
│                 │    │                  │    │                 │
│ • CEO Profile   │    │ • Trader Profile │    │ • Risk Manager  │
│ • CFO Profile   │    │ • Operations     │    │ • Real-time     │
│ • Strategic     │    │ • Speed Focus    │    │ • Monitoring    │
│ • Accuracy      │    │ • Efficiency     │    │ • Alerts        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        └────────────────────────┼────────────────────────┘
                                 │
                    ┌─────────────────────┐
                    │  SmartQueryRouter   │
                    │                     │
                    │ • Profile Detection │
                    │ • Routing Logic     │
                    │ • Cache Management  │
                    │ • Fail-Fast         │
                    └─────────────────────┘
```

## 📁 Test Structure

```
tests/
├── unit/                           # Unit tests (>90% coverage)
│   ├── test_smart_query_router.py     # Routing logic tests
│   ├── test_profile_detector.py       # Profile detection tests
│   └── test_personalized_cache_system.py # Cache system tests
│
├── integration/                    # Integration tests
│   └── test_hybrid_architecture_integration.py # End-to-end tests
│
├── performance/                    # Performance tests
│   └── test_hybrid_architecture_performance.py # Metrics validation
│
└── fixtures/                       # Test data and fixtures
    ├── mock_profiles.json
    ├── sample_kpis.json
    └── test_configurations.yaml
```

## Quick Start

### **Run All Tests**
```bash
# Complete test suite with coverage and reports
python scripts/run_week6_tests.py --coverage --performance --report

# Fast execution (skip unit tests)
python scripts/run_week6_tests.py --fast --performance

# Coverage only
python scripts/run_week6_tests.py --coverage
```

### **Run Specific Test Categories**
```bash
# Unit tests only
poetry run pytest tests/unit/ -v --cov=src

# Integration tests only  
poetry run pytest tests/integration/ -v

# Performance tests only
poetry run pytest tests/performance/ -m performance -v

# Concurrent tests
poetry run pytest -m concurrent -v
```

## Performance Targets

### **Cache Hit Rates by Profile**
| Profile | Target | Strategy | TTL |
|---------|--------|----------|-----|
| CEO | 60% | Snapshot | 3600s |
| CFO | 60% | Snapshot | 1800s |
| Risk Manager | 30% | Direct | 300s |
| Trader | **80%** | Cache | 60s |
| Operations | **80%** | Cache | 900s |

### **Response Time Targets**
| Layer | Target | Use Case |
|-------|--------|----------|
| Snapshot | <500ms | Strategic queries |
| Cache | **<100ms** | Real-time trading |
| Direct | <1000ms | Risk monitoring |

### **Concurrent User Targets**
- **Success Rate**: >95%
- **Max Concurrent Users**: 50
- **Requests per User**: 5
- **Memory Increase Limit**: <100MB

## 🧪 Test Categories

### **Unit Tests** 🔬
```bash
# SmartQueryRouter tests
pytest tests/unit/test_smart_query_router.py -v

# ProfileDetector tests  
pytest tests/unit/test_profile_detector.py -v

# PersonalizedCacheSystem tests
pytest tests/unit/test_personalized_cache_system.py -v
```

**Coverage Target**: >90%

### **Integration Tests** 🔗
```bash
# Full architecture integration
pytest tests/integration/test_hybrid_architecture_integration.py -v

# Specific integration scenarios
pytest tests/integration/ -k "test_end_to_end_routing" -v
pytest tests/integration/ -k "test_cache_hit_rate" -v
pytest tests/integration/ -k "test_concurrent_users" -v
```

### **Performance Tests** ⚡
```bash
# All performance tests
pytest tests/performance/ -m performance -v

# Specific performance scenarios
pytest tests/performance/ -k "test_cache_hit_rate_targets" -v
pytest tests/performance/ -k "test_response_time_targets" -v
pytest tests/performance/ -k "test_concurrent_users_success_rate" -v
```

## 📈 Metrics & Reporting

### **Coverage Reports**
```bash
# Generate HTML coverage report
poetry run coverage html

# View coverage report
open htmlcov/index.html
```

### **Performance Metrics**
- Cache hit rates by profile
- Response time percentiles (P50, P95, P99)
- Success rates under load
- Memory usage patterns
- Throughput measurements

### **Test Reports**
- JSON test results: `test_reports/week6_test_report_*.json`
- HTML coverage: `htmlcov/index.html`
- Performance metrics: Embedded in test output

## 🔧 Configuration

### **Pytest Configuration**
- Main config: `pytest.ini`
- Week 6 specific: `pytest_week6.ini`
- Markers: unit, integration, performance, slow, concurrent

### **Environment Variables**
```bash
# Test database
export TEST_DATABASE_URL="postgresql://..."

# BCB API (for integration tests)
export BCB_API_KEY="your_api_key"

# Cache settings
export REDIS_URL="redis://localhost:6379/1"
```

## 🚨 Fail-Fast Principles

All tests follow **fail-fast** principles:

❌ **No Mock Data**
- No hardcoded values in KPI calculations
- No placeholder data in API responses
- No fake database records

❌ **No Fallback Logic**
- No fallback queries when primary fails
- No default values when real data unavailable
- No backup implementations hiding failures

✅ **Error Visibility**
- Explicit errors instead of fallback values
- Clear logging of all failures
- Immediate visible failures over silent degradation

## 🎯 Success Criteria

### **Week 6 Validation Checklist**

- [x] **Unit Tests**: >90% coverage achieved
- [x] **Integration Tests**: All 3-layer routing validated
- [x] **Performance Tests**: All metrics targets met
- [x] **Cache Hit Rates**: >80% for cache-optimized profiles
- [x] **Response Times**: All layer targets achieved
- [x] **Concurrent Users**: >95% success rate
- [x] **Fail-Fast**: No fallbacks or mock data
- [x] **Memory Usage**: Within acceptable limits
- [x] **Documentation**: Complete test documentation

## 🔍 Debugging & Troubleshooting

### **Common Issues**

**Cache Tests Failing**
```bash
# Check Redis connection
redis-cli ping

# Clear test cache
redis-cli FLUSHDB
```

**Performance Tests Timeout**
```bash
# Increase timeout
pytest --timeout=600 tests/performance/

# Run with fewer samples
pytest tests/performance/ -k "not sustained_load"
```

**Coverage Below Target**
```bash
# Identify missing coverage
poetry run coverage report --show-missing

# Generate detailed HTML report
poetry run coverage html
```

## 📚 Additional Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)
- [DataHero4 Architecture Documentation](../../docs/)
- [Performance Testing Best Practices](./performance/README.md)

---

**Week 6 Testing & Validation** - DataHero4 Team  
*Last Updated: 2025-01-21*
