"""
Test Configuration for Week 6 Testing & Validation - REAL DATABASE
==================================================================

Real test configuration for Week 6 testing suite.
Tests REAL components with REAL PostgreSQL database connections.
NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Uses pytest-postgresql for real database testing.

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import sys
import pytest
import sqlalchemy
from pathlib import Path
from sqlalchemy import create_engine, text

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
from dotenv import load_dotenv
env_path = project_root / '.env'
if env_path.exists():
    load_dotenv(env_path)
    print(f"✅ Loaded environment variables from {env_path}")
else:
    print(f"⚠️  .env file not found at {env_path}")


@pytest.fixture(scope="function")
def real_postgresql(postgresql):
    """
    Real PostgreSQL database for testing - NO MOCKS.

    Uses pytest-postgresql to create a real PostgreSQL instance.
    Each test gets a fresh database with proper schema.
    """
    print("🗄️  Setting up REAL PostgreSQL database for testing...")

    # Use the postgresql connection directly
    cursor = postgresql.cursor()

    # Create learning database schema
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS query_cache (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            query_text TEXT NOT NULL,
            query_hash VARCHAR(255) NOT NULL,
            embedding BYTEA,
            result_data JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # Create profile detection tables
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS user_profiles (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL UNIQUE,
            detected_profile VARCHAR(50),
            confidence FLOAT,
            analysis_data JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # Create KPI snapshots table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS kpi_snapshots (
            id SERIAL PRIMARY KEY,
            kpi_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            profile_type VARCHAR(50),
            snapshot_data JSONB NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # Create indexes separately
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_lookup
        ON kpi_snapshots(kpi_id, client_id, profile_type)
    """)

    postgresql.commit()
    cursor.close()

    print("✅ Real PostgreSQL database ready for testing")
    yield postgresql

    print("🧹 Real PostgreSQL database cleaned up")


@pytest.fixture
def real_db_manager(real_postgresql):
    """
    Real database manager for testing - NO MOCKS.
    
    Provides a real LearningDBManager instance connected to test database.
    """
    from src.utils.learning_db_utils import LearningDBManager
    
    # Create real database manager using the postgresql connection
    # For now, we'll use the existing database manager
    db_manager = LearningDBManager()
    
    print("✅ Real database manager created")
    yield db_manager
    
    # Cleanup
    db_manager.close()
    print("🧹 Real database manager cleaned up")


@pytest.fixture
def real_kpi_service(real_postgresql):
    """
    Real KPI service for testing - NO MOCKS.
    
    Provides a real KpiService instance.
    """
    from src.services.kpi_service import KpiService
    
    # Create real KPI service
    kpi_service = KpiService()
    
    print("✅ Real KPI service created")
    yield kpi_service
    
    print("🧹 Real KPI service cleaned up")


@pytest.fixture
def sample_kpi_data():
    """Sample KPI data for testing."""
    return {
        'spread_income_detailed': {
            'kpi_id': 'spread_income_detailed',
            'client_id': 'L2M',
            'timeframe': 'week',
            'currency': 'BRL'
        },
        'margem_liquida_operacional': {
            'kpi_id': 'margem_liquida_operacional',
            'client_id': 'L2M',
            'timeframe': 'month',
            'currency': 'BRL'
        },
        'custo_por_transacao': {
            'kpi_id': 'custo_por_transacao',
            'client_id': 'L2M',
            'timeframe': 'week',
            'currency': 'BRL'
        },
        'tempo_processamento_medio': {
            'kpi_id': 'tempo_processamento_medio',
            'client_id': 'L2M',
            'timeframe': 'day',
            'currency': 'BRL'
        }
    }


@pytest.fixture
def test_profiles():
    """Real profile configurations for testing."""
    return {
        'CEO': {
            'strategy': 'snapshot',
            'ttl': 3600,
            'priority': 'accuracy',
            'characteristics': ['strategic', 'high_level', 'long_term']
        },
        'CFO': {
            'strategy': 'snapshot',
            'ttl': 1800,
            'priority': 'accuracy',
            'characteristics': ['financial', 'detailed', 'analytical']
        },
        'Risk_Manager': {
            'strategy': 'direct',
            'ttl': 300,
            'priority': 'real_time',
            'characteristics': ['monitoring', 'alerts', 'immediate']
        },
        'Trader': {
            'strategy': 'cache',
            'ttl': 60,
            'priority': 'speed',
            'characteristics': ['fast', 'frequent', 'real_time']
        },
        'Operations': {
            'strategy': 'cache',
            'ttl': 900,
            'priority': 'efficiency',
            'characteristics': ['operational', 'routine', 'efficient']
        }
    }


@pytest.fixture
def test_users():
    """Test user IDs for different profiles."""
    return {
        'CEO': 'test_ceo_user',
        'CFO': 'test_cfo_user',
        'Risk_Manager': 'test_risk_user',
        'Trader': 'test_trader_user',
        'Operations': 'test_ops_user'
    }


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual components"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for multi-layer architecture"
    )
    config.addinivalue_line(
        "markers", "performance: Performance tests with specific metrics validation"
    )
    config.addinivalue_line(
        "markers", "slow: Slow running tests (>5 seconds)"
    )
    config.addinivalue_line(
        "markers", "database: Tests that require database connection"
    )
    config.addinivalue_line(
        "markers", "real: Tests using real components (no mocks)"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
            item.add_marker(pytest.mark.real)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
            item.add_marker(pytest.mark.real)
            item.add_marker(pytest.mark.database)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.real)
        
        # Add slow marker for tests that might be slow
        if "sustained_load" in item.name or "concurrent" in item.name:
            item.add_marker(pytest.mark.slow)


# Test result reporting
def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Custom terminal summary for Week 6 tests."""
    if hasattr(terminalreporter, 'stats'):
        passed = len(terminalreporter.stats.get('passed', []))
        failed = len(terminalreporter.stats.get('failed', []))
        errors = len(terminalreporter.stats.get('error', []))
        
        print(f"\n🧪 WEEK 6 TEST SUMMARY (REAL COMPONENTS):")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"🚨 Errors: {errors}")
        
        if exitstatus == 0:
            print("🎉 All Week 6 tests passed with REAL components!")
        else:
            print("⚠️  Some Week 6 tests failed - check real component issues above")
