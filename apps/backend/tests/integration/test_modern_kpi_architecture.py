#!/usr/bin/env python3
"""
Integration Tests for Modern KPI Architecture
=============================================

Tests for the new SQLModel-based KPI architecture with:
- Repository pattern
- Unified configuration
- Modern service layer
- TanStack Query integration
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))


class TestKpiDefinitions:
    """Test unified KPI definitions configuration."""
    
    def test_kpi_definitions_structure(self):
        """Test that KPI definitions have correct structure."""
        from src.config.kpi_definitions import KPI_DEFINITIONS, CRITICAL_KPIS
        
        assert len(KPI_DEFINITIONS) > 0, "Should have KPI definitions"
        assert len(CRITICAL_KPIS) > 0, "Should have critical KPIs"
        
        # Test structure of first KPI
        first_kpi = list(KPI_DEFINITIONS.values())[0]
        required_fields = ['id', 'name', 'description', 'category', 'priority', 'unit', 'format_type']
        
        for field in required_fields:
            assert field in first_kpi, f"KPI should have {field} field"
    
    def test_critical_kpis_exist(self):
        """Test that all critical KPIs exist in definitions."""
        from src.config.kpi_definitions import KPI_DEFINITIONS, CRITICAL_KPIS
        
        for kpi_id in CRITICAL_KPIS:
            assert kpi_id in KPI_DEFINITIONS, f"Critical KPI {kpi_id} should exist in definitions"
    
    def test_kpi_helper_functions(self):
        """Test KPI helper functions."""
        from src.config.kpi_definitions import (
            get_kpi_definition,
            get_critical_kpis,
            get_timeframe_config,
            get_currency_config
        )
        
        # Test get_kpi_definition
        total_volume = get_kpi_definition("total_volume")
        assert total_volume is not None, "Should return total_volume definition"
        assert total_volume.get('name') == "Volume Total Negociado"
        
        # Test get_critical_kpis
        critical = get_critical_kpis()
        assert isinstance(critical, list), "Should return list"
        assert len(critical) > 0, "Should have critical KPIs"
        
        # Test timeframe config
        week_config = get_timeframe_config("week")
        assert week_config is not None, "Should return week config"
        assert 'name' in week_config, "Should have name field"
        
        # Test currency config
        usd_config = get_currency_config("usd")
        assert usd_config is not None, "Should return USD config"
        assert 'symbol' in usd_config, "Should have symbol field"


class TestKpiModels:
    """Test SQLModel KPI models."""
    
    def test_kpi_calculation_result_model(self):
        """Test KpiCalculationResult model creation."""
        from src.models.kpi_models import KpiCalculationResult
        
        result = KpiCalculationResult(
            kpi_id="test_kpi",
            title="Test KPI",
            description="Test description",
            current_value=100.0,
            trend="positive",
            format_type="number"
        )
        
        assert result.kpi_id == "test_kpi"
        assert result.title == "Test KPI"
        assert result.current_value == 100.0
        assert result.trend == "positive"
    
    def test_transaction_model(self):
        """Test Transaction model structure."""
        from src.models.kpi_models import Transaction
        
        # Test that model has required fields
        fields = Transaction.__fields__
        required_fields = ['client_id', 'currency', 'amount', 'status']
        
        for field in required_fields:
            assert field in fields, f"Transaction should have {field} field"


class TestKpiService:
    """Test modern KPI service."""
    
    def test_kpi_service_initialization(self):
        """Test KPI service can be initialized."""
        try:
            from src.services.kpi_service import get_kpi_service
            
            service = get_kpi_service()
            assert service is not None, "Service should be initialized"
            assert hasattr(service, 'cache'), "Service should have cache"
            assert hasattr(service, 'engine'), "Service should have engine"
            
        except Exception as e:
            # Skip if database not available
            pytest.skip(f"Database not available: {e}")
    
    def test_kpi_service_singleton(self):
        """Test that KPI service is singleton."""
        try:
            from src.services.kpi_service import get_kpi_service
            
            service1 = get_kpi_service()
            service2 = get_kpi_service()
            
            assert service1 is service2, "Should return same instance"
            
        except Exception as e:
            pytest.skip(f"Database not available: {e}")
    
    def test_kpi_service_methods(self):
        """Test KPI service has required methods."""
        try:
            from src.services.kpi_service import get_kpi_service
            
            service = get_kpi_service()
            
            # Test required methods exist
            required_methods = [
                'get_dashboard_kpis',
                'calculate_single_kpi',
                'invalidate_kpis',
                'get_kpi_metadata',
                'get_available_kpis'
            ]
            
            for method in required_methods:
                assert hasattr(service, method), f"Service should have {method} method"
                assert callable(getattr(service, method)), f"{method} should be callable"
            
        except Exception as e:
            pytest.skip(f"Database not available: {e}")


class TestDashboardApi:
    """Test dashboard API integration."""
    
    def test_dashboard_api_imports(self):
        """Test that dashboard API can import new service."""
        try:
            from src.interfaces.dashboard_api import get_kpi_service_dependency
            
            # Should not raise import error
            assert callable(get_kpi_service_dependency), "Should be callable"
            
        except ImportError as e:
            pytest.fail(f"Dashboard API should import successfully: {e}")
    
    @patch('src.services.kpi_service.os.getenv')
    def test_dashboard_api_dependency(self, mock_getenv):
        """Test dashboard API dependency injection."""
        # Mock environment variable
        mock_getenv.return_value = "postgresql://test:test@localhost:5432/test"
        
        try:
            from src.interfaces.dashboard_api import get_kpi_service_dependency
            
            service = get_kpi_service_dependency()
            assert service is not None, "Should return service instance"
            
        except Exception as e:
            pytest.skip(f"Database not available: {e}")


class TestArchitectureIntegration:
    """Test overall architecture integration."""
    
    def test_no_duplicate_imports(self):
        """Test that removed files are not imported anywhere."""
        import ast
        import glob
        
        # Files that should not be imported
        forbidden_imports = [
            'kpi_calculator',
            'dashboard_service',
            'dashboard_kpis_refactored',
            'critical_kpis',
            'dashboard_config'
        ]
        
        # Check all Python files in src
        python_files = glob.glob("src/**/*.py", recursive=True)
        
        for file_path in python_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Parse AST to find imports
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            for forbidden in forbidden_imports:
                                assert forbidden not in alias.name, f"Found forbidden import {alias.name} in {file_path}"
                    
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            for forbidden in forbidden_imports:
                                assert forbidden not in node.module, f"Found forbidden import from {node.module} in {file_path}"
                                
            except (SyntaxError, UnicodeDecodeError):
                # Skip files that can't be parsed
                continue
    
    def test_configuration_consolidation(self):
        """Test that configuration is properly consolidated."""
        from src.config.kpi_definitions import KPI_DEFINITIONS, CRITICAL_KPIS
        
        # Should have consolidated configuration
        assert len(KPI_DEFINITIONS) >= 6, "Should have at least 6 KPI definitions"
        assert len(CRITICAL_KPIS) >= 6, "Should have at least 6 critical KPIs"
        
        # All critical KPIs should be in definitions
        for kpi_id in CRITICAL_KPIS:
            assert kpi_id in KPI_DEFINITIONS, f"Critical KPI {kpi_id} should be in definitions"
    
    def test_service_layer_consistency(self):
        """Test that service layer is consistent."""
        try:
            from src.services.kpi_service import get_kpi_service
            from src.config.kpi_definitions import get_kpi_definition
            
            service = get_kpi_service()
            
            # Test that service can get metadata
            metadata = service.get_kpi_metadata("total_volume")
            definition = get_kpi_definition("total_volume")
            
            # Should return same data
            assert metadata == definition, "Service metadata should match configuration"
            
        except Exception as e:
            pytest.skip(f"Service layer not available: {e}")


if __name__ == "__main__":
    print("🧪 Running Modern KPI Architecture Tests...")
    
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
