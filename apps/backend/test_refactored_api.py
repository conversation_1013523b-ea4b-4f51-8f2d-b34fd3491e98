#!/usr/bin/env python3
"""
Teste da API refatorada com o novo sistema de cache.
"""

import requests
import json
import time

def test_api_refactored():
    """Testa a API refatorada."""
    base_url = "http://localhost:8000/api/dashboard"
    
    print("🧪 Testando API Refatorada com Cache Unificado\n")
    
    # Teste 1: Requisição básica
    print("1️⃣ Teste de Requisição Básica")
    print("-" * 40)
    
    start = time.time()
    response = requests.get(f"{base_url}/kpis", params={
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all"
    })
    time1 = (time.time() - start) * 1000
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"✅ KPIs retornados: {data['total_count']}")
        print(f"⏱️  Tempo: {time1:.2f}ms")
        print(f"📊 Processing time (API): {data.get('processing_time_ms', 'N/A')}ms")
    else:
        print(f"❌ Erro: {response.status_code}")
        print(response.text)
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 2: Cache Hit
    print("2️⃣ Teste de Cache Hit")
    print("-" * 40)
    
    start = time.time()
    response2 = requests.get(f"{base_url}/kpis", params={
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all"
    })
    time2 = (time.time() - start) * 1000
    
    if response2.status_code == 200:
        data2 = response2.json()
        print(f"✅ Status: {response2.status_code}")
        print(f"⏱️  Tempo: {time2:.2f}ms")
        print(f"📊 Processing time (API): {data2.get('processing_time_ms', 'N/A')}ms")
        
        if time1 > 0 and time2 > 0:
            speedup = time1 / time2
            print(f"🚀 Speedup: {speedup:.1f}x mais rápido!")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 3: Diferentes Timeframes
    print("3️⃣ Teste de Diferentes Timeframes")
    print("-" * 40)
    
    timeframes = ['1d', 'week', 'month', 'quarter']
    for tf in timeframes:
        response = requests.get(f"{base_url}/kpis", params={
            "client_id": "L2M",
            "timeframe": tf,
            "currency": "all"
        })
        
        if response.status_code == 200:
            data = response.json()
            # Pegar valor do total_volume
            total_volume = None
            for kpi in data['kpis']:
                if kpi['id'] == 'total_volume':
                    total_volume = kpi['currentValue']
                    break
            
            print(f"Timeframe {tf:8} -> Total Volume: {total_volume:,.2f}")
        else:
            print(f"Timeframe {tf:8} -> Erro: {response.status_code}")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 4: Force Refresh
    print("4️⃣ Teste de Force Refresh")
    print("-" * 40)
    
    response = requests.get(f"{base_url}/kpis", params={
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all",
        "force_refresh": "true"
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"✅ Cache refreshed: {data.get('cache_refreshed', False)}")
        print(f"⏱️  Processing time: {data.get('processing_time_ms', 'N/A')}ms")
    
    print("\n" + "=" * 50 + "\n")
    
    # Teste 5: Cache Stats
    print("5️⃣ Teste de Cache Stats")
    print("-" * 40)
    
    response = requests.get(f"{base_url}/kpis/cache-stats")
    
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"📊 Cache Stats:")
        
        cache_stats = stats.get('cache_stats', {})
        if 'general' in cache_stats:
            general = cache_stats['general']
            print(f"   - Hit Rate: {general.get('hit_rate', 'N/A')}")
            print(f"   - Total Requests: {general.get('total_requests', 0)}")
            print(f"   - Cache Size: {general.get('size', 0)}")
        
        if 'by_namespace' in cache_stats:
            print(f"\n   Por Namespace:")
            for ns, data in cache_stats['by_namespace'].items():
                print(f"   - {ns}: {data.get('count', 0)} entradas")
    else:
        print(f"❌ Erro: {response.status_code}")
        print(response.text)
    
    print("\n✅ Testes concluídos!")


if __name__ == "__main__":
    test_api_refactored() 