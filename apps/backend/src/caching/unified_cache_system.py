"""
Unified Cache System for DataHero4
==================================

Sistema de cache unificado e eficiente que substitui todas as implementações legadas.
Utiliza apenas cache em memória com TTL inteligente e invalidação seletiva.

Features:
- Cache em memória com TTL dinâmico baseado em volatilidade
- Invalidação seletiva por padrão
- Cache keys inteligentes com namespace
- Monitoramento e métricas
- Thread-safe
"""

import time
import logging
from typing import Any, Dict, Optional, List, Tuple
from collections import OrderedDict
from threading import RLock
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import json

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Representa uma entrada no cache."""
    value: Any
    created_at: float
    ttl: int
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


class UnifiedCacheSystem:
    """
    Sistema de cache unificado com TTL inteligente e invalidação seletiva.
    Substitui todas as implementações legadas de cache.
    """
    
    def __init__(self, max_size: int = 1000, enable_stats: bool = True):
        """
        Inicializa o sistema de cache unificado.
        
        Args:
            max_size: Tamanho máximo do cache
            enable_stats: Habilitar estatísticas
        """
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = RLock()
        self.max_size = max_size
        self.enable_stats = enable_stats
        
        # Estatísticas
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "invalidations": 0,
            "total_requests": 0
        }
        
        # TTL configuration by data type
        self.ttl_config = {
            # KPI values - baseado em timeframe
            "kpi:value:1d": 60,        # 1 minuto para dados diários
            "kpi:value:week": 300,     # 5 minutos para dados semanais
            "kpi:value:month": 900,    # 15 minutos para dados mensais
            "kpi:value:quarter": 1800, # 30 minutos para dados trimestrais
            
            # KPI charts - um pouco mais longo que values
            "kpi:chart:1d": 120,       # 2 minutos
            "kpi:chart:week": 600,     # 10 minutos
            "kpi:chart:month": 1200,   # 20 minutos
            "kpi:chart:quarter": 2400, # 40 minutos
            
            # Query results
            "query:result": 600,       # 10 minutos
            
            # Context detection
            "context:detection": 1800, # 30 minutos
            
            # Default
            "default": 300            # 5 minutos
        }
        
        logger.info("✅ Unified Cache System initialized")
    
    def _generate_cache_key(self, namespace: str, **kwargs) -> str:
        """
        Gera uma chave de cache única e consistente.

        Args:
            namespace: Namespace do cache (ex: 'kpi:value', 'query:result')
            **kwargs: Parâmetros para compor a chave

        Returns:
            Chave de cache única
        """
        # Ordenar kwargs para garantir consistência
        sorted_params = sorted(kwargs.items())
        key_parts = [namespace]

        for k, v in sorted_params:
            if v is not None:
                key_parts.append(f"{k}:{v}")

        cache_key = ":".join(key_parts)
        logger.info(f"🔑 Generated cache key: {cache_key}")
        return cache_key
    
    def _get_ttl_for_key(self, cache_key: str, timeframe: Optional[str] = None) -> int:
        """
        Determina o TTL apropriado para uma chave de cache.
        
        Args:
            cache_key: Chave de cache
            timeframe: Timeframe opcional para KPIs
            
        Returns:
            TTL em segundos
        """
        # Para KPIs, usar TTL baseado em timeframe
        if cache_key.startswith("kpi:") and timeframe:
            prefix = cache_key.split(":")[0] + ":" + cache_key.split(":")[1]
            ttl_key = f"{prefix}:{timeframe}"
            return self.ttl_config.get(ttl_key, self.ttl_config["default"])
        
        # Para outros tipos, verificar prefixo
        for key_pattern, ttl in self.ttl_config.items():
            if cache_key.startswith(key_pattern.split(":")[0]):
                return ttl
        
        return self.ttl_config["default"]
    
    def get(self, namespace: str, **kwargs) -> Optional[Any]:
        """
        Recupera valor do cache.
        
        Args:
            namespace: Namespace do cache
            **kwargs: Parâmetros da chave
            
        Returns:
            Valor cacheado ou None
        """
        cache_key = self._generate_cache_key(namespace, **kwargs)

        with self._lock:
            if self.enable_stats:
                self.stats["total_requests"] += 1

            entry = self._cache.get(cache_key)

            if entry is None:
                logger.info(f"❌ Cache MISS for key: {cache_key}")
                if self.enable_stats:
                    self.stats["misses"] += 1
                return None
            
            # Verificar TTL
            age = time.time() - entry.created_at
            if age > entry.ttl:
                # Expirado
                del self._cache[cache_key]
                if self.enable_stats:
                    self.stats["misses"] += 1
                return None
            
            # Atualizar estatísticas de acesso
            entry.access_count += 1
            entry.last_accessed = time.time()
            
            # Move para o final (LRU)
            self._cache.move_to_end(cache_key)
            
            if self.enable_stats:
                self.stats["hits"] += 1
            
            return entry.value
    
    def set(self, namespace: str, value: Any, ttl: Optional[int] = None, 
            timeframe: Optional[str] = None, **kwargs):
        """
        Armazena valor no cache.
        
        Args:
            namespace: Namespace do cache
            value: Valor a ser cacheado
            ttl: TTL customizado (opcional)
            timeframe: Timeframe para KPIs (opcional)
            **kwargs: Parâmetros da chave
        """
        # Incluir timeframe nos kwargs se fornecido
        if timeframe is not None:
            kwargs['timeframe'] = timeframe
            
        cache_key = self._generate_cache_key(namespace, **kwargs)
        
        # Determinar TTL
        if ttl is None:
            ttl = self._get_ttl_for_key(cache_key, timeframe)
        
        with self._lock:
            # Verificar capacidade
            if len(self._cache) >= self.max_size and cache_key not in self._cache:
                # Remover entrada mais antiga (LRU)
                self._cache.popitem(last=False)
                if self.enable_stats:
                    self.stats["evictions"] += 1
            
            # Criar entrada
            entry = CacheEntry(
                value=value,
                created_at=time.time(),
                ttl=ttl,
                metadata={
                    "namespace": namespace,
                    "timeframe": timeframe,
                    **kwargs
                }
            )
            
            self._cache[cache_key] = entry
            self._cache.move_to_end(cache_key)
            logger.info(f"✅ Cache SET for key: {cache_key} (TTL: {ttl}s)")
    
    def invalidate(self, namespace: str, pattern: Optional[str] = None, **kwargs):
        """
        Invalida entradas do cache.
        
        Args:
            namespace: Namespace do cache
            pattern: Padrão para invalidação (opcional)
            **kwargs: Parâmetros específicos para invalidar
        """
        with self._lock:
            keys_to_remove = []
            
            if pattern:
                # Invalidar por padrão
                prefix = f"{namespace}:{pattern}"
                keys_to_remove = [k for k in self._cache if k.startswith(prefix)]
            elif kwargs:
                # Invalidar chave específica
                cache_key = self._generate_cache_key(namespace, **kwargs)
                if cache_key in self._cache:
                    keys_to_remove = [cache_key]
            else:
                # Invalidar todo o namespace
                keys_to_remove = [k for k in self._cache if k.startswith(namespace)]
            
            for key in keys_to_remove:
                del self._cache[key]
                if self.enable_stats:
                    self.stats["invalidations"] += 1
            
            if keys_to_remove:
                logger.info(f"🗑️ Invalidated {len(keys_to_remove)} cache entries")
    
    def clear(self):
        """Limpa todo o cache."""
        with self._lock:
            size = len(self._cache)
            self._cache.clear()
            logger.info(f"🗑️ Cleared entire cache ({size} entries)")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache."""
        with self._lock:
            total = self.stats["total_requests"]
            hit_rate = (self.stats["hits"] / total * 100) if total > 0 else 0
            
            # Calcular idade média das entradas
            ages = []
            now = time.time()
            for entry in self._cache.values():
                ages.append(now - entry.created_at)
            
            avg_age = sum(ages) / len(ages) if ages else 0
            
            return {
                **self.stats,
                "size": len(self._cache),
                "max_size": self.max_size,
                "hit_rate": f"{hit_rate:.1f}%",
                "avg_entry_age_seconds": round(avg_age, 2)
            }
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas detalhadas por namespace."""
        with self._lock:
            namespace_stats = {}
            
            for cache_key, entry in self._cache.items():
                namespace = entry.metadata.get("namespace", "unknown")
                
                if namespace not in namespace_stats:
                    namespace_stats[namespace] = {
                        "count": 0,
                        "total_age": 0,
                        "total_accesses": 0
                    }
                
                age = time.time() - entry.created_at
                namespace_stats[namespace]["count"] += 1
                namespace_stats[namespace]["total_age"] += age
                namespace_stats[namespace]["total_accesses"] += entry.access_count
            
            # Calcular médias
            for namespace, stats in namespace_stats.items():
                if stats["count"] > 0:
                    stats["avg_age"] = round(stats["total_age"] / stats["count"], 2)
                    stats["avg_accesses"] = round(stats["total_accesses"] / stats["count"], 2)
            
            return {
                "general": self.get_stats(),
                "by_namespace": namespace_stats
            }


# Singleton instance
_unified_cache: Optional[UnifiedCacheSystem] = None


def get_unified_cache() -> UnifiedCacheSystem:
    """Retorna instância singleton do cache unificado."""
    global _unified_cache
    if _unified_cache is None:
        _unified_cache = UnifiedCacheSystem()
    return _unified_cache 