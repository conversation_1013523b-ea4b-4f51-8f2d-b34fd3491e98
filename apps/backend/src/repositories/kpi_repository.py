"""
KPI Repository - Data Access Layer
==================================

Repository pattern implementation for KPI data access using SQLModel.
Provides clean separation between business logic and data access.
"""

from typing import List, Optional, Dict, Any
from sqlmodel import Session, select
from decimal import Decimal
import logging

from src.models.kpi_models import (
    KpiRepository as BaseKpiRepository,
    KpiCalculationResult,
    Transaction,
    Client
)
from src.config.kpi_definitions import (
    KPI_DEFINITIONS,
    CRITICAL_KPIS,
    get_kpi_definition,
    get_timeframe_config,
    get_currency_config
)

logger = logging.getLogger(__name__)


class KpiRepository(BaseKpiRepository):
    """Enhanced KPI Repository with full business logic."""
    
    def __init__(self, session: Session):
        super().__init__(session)
        self.session = session
    
    def get_dashboard_kpis(
        self,
        sector: str = "cambio",
        timeframe: str = "week",
        currency: str = "all",
        priority_only: bool = True,
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get dashboard KPIs with filters.

        Args:
            sector: Business sector (cambio)
            timeframe: Time period for calculations
            currency: Currency filter
            priority_only: Return only priority KPIs
            category: Optional category filter

        Returns:
            List of calculated KPI data
        """
        try:
            logger.info(f"📊 Calculating dashboard KPIs for {client_id}")
            
            # Determine which KPIs to calculate
            kpi_ids = self._get_kpi_ids_to_calculate(priority_only, category)
            
            # Calculate each KPI
            results = []
            for kpi_id in kpi_ids:
                try:
                    kpi_result = self._calculate_single_kpi(
                        kpi_id, timeframe, currency
                    )
                    if kpi_result:
                        results.append(self._format_kpi_response(kpi_result))
                except Exception as e:
                    logger.error(f"Error calculating KPI {kpi_id}: {e}")
                    continue
            
            # Sort by display order
            results.sort(key=lambda x: x.get('order', 999))
            
            logger.info(f"✅ Calculated {len(results)} KPIs successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error getting dashboard KPIs: {e}")
            raise
    
    def calculate_single_kpi(
        self,
        kpi_id: str,
        timeframe: str = "week",
        currency: str = "all"
    ) -> Optional[Dict[str, Any]]:
        """Calculate a single KPI on demand."""
        try:
            result = self._calculate_single_kpi(kpi_id, timeframe, currency)
            return self._format_kpi_response(result) if result else None
        except Exception as e:
            logger.error(f"Error calculating single KPI {kpi_id}: {e}")
            raise
    
    def _get_kpi_ids_to_calculate(
        self, 
        priority_only: bool, 
        category: Optional[str]
    ) -> List[str]:
        """Determine which KPI IDs to calculate."""
        if priority_only:
            return CRITICAL_KPIS
        
        kpi_ids = list(KPI_DEFINITIONS.keys())
        
        if category:
            kpi_ids = [
                kpi_id for kpi_id in kpi_ids
                if KPI_DEFINITIONS[kpi_id].get('category', '').value == category
            ]
        
        return kpi_ids
    
    def _calculate_single_kpi(
        self,
        kpi_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[KpiCalculationResult]:
        """Calculate a single KPI using the appropriate method."""
        
        # Map KPI IDs to calculation methods
        calculation_methods = {
            "total_volume": self.get_total_volume,
            "average_spread": self.get_average_spread,
            "average_ticket": self.get_average_ticket,
            "conversion_rate": self._get_conversion_rate,
            "retention_rate": self._get_retention_rate,
            "compliance_score": self._get_compliance_score
        }
        
        method = calculation_methods.get(kpi_id)
        if not method:
            logger.warning(f"No calculation method for KPI: {kpi_id}")
            return None
        
        return method(timeframe, currency)
    
    def _get_conversion_rate(
        self,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> KpiCalculationResult:
        """Calculate conversion rate KPI."""
        # Placeholder implementation - would need leads/prospects table
        return KpiCalculationResult(
            kpi_id="conversion_rate",
            title="Taxa de Conversão",
            description="Percentual de leads convertidos em clientes",
            current_value=0.18,  # 18%
            format_type="percentage",
            unit="%",
            is_priority=True,
            category="conversion",
            display_order=4
        )
    
    def _get_retention_rate(
        self,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> KpiCalculationResult:
        """Calculate retention rate KPI."""
        # Placeholder implementation - would need customer retention logic
        return KpiCalculationResult(
            kpi_id="retention_rate",
            title="Taxa de Retenção",
            description="Percentual de clientes que retornaram",
            current_value=0.75,  # 75%
            format_type="percentage",
            unit="%",
            is_priority=True,
            category="retention",
            display_order=5
        )
    
    def _get_compliance_score(
        self,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> KpiCalculationResult:
        """Calculate compliance score KPI."""
        # Placeholder implementation - would need compliance data
        return KpiCalculationResult(
            kpi_id="compliance_score",
            title="Score de Compliance",
            description="Pontuação de conformidade regulatória",
            current_value=85.0,
            format_type="number",
            unit="score",
            is_priority=True,
            category="compliance",
            display_order=6
        )
    
    def _format_kpi_response(self, result: KpiCalculationResult) -> Dict[str, Any]:
        """Format KPI result for API response."""
        kpi_def = get_kpi_definition(result.kpi_id)
        
        return {
            'id': result.kpi_id,
            'title': result.title,
            'description': result.description,
            'currentValue': result.current_value,
            'previousValue': result.previous_value,
            'changePercent': result.change_percent,
            'trend': result.trend,
            'format': result.format_type,
            'unit': result.unit,
            'chartType': kpi_def.get('chart_type', 'line'),
            'chartData': result.chart_data or [],
            'alert': result.alert,
            'isPriority': result.is_priority,
            'order': result.display_order,
            'category': result.category,
            'frequency': kpi_def.get('frequency', 'daily')
        }
    
    # REMOVED: Client validation methods
    # L2M is the DataHero4 client, not a database record
    # We analyze L2M's business data directly without validation
