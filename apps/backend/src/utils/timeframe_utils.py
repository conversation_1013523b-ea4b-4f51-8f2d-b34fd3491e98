"""
Timeframe utilities for KPI calculations.
Converts frontend timeframe values to database intervals.
"""

import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

def convert_to_interval(timeframe: str) -> str:
    """
    Convert timeframe string to PostgreSQL interval.
    
    Args:
        timeframe: Frontend timeframe value ('1d', 'week', 'month', 'quarter')
        
    Returns:
        PostgreSQL interval string
    """
    timeframe_mapping = {
        '1d': '1 day',
        'today': '1 day',
        'week': '7 days',
        '7d': '7 days',
        'month': '30 days',
        '30d': '30 days',
        'quarter': '90 days',
        '90d': '90 days'
    }
    
    interval = timeframe_mapping.get(timeframe, '7 days')
    logger.info(f"🕐 Timeframe '{timeframe}' converted to interval '{interval}'")
    return interval

def convert_to_days(timeframe: str) -> int:
    """
    Convert timeframe string to number of days.
    
    Args:
        timeframe: Frontend timeframe value
        
    Returns:
        Number of days
    """
    days_mapping = {
        '1d': 1,
        'today': 1,
        'week': 7,
        '7d': 7,
        'month': 30,
        '30d': 30,
        'quarter': 90,
        '90d': 90
    }
    
    return days_mapping.get(timeframe, 7)

def get_timeframe_label(timeframe: str) -> str:
    """
    Get human-readable label for timeframe.
    
    Args:
        timeframe: Frontend timeframe value
        
    Returns:
        Human-readable label
    """
    label_mapping = {
        '1d': 'Hoje',
        'today': 'Hoje',
        'week': 'Últimos 7 dias',
        '7d': 'Últimos 7 dias',
        'month': 'Últimos 30 dias',
        '30d': 'Últimos 30 dias',
        'quarter': 'Últimos 90 dias',
        '90d': 'Últimos 90 dias'
    }
    
    return label_mapping.get(timeframe, 'Período personalizado') 