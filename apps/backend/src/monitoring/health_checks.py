"""
Production Health Checks - DataHero4 Week 7
==========================================

Sistema robusto de health checks para produção com verificações reais
de banco de dados, cache, APIs externas e recursos do sistema.

NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Features:
- Database connectivity checks
- Redis cache health verification
- External API availability checks
- System resource monitoring
- Service dependency validation
- Real-time status reporting

Author: DataHero4 Team
Date: 2025-01-21
"""

import asyncio
import time
import psutil
import structlog
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

import httpx
import redis
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.learning_db_utils import get_db_manager

logger = structlog.get_logger(__name__)


class HealthStatus(Enum):
    """Health check status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: HealthStatus
    message: str
    duration_ms: float
    timestamp: datetime
    details: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "duration_ms": round(self.duration_ms, 2),
            "timestamp": self.timestamp.isoformat(),
            "details": self.details or {}
        }


class ProductionHealthChecker:
    """
    Sistema de health checks para produção - REAL CHECKS ONLY.
    
    Executa verificações reais de saúde do sistema sem usar mocks
    ou fallbacks. Falha fast quando componentes críticos estão indisponíveis.
    """
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.critical_checks: List[str] = []
        self.warning_thresholds = {
            "memory_percent": 80.0,
            "disk_percent": 80.0,
            "cpu_percent": 80.0,
            "response_time_ms": 1000.0
        }
        self.critical_thresholds = {
            "memory_percent": 90.0,
            "disk_percent": 90.0,
            "cpu_percent": 95.0,
            "response_time_ms": 5000.0
        }
        
        # Register default health checks
        self._register_default_checks()
        
        logger.info("Production health checker initialized")
    
    def _register_default_checks(self):
        """Register default health checks - REAL COMPONENTS ONLY."""
        self.register_check("database", self._check_database, critical=True)
        self.register_check("redis", self._check_redis, critical=False)  # Not critical for core functionality
        self.register_check("memory", self._check_memory, critical=False)
        self.register_check("disk", self._check_disk, critical=False)
        self.register_check("cpu", self._check_cpu, critical=False)
        self.register_check("bcb_api", self._check_bcb_api, critical=False)
    
    def register_check(self, name: str, check_func: Callable, critical: bool = False):
        """Register a health check function."""
        self.checks[name] = check_func
        if critical:
            self.critical_checks.append(name)
        
        logger.info("Health check registered", 
                   check_name=name, 
                   critical=critical)
    
    async def _check_database(self) -> HealthCheckResult:
        """Check database connectivity - REAL CONNECTION ONLY."""
        start_time = time.time()

        try:
            db_manager = get_db_manager()

            with db_manager.get_session() as session:
                # Execute real query
                result = session.execute(text("SELECT 1 as health_check"))
                row = result.fetchone()

                duration_ms = (time.time() - start_time) * 1000

                if row and row[0] == 1:
                    return HealthCheckResult(
                        name="database",
                        status=HealthStatus.HEALTHY,
                        message="Database connection successful",
                        duration_ms=duration_ms,
                        timestamp=datetime.utcnow(),
                        details={"query_result": row[0]}
                    )
                else:
                    return HealthCheckResult(
                        name="database",
                        status=HealthStatus.CRITICAL,
                        message="Database query returned unexpected result",
                        duration_ms=duration_ms,
                        timestamp=datetime.utcnow(),
                        details={"query_result": row[0] if row else None}
                    )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("Database health check failed", error=str(e))

            return HealthCheckResult(
                name="database",
                status=HealthStatus.CRITICAL,
                message=f"Database connection failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_redis(self) -> HealthCheckResult:
        """Check Redis connectivity - REAL CONNECTION ONLY."""
        start_time = time.time()

        try:
            import os
            redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
            redis_client = redis.from_url(redis_url)

            # Test Redis with real operations
            test_key = f"health_check_{int(time.time())}"
            test_value = "health_check_value"

            # Set and get test value
            redis_client.set(test_key, test_value, ex=10)  # Expire in 10 seconds
            retrieved_value = redis_client.get(test_key)
            redis_client.delete(test_key)  # Cleanup

            duration_ms = (time.time() - start_time) * 1000

            if retrieved_value and retrieved_value.decode() == test_value:
                return HealthCheckResult(
                    name="redis",
                    status=HealthStatus.HEALTHY,
                    message="Redis connection and operations successful",
                    duration_ms=duration_ms,
                    timestamp=datetime.utcnow(),
                    details={"test_successful": True}
                )
            else:
                return HealthCheckResult(
                    name="redis",
                    status=HealthStatus.WARNING,  # Not critical for core functionality
                    message="Redis operations failed",
                    duration_ms=duration_ms,
                    timestamp=datetime.utcnow(),
                    details={"test_successful": False, "retrieved_value": retrieved_value}
                )

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("Redis health check failed", error=str(e))

            return HealthCheckResult(
                name="redis",
                status=HealthStatus.WARNING,  # Not critical for core functionality
                message=f"Redis connection failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_memory(self) -> HealthCheckResult:
        """Check system memory usage - REAL SYSTEM METRICS."""
        start_time = time.time()
        
        try:
            memory = psutil.virtual_memory()
            duration_ms = (time.time() - start_time) * 1000
            
            # Determine status based on thresholds
            if memory.percent >= self.critical_thresholds["memory_percent"]:
                status = HealthStatus.CRITICAL
                message = f"Critical memory usage: {memory.percent:.1f}%"
            elif memory.percent >= self.warning_thresholds["memory_percent"]:
                status = HealthStatus.WARNING
                message = f"High memory usage: {memory.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage normal: {memory.percent:.1f}%"
            
            return HealthCheckResult(
                name="memory",
                status=status,
                message=message,
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={
                    "percent": memory.percent,
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_gb": round(memory.used / (1024**3), 2)
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("Memory health check failed", error=str(e))
            
            return HealthCheckResult(
                name="memory",
                status=HealthStatus.UNKNOWN,
                message=f"Memory check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_disk(self) -> HealthCheckResult:
        """Check disk usage - REAL SYSTEM METRICS."""
        start_time = time.time()
        
        try:
            disk = psutil.disk_usage('/')
            duration_ms = (time.time() - start_time) * 1000
            
            # Determine status based on thresholds
            if disk.percent >= self.critical_thresholds["disk_percent"]:
                status = HealthStatus.CRITICAL
                message = f"Critical disk usage: {disk.percent:.1f}%"
            elif disk.percent >= self.warning_thresholds["disk_percent"]:
                status = HealthStatus.WARNING
                message = f"High disk usage: {disk.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk usage normal: {disk.percent:.1f}%"
            
            return HealthCheckResult(
                name="disk",
                status=status,
                message=message,
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={
                    "percent": disk.percent,
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "used_gb": round(disk.used / (1024**3), 2)
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("Disk health check failed", error=str(e))
            
            return HealthCheckResult(
                name="disk",
                status=HealthStatus.UNKNOWN,
                message=f"Disk check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_cpu(self) -> HealthCheckResult:
        """Check CPU usage - REAL SYSTEM METRICS."""
        start_time = time.time()
        
        try:
            # Get CPU usage over 1 second interval for accuracy
            cpu_percent = psutil.cpu_percent(interval=1)
            duration_ms = (time.time() - start_time) * 1000
            
            # Determine status based on thresholds
            if cpu_percent >= self.critical_thresholds["cpu_percent"]:
                status = HealthStatus.CRITICAL
                message = f"Critical CPU usage: {cpu_percent:.1f}%"
            elif cpu_percent >= self.warning_thresholds["cpu_percent"]:
                status = HealthStatus.WARNING
                message = f"High CPU usage: {cpu_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"CPU usage normal: {cpu_percent:.1f}%"
            
            return HealthCheckResult(
                name="cpu",
                status=status,
                message=message,
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={
                    "percent": cpu_percent,
                    "cpu_count": psutil.cpu_count(),
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("CPU health check failed", error=str(e))
            
            return HealthCheckResult(
                name="cpu",
                status=HealthStatus.UNKNOWN,
                message=f"CPU check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def _check_bcb_api(self) -> HealthCheckResult:
        """Check BCB API availability - REAL API CALL."""
        start_time = time.time()
        
        try:
            # Test BCB API with real request
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(
                    "https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/CotacaoMoedaPeriodo(moeda=@moeda,dataInicial=@dataInicial,dataFinalCotacao=@dataFinalCotacao)?@moeda='USD'&@dataInicial='01-01-2024'&@dataFinalCotacao='01-02-2024'&$format=json&$select=cotacaoCompra,cotacaoVenda,dataHoraCotacao"
                )
                
                duration_ms = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    data = response.json()
                    if "value" in data and len(data["value"]) > 0:
                        return HealthCheckResult(
                            name="bcb_api",
                            status=HealthStatus.HEALTHY,
                            message="BCB API responding normally",
                            duration_ms=duration_ms,
                            timestamp=datetime.utcnow(),
                            details={
                                "status_code": response.status_code,
                                "response_size": len(data["value"])
                            }
                        )
                    else:
                        return HealthCheckResult(
                            name="bcb_api",
                            status=HealthStatus.WARNING,
                            message="BCB API returned empty data",
                            duration_ms=duration_ms,
                            timestamp=datetime.utcnow(),
                            details={"status_code": response.status_code, "data": data}
                        )
                else:
                    return HealthCheckResult(
                        name="bcb_api",
                        status=HealthStatus.WARNING,
                        message=f"BCB API returned status {response.status_code}",
                        duration_ms=duration_ms,
                        timestamp=datetime.utcnow(),
                        details={"status_code": response.status_code}
                    )
                    
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error("BCB API health check failed", error=str(e))
            
            return HealthCheckResult(
                name="bcb_api",
                status=HealthStatus.WARNING,  # Not critical for core functionality
                message=f"BCB API check failed: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def run_check(self, check_name: str) -> HealthCheckResult:
        """Run a specific health check - FAIL FAST if not found."""
        if check_name not in self.checks:
            raise ValueError(f"Health check '{check_name}' not found - FAIL FAST")
        
        try:
            result = await self.checks[check_name]()
            logger.info("Health check completed", 
                       check_name=check_name,
                       status=result.status.value,
                       duration_ms=result.duration_ms)
            return result
            
        except Exception as e:
            logger.error("Health check failed", check_name=check_name, error=str(e))
            return HealthCheckResult(
                name=check_name,
                status=HealthStatus.UNKNOWN,
                message=f"Health check execution failed: {str(e)}",
                duration_ms=0,
                timestamp=datetime.utcnow(),
                details={"error": str(e)}
            )
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all registered health checks - REAL CHECKS ONLY."""
        start_time = time.time()
        results = {}
        
        # Run all checks concurrently
        tasks = {name: self.run_check(name) for name in self.checks.keys()}
        completed_results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        # Process results
        for name, result in zip(tasks.keys(), completed_results):
            if isinstance(result, Exception):
                results[name] = HealthCheckResult(
                    name=name,
                    status=HealthStatus.UNKNOWN,
                    message=f"Check failed with exception: {str(result)}",
                    duration_ms=0,
                    timestamp=datetime.utcnow(),
                    details={"error": str(result)}
                ).to_dict()
            else:
                results[name] = result.to_dict()
        
        # Determine overall status
        critical_failed = any(
            results[check]["status"] == HealthStatus.CRITICAL.value 
            for check in self.critical_checks 
            if check in results
        )
        
        any_warning = any(
            result["status"] == HealthStatus.WARNING.value 
            for result in results.values()
        )
        
        any_unknown = any(
            result["status"] == HealthStatus.UNKNOWN.value 
            for result in results.values()
        )
        
        if critical_failed:
            overall_status = HealthStatus.CRITICAL
        elif any_unknown:
            overall_status = HealthStatus.UNKNOWN
        elif any_warning:
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY
        
        total_duration = (time.time() - start_time) * 1000
        
        health_summary = {
            "status": overall_status.value,
            "timestamp": datetime.utcnow().isoformat(),
            "total_duration_ms": round(total_duration, 2),
            "checks_count": len(results),
            "critical_checks": self.critical_checks,
            "checks": results
        }
        
        logger.info("All health checks completed",
                   overall_status=overall_status.value,
                   total_duration_ms=total_duration,
                   checks_count=len(results))
        
        return health_summary


# Global health checker instance
health_checker: Optional[ProductionHealthChecker] = None


def get_health_checker() -> ProductionHealthChecker:
    """Get global health checker instance - FAIL FAST if not initialized."""
    global health_checker
    
    if health_checker is None:
        health_checker = ProductionHealthChecker()
    
    return health_checker
