"""
Production Monitoring System - DataHero4 Week 7
===============================================

Sistema completo de monitoramento para produção com métricas reais,
logs estruturados e health checks robustos.

NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Features:
- Prometheus metrics integration
- Structured logging with JSON output
- OpenTelemetry instrumentation
- Real-time performance tracking
- System resource monitoring
- Database health checks
- API endpoint monitoring

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import time
import psutil
import structlog
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_fastapi_instrumentator import Instrumentator
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.instrumentation.fastapi import Fast<PERSON>II<PERSON>rumentor
from opentelemetry.exporter.prometheus import PrometheusMetric<PERSON><PERSON>er
from opentelemetry.sdk.resources import Resource

# Configure structured logging - NO FALLBACKS
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class ProductionMonitoringSystem:
    """
    Sistema de monitoramento para produção - REAL COMPONENTS ONLY.
    
    Integra Prometheus, OpenTelemetry, structured logging e health checks
    sem usar mocks ou fallbacks.
    """
    
    def __init__(self, service_name: str = "datahero4"):
        self.service_name = service_name
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        
        # Initialize OpenTelemetry - FAIL FAST if not configured
        self._setup_opentelemetry()
        
        # Initialize Prometheus metrics
        self._setup_prometheus()
        
        logger.info("Production monitoring system initialized", 
                   service_name=service_name,
                   start_time=self.start_time)
    
    def _setup_opentelemetry(self):
        """Setup OpenTelemetry with real exporters - NO MOCKS."""
        try:
            # Create resource with service information
            resource = Resource.create({
                "service.name": self.service_name,
                "service.version": os.getenv("APP_VERSION", "1.0.0"),
                "deployment.environment": os.getenv("ENVIRONMENT", "production")
            })
            
            # Setup tracing
            trace.set_tracer_provider(TracerProvider(resource=resource))
            self.tracer = trace.get_tracer(__name__)
            
            # Setup metrics with Prometheus exporter
            prometheus_reader = PrometheusMetricReader()
            metrics.set_meter_provider(MeterProvider(
                resource=resource,
                metric_readers=[prometheus_reader]
            ))
            self.meter = metrics.get_meter(__name__)
            
            # Create custom metrics
            self.request_counter = self.meter.create_counter(
                "datahero_requests_total",
                description="Total number of requests"
            )
            
            self.request_duration = self.meter.create_histogram(
                "datahero_request_duration_seconds",
                description="Request duration in seconds"
            )
            
            self.error_counter = self.meter.create_counter(
                "datahero_errors_total",
                description="Total number of errors"
            )
            
            self.system_memory_gauge = self.meter.create_observable_gauge(
                "datahero_system_memory_usage_bytes",
                description="System memory usage in bytes",
                callbacks=[self._get_memory_usage]
            )
            
            self.system_cpu_gauge = self.meter.create_observable_gauge(
                "datahero_system_cpu_usage_percent",
                description="System CPU usage percentage",
                callbacks=[self._get_cpu_usage]
            )
            
            logger.info("OpenTelemetry configured successfully")
            
        except Exception as e:
            logger.error("Failed to setup OpenTelemetry - FAIL FAST", error=str(e))
            raise ValueError(f"OpenTelemetry setup failed: {e}")
    
    def _setup_prometheus(self):
        """Setup Prometheus instrumentator - REAL METRICS ONLY."""
        try:
            self.instrumentator = Instrumentator(
                should_group_status_codes=False,
                should_ignore_untemplated=True,
                should_respect_env_var=True,
                should_instrument_requests_inprogress=True,
                excluded_handlers=["/health", "/metrics"],
                env_var_name="ENABLE_METRICS",
                inprogress_name="datahero_inprogress",
                inprogress_labels=True,
            )
            
            logger.info("Prometheus instrumentator configured")
            
        except Exception as e:
            logger.error("Failed to setup Prometheus - FAIL FAST", error=str(e))
            raise ValueError(f"Prometheus setup failed: {e}")
    
    def _get_memory_usage(self, options):
        """Get real system memory usage - NO MOCKS."""
        try:
            memory = psutil.virtual_memory()
            return [metrics.Observation(memory.used)]
        except Exception as e:
            logger.error("Failed to get memory usage", error=str(e))
            return [metrics.Observation(0)]
    
    def _get_cpu_usage(self, options):
        """Get real system CPU usage - NO MOCKS."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            return [metrics.Observation(cpu_percent)]
        except Exception as e:
            logger.error("Failed to get CPU usage", error=str(e))
            return [metrics.Observation(0)]
    
    def instrument_fastapi(self, app: FastAPI):
        """Instrument FastAPI application with monitoring - REAL ONLY."""
        try:
            # Add Prometheus instrumentation
            self.instrumentator.instrument(app)
            self.instrumentator.expose(app, endpoint="/metrics")
            
            # Add OpenTelemetry instrumentation
            FastAPIInstrumentor.instrument_app(app)
            
            # Add custom middleware
            app.add_middleware(ProductionMonitoringMiddleware, monitoring=self)
            
            logger.info("FastAPI instrumentation completed")
            
        except Exception as e:
            logger.error("Failed to instrument FastAPI - FAIL FAST", error=str(e))
            raise ValueError(f"FastAPI instrumentation failed: {e}")
    
    def log_request(self, request: Request, response: Response, duration: float):
        """Log request with structured data - NO FALLBACKS."""
        self.request_count += 1
        
        # Update metrics
        self.request_counter.add(1, {
            "method": request.method,
            "endpoint": str(request.url.path),
            "status_code": str(response.status_code)
        })
        
        self.request_duration.record(duration, {
            "method": request.method,
            "endpoint": str(request.url.path)
        })
        
        # Log structured data
        log_data = {
            "event": "request_completed",
            "method": request.method,
            "path": str(request.url.path),
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "user_agent": request.headers.get("user-agent"),
            "client_ip": request.client.host if request.client else None,
            "request_id": response.headers.get("x-request-id"),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if response.status_code >= 400:
            self.error_count += 1
            self.error_counter.add(1, {
                "status_code": str(response.status_code),
                "endpoint": str(request.url.path)
            })
            logger.error("Request failed", **log_data)
        else:
            logger.info("Request completed", **log_data)
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log error with structured data - FAIL FAST."""
        self.error_count += 1
        
        error_data = {
            "event": "error_occurred",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.utcnow().isoformat(),
            "context": context or {}
        }
        
        logger.error("Application error", **error_data, exc_info=True)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status - REAL CHECKS ONLY."""
        try:
            uptime = time.time() - self.start_time
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # System health checks - FAIL FAST if critical
            memory_critical = memory.percent > 90
            disk_critical = disk.percent > 90
            error_rate = (self.error_count / max(self.request_count, 1)) * 100
            error_rate_critical = error_rate > 10  # 10% error rate threshold
            
            overall_healthy = not (memory_critical or disk_critical or error_rate_critical)
            
            health_data = {
                "status": "healthy" if overall_healthy else "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_seconds": round(uptime, 2),
                "service_name": self.service_name,
                "version": os.getenv("APP_VERSION", "1.0.0"),
                "environment": os.getenv("ENVIRONMENT", "production"),
                "metrics": {
                    "total_requests": self.request_count,
                    "total_errors": self.error_count,
                    "error_rate_percent": round(error_rate, 2),
                    "memory_usage_percent": memory.percent,
                    "disk_usage_percent": disk.percent,
                    "cpu_count": psutil.cpu_count()
                },
                "checks": {
                    "memory": "critical" if memory_critical else "healthy",
                    "disk": "critical" if disk_critical else "healthy",
                    "error_rate": "critical" if error_rate_critical else "healthy"
                }
            }
            
            if not overall_healthy:
                logger.warning("Health check failed", **health_data)
            
            return health_data
            
        except Exception as e:
            logger.error("Health check failed - FAIL FAST", error=str(e))
            raise ValueError(f"Health check failed: {e}")


class ProductionMonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware para monitoramento de requests - REAL ONLY."""
    
    def __init__(self, app, monitoring: ProductionMonitoringSystem):
        super().__init__(app)
        self.monitoring = monitoring
    
    async def dispatch(self, request: Request, call_next):
        """Process request with monitoring - NO FALLBACKS."""
        start_time = time.time()
        
        # Add request ID for tracing
        request_id = f"req_{int(start_time * 1000000)}"
        
        try:
            # Process request
            response = await call_next(request)
            
            # Add request ID to response
            response.headers["x-request-id"] = request_id
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log request
            self.monitoring.log_request(request, response, duration)
            
            return response
            
        except Exception as e:
            # Log error and re-raise - FAIL FAST
            duration = time.time() - start_time
            self.monitoring.log_error(e, {
                "request_id": request_id,
                "method": request.method,
                "path": str(request.url.path),
                "duration_ms": round(duration * 1000, 2)
            })
            raise


# Global monitoring instance
monitoring_system: Optional[ProductionMonitoringSystem] = None


def get_monitoring_system() -> ProductionMonitoringSystem:
    """Get global monitoring system instance - FAIL FAST if not initialized."""
    global monitoring_system
    
    if monitoring_system is None:
        raise ValueError("Monitoring system not initialized - FAIL FAST")
    
    return monitoring_system


def initialize_monitoring(service_name: str = "datahero4") -> ProductionMonitoringSystem:
    """Initialize global monitoring system - REAL COMPONENTS ONLY."""
    global monitoring_system
    
    if monitoring_system is not None:
        logger.warning("Monitoring system already initialized")
        return monitoring_system
    
    try:
        monitoring_system = ProductionMonitoringSystem(service_name)
        logger.info("Global monitoring system initialized", service_name=service_name)
        return monitoring_system
        
    except Exception as e:
        logger.error("Failed to initialize monitoring system - FAIL FAST", error=str(e))
        raise


def setup_production_monitoring(app: FastAPI, service_name: str = "datahero4"):
    """Setup complete production monitoring for FastAPI app - NO MOCKS."""
    try:
        # Initialize monitoring system
        monitoring = initialize_monitoring(service_name)
        
        # Instrument FastAPI
        monitoring.instrument_fastapi(app)
        
        logger.info("Production monitoring setup completed", service_name=service_name)
        return monitoring
        
    except Exception as e:
        logger.error("Failed to setup production monitoring - FAIL FAST", error=str(e))
        raise
