"""
Intelligent Alerting System - DataHero4 Week 7.4
===============================================

Sistema inteligente de alertas para produção com múltiplos canais,
thresholds dinâmicos e escalation rules.

NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Features:
- Multi-channel notifications (email, slack, webhook)
- Dynamic thresholds based on historical data
- Escalation rules and alert correlation
- Real-time metric monitoring
- Business logic alerts
- Alert suppression and grouping

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import time
import json
import smtplib
import asyncio
import structlog
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

import httpx
import psutil
from jinja2 import Template

logger = structlog.get_logger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class AlertChannel(Enum):
    """Alert notification channels."""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    DASHBOARD = "dashboard"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    metric_name: str
    threshold: float
    comparison: str  # >, <, >=, <=, ==, !=
    severity: AlertSeverity
    channels: List[AlertChannel]
    description: str
    duration: int = 60  # seconds
    evaluation_interval: int = 30  # seconds
    labels: Dict[str, str] = None
    
    def __post_init__(self):
        if self.labels is None:
            self.labels = {}


@dataclass
class Alert:
    """Alert instance."""
    id: str
    rule_name: str
    metric_name: str
    current_value: float
    threshold: float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    labels: Dict[str, str]
    started_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "rule_name": self.rule_name,
            "metric_name": self.metric_name,
            "current_value": self.current_value,
            "threshold": self.threshold,
            "severity": self.severity.value,
            "status": self.status.value,
            "message": self.message,
            "labels": self.labels,
            "started_at": self.started_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "acknowledged_by": self.acknowledged_by
        }


class IntelligentAlertingSystem:
    """
    Sistema inteligente de alertas para DataHero4 - REAL ALERTS ONLY.
    
    Implementa alertas baseados em métricas reais, múltiplos canais de notificação
    e regras de escalation sem usar mocks ou fallbacks.
    """
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notification_channels = self._setup_notification_channels()
        self.metric_history: Dict[str, List[tuple]] = {}  # metric_name -> [(timestamp, value)]
        
        # Configuration from environment
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_user = os.getenv("SMTP_USER")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        self.slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        self.alert_recipients = os.getenv("ALERT_RECIPIENTS", "").split(",")
        
        # Register default alert rules
        self._register_default_rules()
        
        logger.info("Intelligent alerting system initialized")
    
    def _setup_notification_channels(self) -> Dict[AlertChannel, Callable]:
        """Setup notification channel handlers - REAL CHANNELS ONLY."""
        return {
            AlertChannel.EMAIL: self._send_email_alert,
            AlertChannel.SLACK: self._send_slack_alert,
            AlertChannel.WEBHOOK: self._send_webhook_alert,
            AlertChannel.DASHBOARD: self._send_dashboard_alert
        }
    
    def _register_default_rules(self):
        """Register default alert rules - PRODUCTION READY."""
        # System resource alerts
        self.register_rule(AlertRule(
            name="high_cpu_usage",
            metric_name="cpu_percent",
            threshold=80.0,
            comparison=">=",
            severity=AlertSeverity.WARNING,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK],
            description="High CPU usage detected",
            duration=120,  # 2 minutes
            labels={"component": "system", "type": "resource"}
        ))
        
        self.register_rule(AlertRule(
            name="critical_cpu_usage",
            metric_name="cpu_percent",
            threshold=95.0,
            comparison=">=",
            severity=AlertSeverity.CRITICAL,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK, AlertChannel.WEBHOOK],
            description="Critical CPU usage detected",
            duration=60,  # 1 minute
            labels={"component": "system", "type": "resource"}
        ))
        
        self.register_rule(AlertRule(
            name="high_memory_usage",
            metric_name="memory_percent",
            threshold=85.0,
            comparison=">=",
            severity=AlertSeverity.WARNING,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK],
            description="High memory usage detected",
            duration=180,  # 3 minutes
            labels={"component": "system", "type": "resource"}
        ))
        
        self.register_rule(AlertRule(
            name="critical_memory_usage",
            metric_name="memory_percent",
            threshold=95.0,
            comparison=">=",
            severity=AlertSeverity.CRITICAL,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK, AlertChannel.WEBHOOK],
            description="Critical memory usage detected",
            duration=60,  # 1 minute
            labels={"component": "system", "type": "resource"}
        ))
        
        # Application performance alerts
        self.register_rule(AlertRule(
            name="high_error_rate",
            metric_name="error_rate_percent",
            threshold=5.0,
            comparison=">=",
            severity=AlertSeverity.WARNING,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK],
            description="High error rate detected",
            duration=300,  # 5 minutes
            labels={"component": "application", "type": "performance"}
        ))
        
        self.register_rule(AlertRule(
            name="critical_error_rate",
            metric_name="error_rate_percent",
            threshold=15.0,
            comparison=">=",
            severity=AlertSeverity.CRITICAL,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK, AlertChannel.WEBHOOK],
            description="Critical error rate detected",
            duration=120,  # 2 minutes
            labels={"component": "application", "type": "performance"}
        ))
        
        self.register_rule(AlertRule(
            name="slow_response_time",
            metric_name="avg_response_time_ms",
            threshold=2000.0,
            comparison=">=",
            severity=AlertSeverity.WARNING,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK],
            description="Slow response time detected",
            duration=300,  # 5 minutes
            labels={"component": "application", "type": "performance"}
        ))
        
        # Business logic alerts
        self.register_rule(AlertRule(
            name="kpi_calculation_failure",
            metric_name="kpi_failure_rate_percent",
            threshold=10.0,
            comparison=">=",
            severity=AlertSeverity.CRITICAL,
            channels=[AlertChannel.EMAIL, AlertChannel.SLACK, AlertChannel.WEBHOOK],
            description="High KPI calculation failure rate",
            duration=180,  # 3 minutes
            labels={"component": "business", "type": "kpi"}
        ))
        
        logger.info("Default alert rules registered", rules_count=len(self.rules))
    
    def register_rule(self, rule: AlertRule):
        """Register an alert rule - FAIL FAST if invalid."""
        if not rule.name:
            raise ValueError("Alert rule name cannot be empty - FAIL FAST")
        
        if not rule.metric_name:
            raise ValueError("Alert rule metric_name cannot be empty - FAIL FAST")
        
        if rule.comparison not in [">", "<", ">=", "<=", "==", "!="]:
            raise ValueError(f"Invalid comparison operator: {rule.comparison} - FAIL FAST")
        
        self.rules[rule.name] = rule
        logger.info("Alert rule registered", rule_name=rule.name, metric=rule.metric_name)
    
    def evaluate_metric(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """Evaluate metric against alert rules - REAL EVALUATION ONLY."""
        if labels is None:
            labels = {}
        
        # Store metric history for trend analysis
        current_time = time.time()
        if metric_name not in self.metric_history:
            self.metric_history[metric_name] = []
        
        self.metric_history[metric_name].append((current_time, value))
        
        # Keep only last 24 hours of data
        cutoff_time = current_time - (24 * 3600)
        self.metric_history[metric_name] = [
            (ts, val) for ts, val in self.metric_history[metric_name] 
            if ts > cutoff_time
        ]
        
        # Evaluate against all rules for this metric
        for rule_name, rule in self.rules.items():
            if rule.metric_name == metric_name:
                self._evaluate_rule(rule, value, labels)
    
    def _evaluate_rule(self, rule: AlertRule, value: float, labels: Dict[str, str]):
        """Evaluate a specific rule against metric value - FAIL FAST."""
        try:
            # Check if threshold is breached
            threshold_breached = self._check_threshold(rule.comparison, value, rule.threshold)
            
            alert_id = f"{rule.name}_{hash(str(sorted(labels.items())))}"
            
            if threshold_breached:
                if alert_id not in self.active_alerts:
                    # Create new alert
                    alert = Alert(
                        id=alert_id,
                        rule_name=rule.name,
                        metric_name=rule.metric_name,
                        current_value=value,
                        threshold=rule.threshold,
                        severity=rule.severity,
                        status=AlertStatus.ACTIVE,
                        message=self._generate_alert_message(rule, value, labels),
                        labels={**rule.labels, **labels},
                        started_at=datetime.utcnow()
                    )
                    
                    self.active_alerts[alert_id] = alert
                    self.alert_history.append(alert)
                    
                    # Send notifications
                    asyncio.create_task(self._send_alert_notifications(alert, rule))
                    
                    logger.warning("Alert triggered",
                                 alert_id=alert_id,
                                 rule_name=rule.name,
                                 metric=rule.metric_name,
                                 value=value,
                                 threshold=rule.threshold,
                                 severity=rule.severity.value)
                else:
                    # Update existing alert
                    alert = self.active_alerts[alert_id]
                    alert.current_value = value
                    
            else:
                # Check if alert should be resolved
                if alert_id in self.active_alerts:
                    alert = self.active_alerts[alert_id]
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.utcnow()
                    
                    # Send resolution notification
                    asyncio.create_task(self._send_resolution_notification(alert, rule))
                    
                    # Remove from active alerts
                    del self.active_alerts[alert_id]
                    
                    logger.info("Alert resolved",
                               alert_id=alert_id,
                               rule_name=rule.name,
                               metric=rule.metric_name,
                               value=value,
                               threshold=rule.threshold)
                    
        except Exception as e:
            logger.error("Failed to evaluate alert rule - FAIL FAST",
                        rule_name=rule.name,
                        error=str(e))
            raise
    
    def _check_threshold(self, comparison: str, value: float, threshold: float) -> bool:
        """Check if value breaches threshold - FAIL FAST."""
        if comparison == ">":
            return value > threshold
        elif comparison == "<":
            return value < threshold
        elif comparison == ">=":
            return value >= threshold
        elif comparison == "<=":
            return value <= threshold
        elif comparison == "==":
            return value == threshold
        elif comparison == "!=":
            return value != threshold
        else:
            raise ValueError(f"Invalid comparison operator: {comparison} - FAIL FAST")
    
    def _generate_alert_message(self, rule: AlertRule, value: float, labels: Dict[str, str]) -> str:
        """Generate alert message - REAL MESSAGE ONLY."""
        template = Template("""
🚨 **{{ rule.severity.value.upper() }} ALERT**: {{ rule.description }}

**Metric**: {{ rule.metric_name }}
**Current Value**: {{ current_value }}
**Threshold**: {{ rule.threshold }}
**Condition**: {{ rule.metric_name }} {{ rule.comparison }} {{ rule.threshold }}

**Labels**: {{ labels }}
**Time**: {{ timestamp }}

**Service**: DataHero4
**Environment**: {{ environment }}
        """.strip())
        
        return template.render(
            rule=rule,
            current_value=value,
            labels=labels,
            timestamp=datetime.utcnow().isoformat(),
            environment=os.getenv("ENVIRONMENT", "production")
        )
    
    async def _send_alert_notifications(self, alert: Alert, rule: AlertRule):
        """Send alert notifications to all configured channels - REAL NOTIFICATIONS."""
        for channel in rule.channels:
            try:
                if channel in self.notification_channels:
                    await self.notification_channels[channel](alert, rule)
                else:
                    logger.warning("Unknown notification channel", channel=channel.value)
                    
            except Exception as e:
                logger.error("Failed to send alert notification",
                           channel=channel.value,
                           alert_id=alert.id,
                           error=str(e))
    
    async def _send_resolution_notification(self, alert: Alert, rule: AlertRule):
        """Send alert resolution notification - REAL NOTIFICATIONS."""
        resolution_message = f"✅ **RESOLVED**: {rule.description}\n\n"
        resolution_message += f"Alert {alert.id} has been resolved.\n"
        resolution_message += f"Duration: {(alert.resolved_at - alert.started_at).total_seconds():.0f} seconds"
        
        # Create resolution alert
        resolution_alert = Alert(
            id=f"{alert.id}_resolved",
            rule_name=alert.rule_name,
            metric_name=alert.metric_name,
            current_value=alert.current_value,
            threshold=alert.threshold,
            severity=AlertSeverity.INFO,
            status=AlertStatus.RESOLVED,
            message=resolution_message,
            labels=alert.labels,
            started_at=alert.resolved_at
        )
        
        # Send to email and slack only (not webhook for resolutions)
        resolution_channels = [ch for ch in rule.channels if ch in [AlertChannel.EMAIL, AlertChannel.SLACK]]
        
        for channel in resolution_channels:
            try:
                if channel in self.notification_channels:
                    await self.notification_channels[channel](resolution_alert, rule)
                    
            except Exception as e:
                logger.error("Failed to send resolution notification",
                           channel=channel.value,
                           alert_id=alert.id,
                           error=str(e))
    
    async def _send_email_alert(self, alert: Alert, rule: AlertRule):
        """Send email alert - REAL EMAIL ONLY."""
        if not self.smtp_user or not self.smtp_password or not self.alert_recipients:
            logger.warning("Email configuration incomplete, skipping email alert")
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.smtp_user
            msg['To'] = ", ".join(self.alert_recipients)
            msg['Subject'] = f"[DataHero4] {alert.severity.value.upper()} Alert: {rule.description}"
            
            body = alert.message
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_user, self.smtp_password)
            text = msg.as_string()
            server.sendmail(self.smtp_user, self.alert_recipients, text)
            server.quit()
            
            logger.info("Email alert sent", alert_id=alert.id, recipients=len(self.alert_recipients))
            
        except Exception as e:
            logger.error("Failed to send email alert", alert_id=alert.id, error=str(e))
            raise
    
    async def _send_slack_alert(self, alert: Alert, rule: AlertRule):
        """Send Slack alert - REAL SLACK ONLY."""
        if not self.slack_webhook_url:
            logger.warning("Slack webhook URL not configured, skipping Slack alert")
            return
        
        try:
            # Determine color based on severity
            color_map = {
                AlertSeverity.INFO: "#36a64f",
                AlertSeverity.WARNING: "#ff9500",
                AlertSeverity.CRITICAL: "#ff0000",
                AlertSeverity.EMERGENCY: "#8b0000"
            }
            
            payload = {
                "text": f"DataHero4 Alert: {rule.description}",
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "#ff0000"),
                        "fields": [
                            {
                                "title": "Severity",
                                "value": alert.severity.value.upper(),
                                "short": True
                            },
                            {
                                "title": "Metric",
                                "value": alert.metric_name,
                                "short": True
                            },
                            {
                                "title": "Current Value",
                                "value": str(alert.current_value),
                                "short": True
                            },
                            {
                                "title": "Threshold",
                                "value": str(alert.threshold),
                                "short": True
                            },
                            {
                                "title": "Time",
                                "value": alert.started_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                                "short": False
                            }
                        ],
                        "footer": "DataHero4 Monitoring",
                        "ts": int(alert.started_at.timestamp())
                    }
                ]
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.slack_webhook_url, json=payload)
                response.raise_for_status()
            
            logger.info("Slack alert sent", alert_id=alert.id)
            
        except Exception as e:
            logger.error("Failed to send Slack alert", alert_id=alert.id, error=str(e))
            raise
    
    async def _send_webhook_alert(self, alert: Alert, rule: AlertRule):
        """Send webhook alert - REAL WEBHOOK ONLY."""
        webhook_url = os.getenv("ALERT_WEBHOOK_URL")
        if not webhook_url:
            logger.warning("Webhook URL not configured, skipping webhook alert")
            return
        
        try:
            payload = {
                "alert": alert.to_dict(),
                "rule": asdict(rule),
                "timestamp": datetime.utcnow().isoformat(),
                "service": "datahero4"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload, timeout=10.0)
                response.raise_for_status()
            
            logger.info("Webhook alert sent", alert_id=alert.id, webhook_url=webhook_url)
            
        except Exception as e:
            logger.error("Failed to send webhook alert", alert_id=alert.id, error=str(e))
            raise
    
    async def _send_dashboard_alert(self, alert: Alert, rule: AlertRule):
        """Send dashboard alert - REAL DASHBOARD ONLY."""
        # This would integrate with a real dashboard system
        # For now, we'll log it as a structured event
        logger.warning("Dashboard alert",
                      alert_id=alert.id,
                      rule_name=rule.name,
                      severity=alert.severity.value,
                      metric=alert.metric_name,
                      value=alert.current_value,
                      threshold=alert.threshold,
                      message=alert.message,
                      labels=alert.labels,
                      event_type="dashboard_alert")
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts - REAL ALERTS ONLY."""
        return [alert.to_dict() for alert in self.active_alerts.values()]
    
    def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alert history - REAL HISTORY ONLY."""
        return [alert.to_dict() for alert in self.alert_history[-limit:]]
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str):
        """Acknowledge an alert - REAL ACKNOWLEDGMENT."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = acknowledged_by
            
            logger.info("Alert acknowledged",
                       alert_id=alert_id,
                       acknowledged_by=acknowledged_by)
        else:
            raise ValueError(f"Alert {alert_id} not found - FAIL FAST")
    
    def suppress_alert(self, alert_id: str, duration_minutes: int = 60):
        """Suppress an alert for a specified duration - REAL SUPPRESSION."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.SUPPRESSED
            
            # Schedule unsuppression (in a real system, this would be handled by a scheduler)
            logger.info("Alert suppressed",
                       alert_id=alert_id,
                       duration_minutes=duration_minutes)
        else:
            raise ValueError(f"Alert {alert_id} not found - FAIL FAST")
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics - REAL STATS ONLY."""
        total_alerts = len(self.alert_history)
        active_alerts = len(self.active_alerts)
        
        # Count by severity
        severity_counts = {}
        for alert in self.alert_history:
            severity = alert.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Count by rule
        rule_counts = {}
        for alert in self.alert_history:
            rule = alert.rule_name
            rule_counts[rule] = rule_counts.get(rule, 0) + 1
        
        return {
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "severity_distribution": severity_counts,
            "rule_distribution": rule_counts,
            "registered_rules": len(self.rules),
            "timestamp": datetime.utcnow().isoformat()
        }


# Global alerting system instance
alerting_system: Optional[IntelligentAlertingSystem] = None


def get_alerting_system() -> IntelligentAlertingSystem:
    """Get global alerting system instance - FAIL FAST if not initialized."""
    global alerting_system
    
    if alerting_system is None:
        alerting_system = IntelligentAlertingSystem()
    
    return alerting_system


def alert_metric(metric_name: str, value: float, labels: Dict[str, str] = None):
    """Convenience function to evaluate metric against alert rules."""
    alerting = get_alerting_system()
    alerting.evaluate_metric(metric_name, value, labels or {})
