"""
Advanced Observability System - DataHero4 Week 7.3
==================================================

Sistema completo de observabilidade com APM, distributed tracing,
error tracking e performance monitoring para produção.

NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Features:
- OpenTelemetry distributed tracing
- Custom metrics and spans
- Error tracking and correlation
- Performance monitoring
- Business metrics tracking
- Real-time alerting integration

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import time
import uuid
import structlog
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from contextlib import contextmanager
from functools import wraps

from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider, Span
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.trace import Status, StatusCode
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor

logger = structlog.get_logger(__name__)


class DataHeroObservabilitySystem:
    """
    Sistema avançado de observabilidade para DataHero4 - REAL COMPONENTS ONLY.
    
    Implementa distributed tracing, APM, error tracking e performance monitoring
    sem usar mocks ou fallbacks.
    """
    
    def __init__(self, service_name: str = "datahero4"):
        self.service_name = service_name
        self.tracer = trace.get_tracer(__name__)
        self.meter = metrics.get_meter(__name__)
        
        # Initialize custom metrics
        self._setup_custom_metrics()
        
        # Initialize instrumentations
        self._setup_instrumentations()
        
        logger.info("Advanced observability system initialized", 
                   service_name=service_name)
    
    def _setup_custom_metrics(self):
        """Setup custom business and technical metrics - REAL METRICS ONLY."""
        try:
            # Business metrics
            self.kpi_calculation_counter = self.meter.create_counter(
                "datahero_kpi_calculations_total",
                description="Total number of KPI calculations performed"
            )
            
            self.kpi_calculation_duration = self.meter.create_histogram(
                "datahero_kpi_calculation_duration_seconds",
                description="Duration of KPI calculations in seconds"
            )
            
            self.profile_detection_counter = self.meter.create_counter(
                "datahero_profile_detections_total",
                description="Total number of profile detections"
            )
            
            self.routing_decisions_counter = self.meter.create_counter(
                "datahero_routing_decisions_total",
                description="Total number of routing decisions by layer"
            )
            
            self.cache_operations_counter = self.meter.create_counter(
                "datahero_cache_operations_total",
                description="Total number of cache operations"
            )
            
            # Technical metrics
            self.database_query_duration = self.meter.create_histogram(
                "datahero_database_query_duration_seconds",
                description="Duration of database queries in seconds"
            )
            
            self.external_api_calls_counter = self.meter.create_counter(
                "datahero_external_api_calls_total",
                description="Total number of external API calls"
            )
            
            self.external_api_duration = self.meter.create_histogram(
                "datahero_external_api_duration_seconds",
                description="Duration of external API calls in seconds"
            )
            
            self.error_counter = self.meter.create_counter(
                "datahero_errors_total",
                description="Total number of errors by type and component"
            )
            
            logger.info("Custom metrics initialized successfully")
            
        except Exception as e:
            logger.error("Failed to setup custom metrics - FAIL FAST", error=str(e))
            raise ValueError(f"Custom metrics setup failed: {e}")
    
    def _setup_instrumentations(self):
        """Setup automatic instrumentations - REAL ONLY."""
        try:
            # Instrument HTTP requests
            RequestsInstrumentor().instrument()
            
            # Instrument SQLAlchemy
            SQLAlchemyInstrumentor().instrument()
            
            # Instrument Redis
            RedisInstrumentor().instrument()
            
            logger.info("Automatic instrumentations configured")
            
        except Exception as e:
            logger.error("Failed to setup instrumentations", error=str(e))
            # Don't fail hard for instrumentations in development
            if os.getenv("ENVIRONMENT") == "production":
                raise
    
    @contextmanager
    def trace_operation(self, operation_name: str, **attributes):
        """Context manager for tracing operations - REAL TRACING ONLY."""
        span_name = f"datahero.{operation_name}"
        
        with self.tracer.start_as_current_span(span_name) as span:
            # Add custom attributes
            for key, value in attributes.items():
                span.set_attribute(f"datahero.{key}", str(value))
            
            # Add common attributes
            span.set_attribute("service.name", self.service_name)
            span.set_attribute("operation.name", operation_name)
            span.set_attribute("timestamp", datetime.utcnow().isoformat())
            
            start_time = time.time()
            
            try:
                yield span
                
                # Mark as successful
                span.set_status(Status(StatusCode.OK))
                
            except Exception as e:
                # Record error in span
                span.set_status(Status(StatusCode.ERROR, str(e)))
                span.record_exception(e)
                
                # Update error metrics
                self.error_counter.add(1, {
                    "operation": operation_name,
                    "error_type": type(e).__name__,
                    "service": self.service_name
                })
                
                logger.error("Operation failed with tracing", 
                           operation=operation_name,
                           error=str(e),
                           span_id=span.get_span_context().span_id,
                           trace_id=span.get_span_context().trace_id)
                
                raise
                
            finally:
                # Record duration
                duration = time.time() - start_time
                span.set_attribute("duration_seconds", duration)
    
    def trace_kpi_calculation(self, kpi_id: str, profile_type: str, layer: str):
        """Decorator for tracing KPI calculations - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "kpi_calculation",
                    kpi_id=kpi_id,
                    profile_type=profile_type,
                    routing_layer=layer
                ) as span:
                    start_time = time.time()
                    
                    try:
                        result = func(*args, **kwargs)
                        
                        # Record success metrics
                        duration = time.time() - start_time
                        
                        self.kpi_calculation_counter.add(1, {
                            "kpi_id": kpi_id,
                            "profile_type": profile_type,
                            "layer": layer,
                            "status": "success"
                        })
                        
                        self.kpi_calculation_duration.record(duration, {
                            "kpi_id": kpi_id,
                            "profile_type": profile_type,
                            "layer": layer
                        })
                        
                        # Add result metadata to span
                        if isinstance(result, dict):
                            span.set_attribute("result.has_data", bool(result.get("data")))
                            span.set_attribute("result.cache_hit", bool(result.get("cache_hit")))
                            if "value" in result:
                                span.set_attribute("result.value", str(result["value"]))
                        
                        return result
                        
                    except Exception as e:
                        # Record failure metrics
                        self.kpi_calculation_counter.add(1, {
                            "kpi_id": kpi_id,
                            "profile_type": profile_type,
                            "layer": layer,
                            "status": "error"
                        })
                        
                        raise
                        
            return wrapper
        return decorator
    
    def trace_profile_detection(self, user_id: str):
        """Decorator for tracing profile detection - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "profile_detection",
                    user_id=user_id
                ) as span:
                    start_time = time.time()
                    
                    try:
                        result = func(*args, **kwargs)
                        
                        # Record metrics
                        detected_profile = result.get("profile", "unknown")
                        confidence = result.get("confidence", 0.0)
                        
                        self.profile_detection_counter.add(1, {
                            "user_id": user_id,
                            "detected_profile": detected_profile,
                            "confidence_range": self._get_confidence_range(confidence)
                        })
                        
                        # Add result to span
                        span.set_attribute("detected_profile", detected_profile)
                        span.set_attribute("confidence", confidence)
                        span.set_attribute("detection_method", result.get("method", "unknown"))
                        
                        return result
                        
                    except Exception as e:
                        self.profile_detection_counter.add(1, {
                            "user_id": user_id,
                            "detected_profile": "error",
                            "confidence_range": "error"
                        })
                        raise
                        
            return wrapper
        return decorator
    
    def trace_routing_decision(self, kpi_id: str, profile_type: str):
        """Decorator for tracing routing decisions - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "routing_decision",
                    kpi_id=kpi_id,
                    profile_type=profile_type
                ) as span:
                    try:
                        result = func(*args, **kwargs)
                        
                        # Extract routing decision
                        chosen_layer = result.get("layer", "unknown")
                        routing_reason = result.get("reason", "unknown")
                        
                        # Record metrics
                        self.routing_decisions_counter.add(1, {
                            "kpi_id": kpi_id,
                            "profile_type": profile_type,
                            "chosen_layer": chosen_layer,
                            "routing_reason": routing_reason
                        })
                        
                        # Add to span
                        span.set_attribute("chosen_layer", chosen_layer)
                        span.set_attribute("routing_reason", routing_reason)
                        span.set_attribute("cache_available", bool(result.get("cache_available")))
                        
                        return result
                        
                    except Exception as e:
                        self.routing_decisions_counter.add(1, {
                            "kpi_id": kpi_id,
                            "profile_type": profile_type,
                            "chosen_layer": "error",
                            "routing_reason": "error"
                        })
                        raise
                        
            return wrapper
        return decorator
    
    def trace_database_query(self, query_type: str, table_name: str = None):
        """Decorator for tracing database queries - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "database_query",
                    query_type=query_type,
                    table_name=table_name or "unknown"
                ) as span:
                    start_time = time.time()
                    
                    try:
                        result = func(*args, **kwargs)
                        
                        # Record metrics
                        duration = time.time() - start_time
                        self.database_query_duration.record(duration, {
                            "query_type": query_type,
                            "table_name": table_name or "unknown",
                            "status": "success"
                        })
                        
                        # Add result metadata
                        if hasattr(result, 'rowcount'):
                            span.set_attribute("rows_affected", result.rowcount)
                        
                        return result
                        
                    except Exception as e:
                        duration = time.time() - start_time
                        self.database_query_duration.record(duration, {
                            "query_type": query_type,
                            "table_name": table_name or "unknown",
                            "status": "error"
                        })
                        raise
                        
            return wrapper
        return decorator
    
    def trace_external_api_call(self, api_name: str, endpoint: str = None):
        """Decorator for tracing external API calls - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "external_api_call",
                    api_name=api_name,
                    endpoint=endpoint or "unknown"
                ) as span:
                    start_time = time.time()
                    
                    try:
                        result = func(*args, **kwargs)
                        
                        # Record metrics
                        duration = time.time() - start_time
                        
                        self.external_api_calls_counter.add(1, {
                            "api_name": api_name,
                            "endpoint": endpoint or "unknown",
                            "status": "success"
                        })
                        
                        self.external_api_duration.record(duration, {
                            "api_name": api_name,
                            "endpoint": endpoint or "unknown"
                        })
                        
                        # Add response metadata
                        if hasattr(result, 'status_code'):
                            span.set_attribute("http.status_code", result.status_code)
                        
                        return result
                        
                    except Exception as e:
                        duration = time.time() - start_time
                        
                        self.external_api_calls_counter.add(1, {
                            "api_name": api_name,
                            "endpoint": endpoint or "unknown",
                            "status": "error"
                        })
                        
                        self.external_api_duration.record(duration, {
                            "api_name": api_name,
                            "endpoint": endpoint or "unknown"
                        })
                        
                        raise
                        
            return wrapper
        return decorator
    
    def trace_cache_operation(self, operation: str, cache_type: str):
        """Decorator for tracing cache operations - REAL METRICS."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.trace_operation(
                    "cache_operation",
                    operation=operation,
                    cache_type=cache_type
                ) as span:
                    try:
                        result = func(*args, **kwargs)
                        
                        # Determine if it was a hit or miss
                        cache_result = "hit" if result else "miss"
                        if operation in ["set", "delete", "clear"]:
                            cache_result = "write"
                        
                        # Record metrics
                        self.cache_operations_counter.add(1, {
                            "operation": operation,
                            "cache_type": cache_type,
                            "result": cache_result
                        })
                        
                        # Add to span
                        span.set_attribute("cache_result", cache_result)
                        
                        return result
                        
                    except Exception as e:
                        self.cache_operations_counter.add(1, {
                            "operation": operation,
                            "cache_type": cache_type,
                            "result": "error"
                        })
                        raise
                        
            return wrapper
        return decorator
    
    def _get_confidence_range(self, confidence: float) -> str:
        """Get confidence range for metrics grouping."""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.5:
            return "medium"
        elif confidence >= 0.3:
            return "low"
        else:
            return "very_low"
    
    def create_custom_span(self, name: str, **attributes) -> Span:
        """Create a custom span with attributes - REAL TRACING."""
        span = self.tracer.start_span(f"datahero.custom.{name}")
        
        # Add custom attributes
        for key, value in attributes.items():
            span.set_attribute(f"custom.{key}", str(value))
        
        return span
    
    def record_business_event(self, event_name: str, **attributes):
        """Record a business event with custom attributes."""
        with self.trace_operation("business_event", event_name=event_name) as span:
            # Add all attributes to span
            for key, value in attributes.items():
                span.set_attribute(f"business.{key}", str(value))
            
            logger.info("Business event recorded",
                       event_name=event_name,
                       **attributes,
                       span_id=span.get_span_context().span_id,
                       trace_id=span.get_span_context().trace_id)
    
    def get_trace_context(self) -> Dict[str, str]:
        """Get current trace context for correlation - REAL CONTEXT."""
        current_span = trace.get_current_span()
        
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            return {
                "trace_id": format(span_context.trace_id, '032x'),
                "span_id": format(span_context.span_id, '016x'),
                "trace_flags": span_context.trace_flags
            }
        
        return {}


# Global observability instance
observability_system: Optional[DataHeroObservabilitySystem] = None


def get_observability_system() -> DataHeroObservabilitySystem:
    """Get global observability system instance - FAIL FAST if not initialized."""
    global observability_system
    
    if observability_system is None:
        raise ValueError("Observability system not initialized - FAIL FAST")
    
    return observability_system


def initialize_observability(service_name: str = "datahero4") -> DataHeroObservabilitySystem:
    """Initialize global observability system - REAL COMPONENTS ONLY."""
    global observability_system
    
    if observability_system is not None:
        logger.warning("Observability system already initialized")
        return observability_system
    
    try:
        observability_system = DataHeroObservabilitySystem(service_name)
        logger.info("Global observability system initialized", service_name=service_name)
        return observability_system
        
    except Exception as e:
        logger.error("Failed to initialize observability system - FAIL FAST", error=str(e))
        raise


# Convenience decorators using global instance
def trace_kpi_calculation(kpi_id: str, profile_type: str, layer: str):
    """Convenience decorator for KPI calculation tracing."""
    return get_observability_system().trace_kpi_calculation(kpi_id, profile_type, layer)


def trace_profile_detection(user_id: str):
    """Convenience decorator for profile detection tracing."""
    return get_observability_system().trace_profile_detection(user_id)


def trace_routing_decision(kpi_id: str, profile_type: str):
    """Convenience decorator for routing decision tracing."""
    return get_observability_system().trace_routing_decision(kpi_id, profile_type)


def trace_database_query(query_type: str, table_name: str = None):
    """Convenience decorator for database query tracing."""
    return get_observability_system().trace_database_query(query_type, table_name)


def trace_external_api_call(api_name: str, endpoint: str = None):
    """Convenience decorator for external API call tracing."""
    return get_observability_system().trace_external_api_call(api_name, endpoint)


def trace_cache_operation(operation: str, cache_type: str):
    """Convenience decorator for cache operation tracing."""
    return get_observability_system().trace_cache_operation(operation, cache_type)
