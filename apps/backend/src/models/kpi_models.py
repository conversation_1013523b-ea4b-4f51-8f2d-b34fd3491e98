"""
SQLModel Definitions for KPI Data Access
========================================

Type-safe database models using SQLModel for KPI calculations.
Replaces manual SQL string construction with ORM queries.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from sqlmodel import SQLModel, Field, select, Session, create_engine
from sqlalchemy import Column, DateTime, func, text
import logging

logger = logging.getLogger(__name__)


class TransactionBase(SQLModel):
    """Base model for transaction data."""
    id: Optional[int] = Field(default=None, primary_key=True)
    client_id: str = Field(index=True)
    currency: str = Field(index=True)
    amount: Decimal = Field(decimal_places=2)
    spread: Optional[Decimal] = Field(default=None, decimal_places=4)
    status: str = Field(index=True)
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    updated_at: Optional[datetime] = Field(default=None)


class Transaction(TransactionBase, table=True):
    """Transaction table model."""
    __tablename__ = "transactions"


class ClientBase(SQLModel):
    """Base model for client data."""
    id: str = Field(primary_key=True)
    name: str
    sector: str = Field(index=True)
    status: str = Field(default="active", index=True)
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )


class Client(ClientBase, table=True):
    """Client table model."""
    __tablename__ = "clients"


class KpiCalculationResult(SQLModel):
    """Model for KPI calculation results."""
    kpi_id: str
    title: str
    description: str
    current_value: float
    previous_value: Optional[float] = None
    change_percent: Optional[float] = None
    trend: str = "neutral"  # positive, negative, neutral
    format_type: str = "number"  # number, currency, percentage
    unit: str = ""
    chart_data: Optional[List[Dict[str, Any]]] = None
    alert: Optional[Dict[str, Any]] = None
    is_priority: bool = False
    category: str = "general"
    display_order: int = 999


class KpiRepository:
    """Repository for KPI data access using SQLModel."""
    
    def __init__(self, session: Session):
        self.session = session
        
    def get_total_volume(
        self, 
        client_id: str, 
        timeframe: str = "week",
        currency: str = "all"
    ) -> KpiCalculationResult:
        """Calculate total volume KPI."""
        try:
            # Build base query
            query = select(func.sum(Transaction.amount)).where(
                Transaction.client_id == client_id,
                Transaction.status == "completed"
            )
            
            # Add timeframe filter
            query = self._add_timeframe_filter(query, timeframe)
            
            # Add currency filter
            if currency != "all":
                query = query.where(Transaction.currency == currency.upper())
            
            # Execute query
            result = self.session.exec(query).first()
            current_value = float(result or 0)
            
            # Calculate previous period for comparison
            previous_value = self._get_previous_period_value(
                "total_volume", client_id, timeframe, currency
            )
            
            # Calculate change percentage
            change_percent = self._calculate_change_percent(current_value, previous_value)
            
            return KpiCalculationResult(
                kpi_id="total_volume",
                title="Volume Total Negociado",
                description="Volume total de transações processadas",
                current_value=current_value,
                previous_value=previous_value,
                change_percent=change_percent,
                trend=self._determine_trend(change_percent),
                format_type="currency",
                unit="USD",
                is_priority=True,
                category="volume",
                display_order=1
            )
            
        except Exception as e:
            logger.error(f"Error calculating total_volume: {e}")
            raise
    
    def get_average_spread(
        self,
        client_id: str,
        timeframe: str = "week", 
        currency: str = "all"
    ) -> KpiCalculationResult:
        """Calculate average spread KPI."""
        try:
            query = select(func.avg(Transaction.spread)).where(
                Transaction.client_id == client_id,
                Transaction.status == "completed",
                Transaction.spread.is_not(None)
            )
            
            query = self._add_timeframe_filter(query, timeframe)
            
            if currency != "all":
                query = query.where(Transaction.currency == currency.upper())
            
            result = self.session.exec(query).first()
            current_value = float(result or 0)
            
            previous_value = self._get_previous_period_value(
                "average_spread", client_id, timeframe, currency
            )
            
            change_percent = self._calculate_change_percent(current_value, previous_value)
            
            return KpiCalculationResult(
                kpi_id="average_spread",
                title="Spread Médio",
                description="Spread médio das operações de câmbio",
                current_value=current_value,
                previous_value=previous_value,
                change_percent=change_percent,
                trend=self._determine_trend(change_percent, inverse=True),  # Lower spread is better
                format_type="percentage",
                unit="bps",
                is_priority=True,
                category="spread",
                display_order=2
            )
            
        except Exception as e:
            logger.error(f"Error calculating average_spread: {e}")
            raise
    
    def get_average_ticket(
        self,
        client_id: str,
        timeframe: str = "week",
        currency: str = "all"
    ) -> KpiCalculationResult:
        """Calculate average ticket KPI."""
        try:
            query = select(func.avg(Transaction.amount)).where(
                Transaction.client_id == client_id,
                Transaction.status == "completed"
            )
            
            query = self._add_timeframe_filter(query, timeframe)
            
            if currency != "all":
                query = query.where(Transaction.currency == currency.upper())
            
            result = self.session.exec(query).first()
            current_value = float(result or 0)
            
            previous_value = self._get_previous_period_value(
                "average_ticket", client_id, timeframe, currency
            )
            
            change_percent = self._calculate_change_percent(current_value, previous_value)
            
            return KpiCalculationResult(
                kpi_id="average_ticket",
                title="Ticket Médio",
                description="Valor médio por transação",
                current_value=current_value,
                previous_value=previous_value,
                change_percent=change_percent,
                trend=self._determine_trend(change_percent),
                format_type="currency",
                unit="USD",
                is_priority=True,
                category="volume",
                display_order=3
            )
            
        except Exception as e:
            logger.error(f"Error calculating average_ticket: {e}")
            raise
    
    def _add_timeframe_filter(self, query, timeframe: str):
        """Add timeframe filter to query."""
        if timeframe == "1d":
            return query.where(func.date(Transaction.created_at) == func.current_date())
        elif timeframe == "week":
            return query.where(Transaction.created_at >= func.current_date() - text("INTERVAL '7 days'"))
        elif timeframe == "month":
            return query.where(Transaction.created_at >= func.current_date() - text("INTERVAL '30 days'"))
        elif timeframe == "quarter":
            return query.where(Transaction.created_at >= func.current_date() - text("INTERVAL '90 days'"))
        else:
            return query.where(Transaction.created_at >= func.current_date() - text("INTERVAL '7 days'"))
    
    def _get_previous_period_value(
        self, 
        kpi_id: str, 
        client_id: str, 
        timeframe: str, 
        currency: str
    ) -> Optional[float]:
        """Get previous period value for comparison."""
        # This would implement the logic to get the previous period
        # For now, return None to avoid complexity
        return None
    
    def _calculate_change_percent(
        self, 
        current: float, 
        previous: Optional[float]
    ) -> Optional[float]:
        """Calculate percentage change."""
        if previous is None or previous == 0:
            return None
        return ((current - previous) / previous) * 100
    
    def _determine_trend(self, change_percent: Optional[float], inverse: bool = False) -> str:
        """Determine trend based on change percentage."""
        if change_percent is None:
            return "neutral"
        
        if inverse:
            # For metrics where lower is better (like spread)
            if change_percent < -2:
                return "positive"
            elif change_percent > 2:
                return "negative"
        else:
            # For metrics where higher is better
            if change_percent > 2:
                return "positive"
            elif change_percent < -2:
                return "negative"
        
        return "neutral"
