"""
Alert Configuration Models for DataHero4

Defines data models for the alert configuration system that integrates
with the existing KPI monitoring and intelligent alerting infrastructure.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum


class NotificationChannel(Enum):
    """Available notification channels for alerts."""
    EMAIL = "email"
    DASHBOARD = "dashboard"
    SLACK = "slack"


@dataclass
class AlertConfig:
    """User-specific alert configuration for a KPI."""
    kpi_id: str
    user_id: str
    enabled: bool
    warning_threshold: Optional[float] = None
    critical_threshold: Optional[float] = None
    notification_channels: List[str] = field(default_factory=lambda: ["dashboard"])
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "kpiId": self.kpi_id,
            "userId": self.user_id,
            "enabled": self.enabled,
            "warningThreshold": self.warning_threshold,
            "criticalThreshold": self.critical_threshold,
            "notificationChannels": self.notification_channels,
            "createdAt": self.created_at.isoformat(),
            "updatedAt": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AlertConfig':
        """Create from dictionary data."""
        return cls(
            kpi_id=data.get("kpiId", ""),
            user_id=data.get("userId", ""),
            enabled=data.get("enabled", True),
            warning_threshold=data.get("warningThreshold"),
            critical_threshold=data.get("criticalThreshold"),
            notification_channels=data.get("notificationChannels", ["dashboard"]),
            created_at=datetime.fromisoformat(data.get("createdAt", datetime.utcnow().isoformat())),
            updated_at=datetime.fromisoformat(data.get("updatedAt", datetime.utcnow().isoformat()))
        )
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        if not self.kpi_id:
            errors.append("KPI ID is required")
        
        if not self.user_id:
            errors.append("User ID is required")
        
        if self.enabled:
            if self.warning_threshold is not None and self.critical_threshold is not None:
                if self.warning_threshold <= self.critical_threshold:
                    errors.append("Warning threshold must be greater than critical threshold")
            
            if not self.notification_channels:
                errors.append("At least one notification channel is required when alerts are enabled")
            
            # Validate notification channels
            valid_channels = [channel.value for channel in NotificationChannel]
            for channel in self.notification_channels:
                if channel not in valid_channels:
                    errors.append(f"Invalid notification channel: {channel}")
        
        return errors


@dataclass
class ActiveAlert:
    """Represents an active alert for a KPI."""
    id: str
    kpi_id: str
    kpi_name: str
    type: str  # 'warning' or 'critical'
    message: str
    current_value: float
    threshold: float
    triggered_at: datetime
    is_active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "kpiId": self.kpi_id,
            "kpiName": self.kpi_name,
            "type": self.type,
            "message": self.message,
            "currentValue": self.current_value,
            "threshold": self.threshold,
            "triggeredAt": self.triggered_at.isoformat(),
            "isActive": self.is_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ActiveAlert':
        """Create from dictionary data."""
        return cls(
            id=data.get("id", ""),
            kpi_id=data.get("kpiId", ""),
            kpi_name=data.get("kpiName", ""),
            type=data.get("type", "warning"),
            message=data.get("message", ""),
            current_value=data.get("currentValue", 0.0),
            threshold=data.get("threshold", 0.0),
            triggered_at=datetime.fromisoformat(data.get("triggeredAt", datetime.utcnow().isoformat())),
            is_active=data.get("isActive", True)
        )


# In-memory storage for alert configurations (in production, use database)
_alert_configs: Dict[str, AlertConfig] = {}
_active_alerts: Dict[str, ActiveAlert] = {}


class AlertConfigStorage:
    """Simple storage interface for alert configurations."""
    
    @staticmethod
    def save_config(config: AlertConfig) -> AlertConfig:
        """Save alert configuration."""
        config.updated_at = datetime.utcnow()
        key = f"{config.user_id}:{config.kpi_id}"
        _alert_configs[key] = config
        return config
    
    @staticmethod
    def get_config(user_id: str, kpi_id: str) -> Optional[AlertConfig]:
        """Get alert configuration."""
        key = f"{user_id}:{kpi_id}"
        return _alert_configs.get(key)
    
    @staticmethod
    def delete_config(user_id: str, kpi_id: str) -> bool:
        """Delete alert configuration."""
        key = f"{user_id}:{kpi_id}"
        if key in _alert_configs:
            del _alert_configs[key]
            return True
        return False
    
    @staticmethod
    def get_user_configs(user_id: str) -> List[AlertConfig]:
        """Get all configurations for a user."""
        return [config for config in _alert_configs.values() if config.user_id == user_id]
    
    @staticmethod
    def save_active_alert(alert: ActiveAlert) -> ActiveAlert:
        """Save active alert."""
        _active_alerts[alert.id] = alert
        return alert
    
    @staticmethod
    def get_active_alert(kpi_id: str) -> Optional[ActiveAlert]:
        """Get active alert for KPI."""
        for alert in _active_alerts.values():
            if alert.kpi_id == kpi_id and alert.is_active:
                return alert
        return None
    
    @staticmethod
    def get_all_active_alerts() -> List[ActiveAlert]:
        """Get all active alerts."""
        return [alert for alert in _active_alerts.values() if alert.is_active]
    
    @staticmethod
    def deactivate_alert(alert_id: str) -> bool:
        """Deactivate an alert."""
        if alert_id in _active_alerts:
            _active_alerts[alert_id].is_active = False
            return True
        return False
