"""
Profile-specific KPI mappings for DataHero4 Fase 1 Implementation
================================================================

Extends existing architecture without refactoring.
Maps 18 high-priority KPIs to user profiles based on business requirements.

Author: DataHero4 Team
Date: 2025-01-24
"""

from typing import Dict, List
from enum import Enum


class ProfileType(Enum):
    """User profile types."""
    CEO = "CEO"
    CFO = "CFO"
    RISK_MANAGER = "Risk_Manager"
    OPERATIONS = "Operations"
    TRADER = "Trader"


# FASE 1: 18 KPIs de Alta Prioridade
# Validados via MCP Postgres com dados reais

# CEO Profile KPIs (5 KPIs)
CEO_PROFILE_KPIS = [
    # EXISTING (já implementados)
    'spread_income_detailed',
    'margem_liquida_operacional',

    # NEW Phase 1 additions (validados MCP Postgres)
    'receita_total_mensal',           # ✅ Validado: conta_receber
    'concentracao_top10_clientes',    # ✅ Validado: boleta + pessoa
    'top_10_clientes_volume',         # ✅ Validado: boleta + pessoa (ranking)
    'crescimento_receita_yoy',        # ✅ Validado: conta_receber histórico
    'ltv_lifetime_value',             # ✅ Validado: boleta + pessoa
]

# CFO Profile KPIs (6 KPIs)
CFO_PROFILE_KPIS = [
    # EXISTING (já implementado)
    'margem_liquida_operacional',

    # NEW Phase 1 additions (validados MCP Postgres)
    'receita_total_mensal',           # ✅ Validado: conta_receber
    'margem_bruta_por_produto',       # ✅ Validado: boleta + boleta_moeda
    'top_10_clientes_volume',         # ✅ Validado: boleta + pessoa (ranking)
    'ebitda_mensal',                  # ✅ Validado: conta_receber + conta_pagar
    'custo_operacional_por_transacao', # ✅ Validado: boleta (custos)
    'auditoria_compliance_score',     # ✅ Validado: tb_compliance_score
]

# Risk Manager Profile KPIs (3 KPIs)
RISK_MANAGER_PROFILE_KPIS = [
    # EXISTING (já implementado)
    'tempo_processamento_medio',
    
    # NEW Phase 1 additions (validados MCP Postgres)
    'utilizacao_limites_cliente',     # ✅ Validado: pld_compliance
    'aging_receivables',              # ✅ Validado: conta_receber
    'credit_score_medio_carteira',    # ✅ Validado: tb_compliance_score
]

# Operations Profile KPIs (4 KPIs)
OPERATIONS_PROFILE_KPIS = [
    # EXISTING (já implementados)
    'custo_por_transacao',
    'tempo_processamento_medio',
    
    # NEW Phase 1 additions (validados MCP Postgres)
    'throughput_transacoes_hora',     # ✅ Validado: boleta (análise horária)
    'produtividade_equipe',           # ✅ Validado: boleta + usuario
    'fila_processamento_tamanho',     # ✅ Validado: boleta_status
    'sla_compliance_rate',            # ✅ Validado: boleta (tempos)
]

# Trader Profile KPIs (2 KPIs)
TRADER_PROFILE_KPIS = [
    # EXISTING (já implementado)
    'spread_income_detailed',
    
    # NEW Phase 1 additions (validados MCP Postgres)
    'volume_vendas_mensal',           # ✅ Validado: boleta (vendas)
    'numero_novos_clientes',          # ✅ Validado: pessoa (criação)
]

# Consolidated mapping
PROFILE_KPI_MAPPING: Dict[ProfileType, List[str]] = {
    ProfileType.CEO: CEO_PROFILE_KPIS,
    ProfileType.CFO: CFO_PROFILE_KPIS,
    ProfileType.RISK_MANAGER: RISK_MANAGER_PROFILE_KPIS,
    ProfileType.OPERATIONS: OPERATIONS_PROFILE_KPIS,
    ProfileType.TRADER: TRADER_PROFILE_KPIS,
}

# Critical KPIs for SmartQueryRouter (extend existing)
CRITICAL_KPIS_BY_PROFILE: Dict[ProfileType, List[str]] = {
    ProfileType.CEO: [
        'spread_income_detailed',
        'margem_liquida_operacional',
        'receita_total_mensal',        # NEW: High priority for CEO
        'concentracao_top10_clientes', # NEW: Risk management priority
        'top_10_clientes_volume',      # NEW: Client ranking priority
    ],
    ProfileType.CFO: [
        'margem_liquida_operacional',
        'receita_total_mensal',        # NEW: High priority for CFO
        'top_10_clientes_volume',      # NEW: Client analysis priority
        'ebitda_mensal',               # NEW: Financial performance
    ],
    ProfileType.RISK_MANAGER: [
        'tempo_processamento_medio',
        'utilizacao_limites_cliente',  # NEW: Risk monitoring
        'credit_score_medio_carteira', # NEW: Credit risk
    ],
    ProfileType.OPERATIONS: [
        'custo_por_transacao',
        'tempo_processamento_medio',
        'throughput_transacoes_hora',  # NEW: Operational efficiency
        'produtividade_equipe',        # NEW: Team performance
    ],
    ProfileType.TRADER: [
        'spread_income_detailed',
        'volume_vendas_mensal',        # NEW: Sales performance
    ],
}

# KPI Categories for filtering
KPI_CATEGORIES = {
    # Financial KPIs
    'financial': [
        'receita_total_mensal',
        'margem_bruta_por_produto',
        'ebitda_mensal',
        'margem_liquida_operacional',
        'spread_income_detailed',
        'custo_operacional_por_transacao',
        'ltv_lifetime_value',
    ],
    
    # Risk Management KPIs
    'risk': [
        'utilizacao_limites_cliente',
        'credit_score_medio_carteira',
        'concentracao_top10_clientes',
        'top_10_clientes_volume',
        'aging_receivables',
    ],
    
    # Operational KPIs
    'operational': [
        'throughput_transacoes_hora',
        'produtividade_equipe',
        'tempo_processamento_medio',
        'custo_por_transacao',
        'fila_processamento_tamanho',
        'sla_compliance_rate',
    ],
    
    # Sales & Growth KPIs
    'sales': [
        'volume_vendas_mensal',
        'numero_novos_clientes',
        'crescimento_receita_yoy',
    ],
    
    # Compliance KPIs
    'compliance': [
        'auditoria_compliance_score',
    ],
}

# Priority levels for implementation
KPI_PRIORITY_LEVELS = {
    'HIGH': [
        'receita_total_mensal',
        'concentracao_top10_clientes',
        'top_10_clientes_volume',
        'margem_bruta_por_produto',
        'throughput_transacoes_hora',
        'produtividade_equipe',
        'volume_vendas_mensal',
        'utilizacao_limites_cliente',
    ],
    'MEDIUM': [
        'ebitda_mensal',
        'crescimento_receita_yoy',
        'ltv_lifetime_value',
        'custo_operacional_por_transacao',
        'aging_receivables',
        'credit_score_medio_carteira',
        'fila_processamento_tamanho',
        'sla_compliance_rate',
        'numero_novos_clientes',
    ],
    'LOW': [
        'auditoria_compliance_score',
    ],
}


def get_kpis_for_profile(profile_type: str) -> List[str]:
    """
    Get KPI list for a specific profile type.
    
    Args:
        profile_type: Profile type string
        
    Returns:
        List of KPI IDs for the profile
    """
    try:
        profile_enum = ProfileType(profile_type)
        return PROFILE_KPI_MAPPING.get(profile_enum, [])
    except ValueError:
        return []


def get_critical_kpis_for_profile(profile_type: str) -> List[str]:
    """
    Get critical KPI list for SmartQueryRouter.
    
    Args:
        profile_type: Profile type string
        
    Returns:
        List of critical KPI IDs for the profile
    """
    try:
        profile_enum = ProfileType(profile_type)
        return CRITICAL_KPIS_BY_PROFILE.get(profile_enum, [])
    except ValueError:
        return []


def get_kpis_by_category(category: str) -> List[str]:
    """
    Get KPIs by category.
    
    Args:
        category: Category name
        
    Returns:
        List of KPI IDs in the category
    """
    return KPI_CATEGORIES.get(category, [])


def get_kpis_by_priority(priority: str) -> List[str]:
    """
    Get KPIs by priority level.
    
    Args:
        priority: Priority level (HIGH, MEDIUM, LOW)
        
    Returns:
        List of KPI IDs with the priority level
    """
    return KPI_PRIORITY_LEVELS.get(priority, [])


def is_kpi_supported_for_profile(kpi_id: str, profile_type: str) -> bool:
    """
    Check if a KPI is supported for a specific profile.
    
    Args:
        kpi_id: KPI identifier
        profile_type: Profile type string
        
    Returns:
        True if KPI is supported for the profile
    """
    profile_kpis = get_kpis_for_profile(profile_type)
    return kpi_id in profile_kpis


# Export for use in other modules
__all__ = [
    'ProfileType',
    'PROFILE_KPI_MAPPING',
    'CRITICAL_KPIS_BY_PROFILE',
    'KPI_CATEGORIES',
    'KPI_PRIORITY_LEVELS',
    'get_kpis_for_profile',
    'get_critical_kpis_for_profile',
    'get_kpis_by_category',
    'get_kpis_by_priority',
    'is_kpi_supported_for_profile',
]
