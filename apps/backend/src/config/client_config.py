"""
Client Configuration System
===========================

Distingue entre L2M (empresa/sistema) e clientes reais.
L2M não deve ser tratada como cliente nos filtros de dados.
"""

from typing import Dict, List, Optional, Set
from enum import Enum
import os


class ClientType(Enum):
    """Tipos de cliente no sistema."""
    SYSTEM = "system"      # L2M como empresa/sistema
    REAL_CLIENT = "client" # Clientes reais da L2M


class ClientConfig:
    """Configuração de clientes do sistema."""
    
    # L2M é a EMPRESA, não um cliente
    SYSTEM_ENTITY = "L2M"
    
    # Clientes reais devem ser identificados por outros IDs
    REAL_CLIENTS: Set[str] = {
        # Adicionar IDs de clientes reais aqui
        # Exemplo: "CLIENT_001", "CLIENT_002", etc.
    }
    
    @classmethod
    def is_system_entity(cls, identifier: str) -> bool:
        """
        Verifica se o identificador é da própria L2M (sistema).
        
        Args:
            identifier: ID para verificar
            
        Returns:
            True se for L2M (sistema), False se for cliente real
        """
        return identifier == cls.SYSTEM_ENTITY
    
    @classmethod
    def is_real_client(cls, identifier: str) -> bool:
        """
        Verifica se o identificador é de um cliente real.
        
        Args:
            identifier: ID para verificar
            
        Returns:
            True se for cliente real, False se for sistema
        """
        return identifier in cls.REAL_CLIENTS and identifier != cls.SYSTEM_ENTITY
    
    @classmethod
    def get_client_type(cls, identifier: str) -> ClientType:
        """
        Determina o tipo de cliente.
        
        Args:
            identifier: ID para classificar
            
        Returns:
            Tipo do cliente (SYSTEM ou REAL_CLIENT)
        """
        if cls.is_system_entity(identifier):
            return ClientType.SYSTEM
        elif cls.is_real_client(identifier):
            return ClientType.REAL_CLIENT
        else:
            # Por padrão, assumir que é cliente real se não for L2M
            return ClientType.REAL_CLIENT
    
    @classmethod
    def should_filter_by_client(cls, identifier: str) -> bool:
        """
        Determina se deve filtrar dados por este cliente.
        
        L2M (sistema) não deve filtrar dados - deve ver todos.
        Clientes reais devem ver apenas seus próprios dados.
        
        Args:
            identifier: ID do cliente
            
        Returns:
            True se deve filtrar, False se deve ver todos os dados
        """
        return not cls.is_system_entity(identifier)
    
    @classmethod
    def get_database_filter_clause(cls, identifier: str, table_alias: str = "") -> Optional[str]:
        """
        Gera cláusula SQL para filtrar por cliente.
        
        Args:
            identifier: ID do cliente
            table_alias: Alias da tabela (opcional)
            
        Returns:
            Cláusula SQL ou None se não deve filtrar
        """
        if not cls.should_filter_by_client(identifier):
            # L2M (sistema) vê todos os dados
            return None
        
        # Clientes reais veem apenas seus dados
        prefix = f"{table_alias}." if table_alias else ""
        return f"{prefix}client_id = '{identifier}'"
    
    @classmethod
    def validate_client_access(cls, requesting_client: str, data_client: str) -> bool:
        """
        Valida se um cliente pode acessar dados de outro cliente.
        
        Args:
            requesting_client: Cliente fazendo a requisição
            data_client: Cliente dono dos dados
            
        Returns:
            True se pode acessar, False caso contrário
        """
        # L2M (sistema) pode acessar dados de qualquer cliente
        if cls.is_system_entity(requesting_client):
            return True
        
        # Clientes reais só podem acessar seus próprios dados
        return requesting_client == data_client
    
    @classmethod
    def get_accessible_clients(cls, requesting_client: str) -> List[str]:
        """
        Retorna lista de clientes que o solicitante pode acessar.
        
        Args:
            requesting_client: Cliente fazendo a requisição
            
        Returns:
            Lista de IDs de clientes acessíveis
        """
        if cls.is_system_entity(requesting_client):
            # L2M (sistema) pode acessar todos os clientes
            return list(cls.REAL_CLIENTS)
        else:
            # Clientes reais só podem acessar a si mesmos
            return [requesting_client]


# Configuração global
CLIENT_CONFIG = ClientConfig()
