#!/usr/bin/env python3
"""
Migration: Add kpi_snapshots_v2 table for hybrid architecture
=============================================================

Creates the kpi_snapshots_v2 table extending the existing snapshot system
with user profile dimensions and proper partitioning for time-series data.

This migration:
1. Creates kpi_snapshots_v2 table with profile-aware structure
2. Implements PostgreSQL partitioning by period_date
3. Creates optimized indexes for hybrid architecture queries
4. Maintains backward compatibility with existing snapshot system

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import sys
from pathlib import Path
from sqlalchemy import text, create_engine
from sqlalchemy.orm import sessionmaker

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.learning_db_utils import get_db_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_kpi_snapshots_v2_table():
    """Create kpi_snapshots_v2 table with partitioning and indexes."""
    try:
        logger.info("🔨 Creating kpi_snapshots_v2 table for hybrid architecture...")
        
        # Get database manager
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Create the main partitioned table
            create_table_sql = text("""
                -- Create kpi_snapshots_v2 table with partitioning
                CREATE TABLE IF NOT EXISTS kpi_snapshots_v2 (
                    client_id VARCHAR(50) NOT NULL,
                    kpi_id VARCHAR(50) NOT NULL,
                    user_profile VARCHAR(50) NOT NULL,
                    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly')),
                    period_date DATE NOT NULL,
                    
                    -- Flexible dimensions for profile-specific data
                    dimensions JSONB DEFAULT '{}',
                    
                    -- KPI metrics and calculations
                    metrics JSONB DEFAULT '{}',
                    
                    -- Metadata and timestamps
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW(),
                    
                    -- Composite primary key for partitioning
                    PRIMARY KEY (client_id, kpi_id, user_profile, period_type, period_date)
                ) PARTITION BY RANGE (period_date);
            """)
            
            session.execute(create_table_sql)
            logger.info("✅ Created kpi_snapshots_v2 main table")
            
            # Create initial partitions for current and next quarters
            create_partitions_sql = text("""
                -- Create partition for Q1 2025
                CREATE TABLE IF NOT EXISTS kpi_snapshots_v2_2025_q1 
                PARTITION OF kpi_snapshots_v2
                FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');
                
                -- Create partition for Q2 2025
                CREATE TABLE IF NOT EXISTS kpi_snapshots_v2_2025_q2 
                PARTITION OF kpi_snapshots_v2
                FOR VALUES FROM ('2025-04-01') TO ('2025-07-01');
                
                -- Create partition for Q3 2025
                CREATE TABLE IF NOT EXISTS kpi_snapshots_v2_2025_q3 
                PARTITION OF kpi_snapshots_v2
                FOR VALUES FROM ('2025-07-01') TO ('2025-10-01');
                
                -- Create partition for Q4 2025
                CREATE TABLE IF NOT EXISTS kpi_snapshots_v2_2025_q4 
                PARTITION OF kpi_snapshots_v2
                FOR VALUES FROM ('2025-10-01') TO ('2026-01-01');
            """)
            
            session.execute(create_partitions_sql)
            logger.info("✅ Created quarterly partitions for 2025")
            
            # Create optimized indexes for hybrid architecture queries
            create_indexes_sql = text("""
                -- Index for profile-based KPI lookups (most common query pattern)
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_profile_kpi 
                ON kpi_snapshots_v2 (user_profile, kpi_id, period_date DESC);
                
                -- Index for client-specific queries
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_client_profile 
                ON kpi_snapshots_v2 (client_id, user_profile, period_date DESC);
                
                -- GIN index for JSONB dimensions (flexible querying)
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_dimensions_gin 
                ON kpi_snapshots_v2 USING GIN (dimensions);
                
                -- GIN index for JSONB metrics (KPI value queries)
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_metrics_gin 
                ON kpi_snapshots_v2 USING GIN (metrics);
                
                -- Index for time-based queries
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_period_date 
                ON kpi_snapshots_v2 (period_date DESC);
                
                -- Composite index for smart router queries
                CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_v2_router_lookup 
                ON kpi_snapshots_v2 (kpi_id, user_profile, period_type, period_date DESC);
            """)
            
            session.execute(create_indexes_sql)
            logger.info("✅ Created optimized indexes for hybrid architecture")
            
            # Create function for automatic partition creation
            create_partition_function_sql = text("""
                -- Function to automatically create new partitions
                CREATE OR REPLACE FUNCTION create_kpi_snapshots_v2_partition(start_date DATE, end_date DATE, partition_name TEXT)
                RETURNS VOID AS $$
                BEGIN
                    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF kpi_snapshots_v2 FOR VALUES FROM (%L) TO (%L)',
                                   partition_name, start_date, end_date);
                END;
                $$ LANGUAGE plpgsql;
                
                -- Function to get or create partition for a given date
                CREATE OR REPLACE FUNCTION ensure_kpi_snapshots_v2_partition(target_date DATE)
                RETURNS TEXT AS $$
                DECLARE
                    quarter_start DATE;
                    quarter_end DATE;
                    partition_name TEXT;
                    year_val INTEGER;
                    quarter_val INTEGER;
                BEGIN
                    -- Calculate quarter boundaries
                    year_val := EXTRACT(YEAR FROM target_date);
                    quarter_val := EXTRACT(QUARTER FROM target_date);
                    
                    quarter_start := DATE_TRUNC('quarter', target_date);
                    quarter_end := quarter_start + INTERVAL '3 months';
                    
                    partition_name := format('kpi_snapshots_v2_%s_q%s', year_val, quarter_val);
                    
                    -- Create partition if it doesn't exist
                    PERFORM create_kpi_snapshots_v2_partition(quarter_start, quarter_end, partition_name);
                    
                    RETURN partition_name;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            session.execute(create_partition_function_sql)
            logger.info("✅ Created automatic partition management functions")
            
            session.commit()
            logger.info("🎉 Successfully created kpi_snapshots_v2 table with hybrid architecture support")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error creating kpi_snapshots_v2 table: {e}")
        return False


def validate_kpi_snapshots_v2_table():
    """Validate that the kpi_snapshots_v2 table was created correctly."""
    try:
        logger.info("🔍 Validating kpi_snapshots_v2 table structure...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Check if table exists
            check_table_sql = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'kpi_snapshots_v2'
                );
            """)
            
            table_exists = session.execute(check_table_sql).scalar()
            
            if not table_exists:
                logger.error("❌ kpi_snapshots_v2 table was not created")
                return False
            
            # Check partitions
            check_partitions_sql = text("""
                SELECT schemaname, tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'kpi_snapshots_v2_%' 
                ORDER BY tablename;
            """)
            
            partitions = session.execute(check_partitions_sql).fetchall()
            logger.info(f"✅ Found {len(partitions)} partitions:")
            for partition in partitions:
                logger.info(f"   - {partition.tablename}")
            
            # Check indexes
            check_indexes_sql = text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'kpi_snapshots_v2' 
                ORDER BY indexname;
            """)
            
            indexes = session.execute(check_indexes_sql).fetchall()
            logger.info(f"✅ Found {len(indexes)} indexes:")
            for index in indexes:
                logger.info(f"   - {index.indexname}")
            
            logger.info("🎉 kpi_snapshots_v2 table validation successful")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error validating kpi_snapshots_v2 table: {e}")
        return False


def rollback_kpi_snapshots_v2_table():
    """Rollback function to drop kpi_snapshots_v2 table if needed."""
    try:
        logger.warning("🔄 Rolling back kpi_snapshots_v2 table creation...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Drop the main table (will cascade to partitions)
            rollback_sql = text("""
                DROP TABLE IF EXISTS kpi_snapshots_v2 CASCADE;
                DROP FUNCTION IF EXISTS create_kpi_snapshots_v2_partition(DATE, DATE, TEXT);
                DROP FUNCTION IF EXISTS ensure_kpi_snapshots_v2_partition(DATE);
            """)
            
            session.execute(rollback_sql)
            session.commit()
            
            logger.info("✅ Successfully rolled back kpi_snapshots_v2 table")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error rolling back kpi_snapshots_v2 table: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting kpi_snapshots_v2 table migration...")
    
    # Create the table
    if create_kpi_snapshots_v2_table():
        # Validate the creation
        if validate_kpi_snapshots_v2_table():
            logger.info("🎉 Migration completed successfully!")
        else:
            logger.error("❌ Migration validation failed")
            sys.exit(1)
    else:
        logger.error("❌ Migration failed")
        sys.exit(1)
