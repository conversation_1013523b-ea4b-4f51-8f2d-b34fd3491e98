#!/usr/bin/env python3
"""
Migration: Add user_profiles table for profile management
========================================================

Creates the user_profiles table for managing user profile types,
preferences, and KPI selections in the hybrid architecture.

This migration:
1. Creates user_profiles table with profile management fields
2. Creates indexes for efficient profile lookups
3. Inserts default profile types (CEO, CF<PERSON>, Risk_Manager, Trader, Operations)
4. Supports CRUD operations for profile management

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import sys
from pathlib import Path
from sqlalchemy import text
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.learning_db_utils import get_db_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_user_profiles_table():
    """Create user_profiles table for profile management."""
    try:
        logger.info("🔨 Creating user_profiles table...")
        
        # Get database manager
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Create the user_profiles table
            create_table_sql = text("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) UNIQUE NOT NULL,
                    profile_type VARCHAR(50) NOT NULL CHECK (
                        profile_type IN ('CEO', 'CFO', 'Risk_Manager', 'Trader', 'Operations', 'Analyst', 'Custom')
                    ),
                    
                    -- Profile preferences and configuration
                    preferences JSONB DEFAULT '{}',
                    selected_kpis TEXT[] DEFAULT '{}',
                    dashboard_layout VARCHAR(50) DEFAULT 'default',
                    
                    -- Cache and performance settings
                    cache_ttl INTEGER DEFAULT 3600, -- TTL in seconds
                    preferred_timeframe VARCHAR(20) DEFAULT 'week',
                    
                    -- Profile metadata
                    display_name VARCHAR(255),
                    description TEXT,
                    is_active BOOLEAN DEFAULT true,
                    
                    -- Timestamps
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW(),
                    last_login TIMESTAMP,
                    
                    -- Profile detection metadata
                    detection_confidence FLOAT DEFAULT 0.0,
                    detection_method VARCHAR(50) DEFAULT 'manual', -- manual, automatic, hybrid
                    usage_patterns JSONB DEFAULT '{}',
                    
                    -- Constraints
                    CONSTRAINT valid_cache_ttl CHECK (cache_ttl > 0),
                    CONSTRAINT valid_detection_confidence CHECK (detection_confidence >= 0.0 AND detection_confidence <= 1.0)
                );
            """)
            
            session.execute(create_table_sql)
            logger.info("✅ Created user_profiles table")
            
            # Create indexes for efficient lookups
            create_indexes_sql = text("""
                -- Primary lookup index
                CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id 
                ON user_profiles (user_id);
                
                -- Profile type queries
                CREATE INDEX IF NOT EXISTS idx_user_profiles_profile_type 
                ON user_profiles (profile_type);
                
                -- Active profiles lookup
                CREATE INDEX IF NOT EXISTS idx_user_profiles_active 
                ON user_profiles (is_active, profile_type);
                
                -- GIN index for preferences JSONB
                CREATE INDEX IF NOT EXISTS idx_user_profiles_preferences_gin 
                ON user_profiles USING GIN (preferences);
                
                -- GIN index for usage patterns JSONB
                CREATE INDEX IF NOT EXISTS idx_user_profiles_usage_patterns_gin 
                ON user_profiles USING GIN (usage_patterns);
                
                -- Index for profile detection queries
                CREATE INDEX IF NOT EXISTS idx_user_profiles_detection 
                ON user_profiles (detection_method, detection_confidence DESC);
                
                -- Composite index for hybrid architecture queries
                CREATE INDEX IF NOT EXISTS idx_user_profiles_hybrid_lookup 
                ON user_profiles (user_id, profile_type, is_active);
            """)
            
            session.execute(create_indexes_sql)
            logger.info("✅ Created indexes for user_profiles table")
            
            # Create trigger for updated_at timestamp
            create_trigger_sql = text("""
                -- Function to update updated_at timestamp
                CREATE OR REPLACE FUNCTION update_user_profiles_updated_at()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = NOW();
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Trigger to automatically update updated_at
                DROP TRIGGER IF EXISTS trigger_user_profiles_updated_at ON user_profiles;
                CREATE TRIGGER trigger_user_profiles_updated_at
                    BEFORE UPDATE ON user_profiles
                    FOR EACH ROW
                    EXECUTE FUNCTION update_user_profiles_updated_at();
            """)
            
            session.execute(create_trigger_sql)
            logger.info("✅ Created updated_at trigger for user_profiles table")
            
            session.commit()
            logger.info("🎉 Successfully created user_profiles table")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error creating user_profiles table: {e}")
        return False


def insert_default_profile_types():
    """Insert default profile types with their configurations."""
    try:
        logger.info("📋 Inserting default profile types...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Define default profile configurations
            default_profiles = [
                {
                    'user_id': 'system_ceo_template',
                    'profile_type': 'CEO',
                    'display_name': 'CEO/Diretoria',
                    'description': 'Perfil executivo com foco em KPIs estratégicos e visão geral do negócio',
                    'preferences': {
                        'preferred_charts': ['line', 'bar', 'pie'],
                        'data_granularity': 'daily',
                        'alert_threshold': 'high',
                        'dashboard_sections': ['revenue', 'growth', 'efficiency']
                    },
                    'selected_kpis': ['spread_income_detailed', 'margem_liquida_operacional', 'volume_total', 'crescimento_receita'],
                    'cache_ttl': 3600,  # 1 hour
                    'preferred_timeframe': 'month'
                },
                {
                    'user_id': 'system_cfo_template',
                    'profile_type': 'CFO',
                    'display_name': 'CFO/Financeiro',
                    'description': 'Perfil financeiro com foco em rentabilidade, custos e margens',
                    'preferences': {
                        'preferred_charts': ['line', 'bar', 'waterfall'],
                        'data_granularity': 'daily',
                        'alert_threshold': 'medium',
                        'dashboard_sections': ['profitability', 'costs', 'margins']
                    },
                    'selected_kpis': ['margem_liquida_operacional', 'custo_por_transacao', 'roe', 'spread_income_detailed'],
                    'cache_ttl': 1800,  # 30 minutes
                    'preferred_timeframe': 'week'
                },
                {
                    'user_id': 'system_risk_template',
                    'profile_type': 'Risk_Manager',
                    'display_name': 'Gerente de Risco',
                    'description': 'Perfil de risco com foco em exposições, limites e volatilidade',
                    'preferences': {
                        'preferred_charts': ['line', 'heatmap', 'gauge'],
                        'data_granularity': 'hourly',
                        'alert_threshold': 'low',
                        'dashboard_sections': ['exposure', 'limits', 'volatility']
                    },
                    'selected_kpis': ['var_diario', 'exposicao_cambial', 'utilizacao_limites', 'volatilidade'],
                    'cache_ttl': 300,   # 5 minutes
                    'preferred_timeframe': '1d'
                },
                {
                    'user_id': 'system_trader_template',
                    'profile_type': 'Trader',
                    'display_name': 'Trader/Operador',
                    'description': 'Perfil operacional com foco em spreads, volumes e performance em tempo real',
                    'preferences': {
                        'preferred_charts': ['candlestick', 'line', 'volume'],
                        'data_granularity': 'minute',
                        'alert_threshold': 'low',
                        'dashboard_sections': ['spreads', 'volumes', 'performance']
                    },
                    'selected_kpis': ['spread_realtime', 'volume_hora', 'tempo_processamento_medio', 'spread_income_detailed'],
                    'cache_ttl': 60,    # 1 minute
                    'preferred_timeframe': '1d'
                },
                {
                    'user_id': 'system_operations_template',
                    'profile_type': 'Operations',
                    'display_name': 'Operações',
                    'description': 'Perfil operacional com foco em eficiência, processamento e qualidade',
                    'preferences': {
                        'preferred_charts': ['bar', 'line', 'gauge'],
                        'data_granularity': 'hourly',
                        'alert_threshold': 'medium',
                        'dashboard_sections': ['efficiency', 'processing', 'quality']
                    },
                    'selected_kpis': ['tempo_processamento_medio', 'custo_por_transacao', 'taxa_erro', 'volume_processado'],
                    'cache_ttl': 900,   # 15 minutes
                    'preferred_timeframe': 'week'
                }
            ]
            
            # Insert default profiles using direct SQL to avoid parameter binding issues
            import json

            # Create SQL for all profiles at once
            insert_profiles_sql = text("""
                INSERT INTO user_profiles (
                    user_id, profile_type, display_name, description,
                    preferences, selected_kpis, cache_ttl, preferred_timeframe,
                    is_active, detection_method
                ) VALUES
                ('system_ceo_template', 'CEO', 'CEO/Diretoria', 'Perfil executivo com foco em KPIs estratégicos e visão geral do negócio',
                 '{"preferred_charts": ["line", "bar", "pie"], "data_granularity": "daily", "alert_threshold": "high", "dashboard_sections": ["revenue", "growth", "efficiency"]}'::jsonb,
                 ARRAY['spread_income_detailed', 'margem_liquida_operacional', 'volume_total', 'crescimento_receita'],
                 3600, 'month', true, 'template'),

                ('system_cfo_template', 'CFO', 'CFO/Financeiro', 'Perfil financeiro com foco em rentabilidade, custos e margens',
                 '{"preferred_charts": ["line", "bar", "waterfall"], "data_granularity": "daily", "alert_threshold": "medium", "dashboard_sections": ["profitability", "costs", "margins"]}'::jsonb,
                 ARRAY['margem_liquida_operacional', 'custo_por_transacao', 'roe', 'spread_income_detailed'],
                 1800, 'week', true, 'template'),

                ('system_risk_template', 'Risk_Manager', 'Gerente de Risco', 'Perfil de risco com foco em exposições, limites e volatilidade',
                 '{"preferred_charts": ["line", "heatmap", "gauge"], "data_granularity": "hourly", "alert_threshold": "low", "dashboard_sections": ["exposure", "limits", "volatility"]}'::jsonb,
                 ARRAY['var_diario', 'exposicao_cambial', 'utilizacao_limites', 'volatilidade'],
                 300, '1d', true, 'template'),

                ('system_trader_template', 'Trader', 'Trader/Operador', 'Perfil operacional com foco em spreads, volumes e performance em tempo real',
                 '{"preferred_charts": ["candlestick", "line", "volume"], "data_granularity": "minute", "alert_threshold": "low", "dashboard_sections": ["spreads", "volumes", "performance"]}'::jsonb,
                 ARRAY['spread_realtime', 'volume_hora', 'tempo_processamento_medio', 'spread_income_detailed'],
                 60, '1d', true, 'template'),

                ('system_operations_template', 'Operations', 'Operações', 'Perfil operacional com foco em eficiência, processamento e qualidade',
                 '{"preferred_charts": ["bar", "line", "gauge"], "data_granularity": "hourly", "alert_threshold": "medium", "dashboard_sections": ["efficiency", "processing", "quality"]}'::jsonb,
                 ARRAY['tempo_processamento_medio', 'custo_por_transacao', 'taxa_erro', 'volume_processado'],
                 900, 'week', true, 'template')

                ON CONFLICT (user_id) DO NOTHING;
            """)

            session.execute(insert_profiles_sql)
            
            session.commit()
            logger.info("✅ Inserted 5 default profile templates")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error inserting default profile types: {e}")
        return False


def validate_user_profiles_table():
    """Validate that the user_profiles table was created correctly."""
    try:
        logger.info("🔍 Validating user_profiles table structure...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Check if table exists
            check_table_sql = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'user_profiles'
                );
            """)
            
            table_exists = session.execute(check_table_sql).scalar()
            
            if not table_exists:
                logger.error("❌ user_profiles table was not created")
                return False
            
            # Check columns
            check_columns_sql = text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'user_profiles' 
                ORDER BY ordinal_position;
            """)
            
            columns = session.execute(check_columns_sql).fetchall()
            logger.info(f"✅ Found {len(columns)} columns in user_profiles table")
            
            # Check indexes
            check_indexes_sql = text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'user_profiles' 
                ORDER BY indexname;
            """)
            
            indexes = session.execute(check_indexes_sql).fetchall()
            logger.info(f"✅ Found {len(indexes)} indexes on user_profiles table")
            
            # Check default profiles
            check_profiles_sql = text("""
                SELECT profile_type, COUNT(*) as count
                FROM user_profiles 
                WHERE detection_method = 'template'
                GROUP BY profile_type
                ORDER BY profile_type;
            """)
            
            profiles = session.execute(check_profiles_sql).fetchall()
            logger.info(f"✅ Found {len(profiles)} default profile templates:")
            for profile in profiles:
                logger.info(f"   - {profile.profile_type}: {profile.count} template(s)")
            
            logger.info("🎉 user_profiles table validation successful")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error validating user_profiles table: {e}")
        return False


def rollback_user_profiles_table():
    """Rollback function to drop user_profiles table if needed."""
    try:
        logger.warning("🔄 Rolling back user_profiles table creation...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            rollback_sql = text("""
                DROP TRIGGER IF EXISTS trigger_user_profiles_updated_at ON user_profiles;
                DROP FUNCTION IF EXISTS update_user_profiles_updated_at();
                DROP TABLE IF EXISTS user_profiles CASCADE;
            """)
            
            session.execute(rollback_sql)
            session.commit()
            
            logger.info("✅ Successfully rolled back user_profiles table")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error rolling back user_profiles table: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting user_profiles table migration...")
    
    # Create the table
    if create_user_profiles_table():
        # Insert default profile types
        if insert_default_profile_types():
            # Validate the creation
            if validate_user_profiles_table():
                logger.info("🎉 Migration completed successfully!")
            else:
                logger.error("❌ Migration validation failed")
                sys.exit(1)
        else:
            logger.error("❌ Failed to insert default profile types")
            sys.exit(1)
    else:
        logger.error("❌ Migration failed")
        sys.exit(1)
