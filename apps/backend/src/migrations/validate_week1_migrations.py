#!/usr/bin/env python3
"""
Validation Script: Week 1 Database Extensions
=============================================

Validates that all Week 1 database extension migrations were completed
successfully and that the hybrid architecture database structure is ready.

This script:
1. Checks all tables were created correctly
2. Validates indexes and constraints
3. Verifies sample data was inserted
4. Tests basic functionality of new structures
5. Provides a comprehensive report

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import sys
from pathlib import Path
from sqlalchemy import text
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.learning_db_utils import get_db_manager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Week1MigrationValidator:
    """Validates all Week 1 database migrations."""
    
    def __init__(self):
        self.db_manager = get_db_manager()
        self.validation_results = {
            'kpi_snapshots_v2': False,
            'user_profiles': False,
            'operational_costs': False,
            'transactions_processed_at': False
        }
        self.errors = []
    
    def validate_kpi_snapshots_v2(self):
        """Validate kpi_snapshots_v2 table and partitions."""
        try:
            logger.info("🔍 Validating kpi_snapshots_v2 table...")
            
            with self.db_manager.get_session() as session:
                # Check table exists
                check_table_sql = text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'kpi_snapshots_v2'
                    );
                """)
                
                table_exists = session.execute(check_table_sql).scalar()
                if not table_exists:
                    self.errors.append("kpi_snapshots_v2 table does not exist")
                    return False
                
                # Check partitions
                check_partitions_sql = text("""
                    SELECT COUNT(*) as partition_count
                    FROM pg_tables 
                    WHERE tablename LIKE 'kpi_snapshots_v2_%';
                """)
                
                partition_count = session.execute(check_partitions_sql).scalar()
                if partition_count < 4:
                    self.errors.append(f"Expected 4 partitions, found {partition_count}")
                    return False
                
                # Check indexes
                check_indexes_sql = text("""
                    SELECT COUNT(*) as index_count
                    FROM pg_indexes 
                    WHERE tablename = 'kpi_snapshots_v2';
                """)
                
                index_count = session.execute(check_indexes_sql).scalar()
                if index_count < 6:
                    self.errors.append(f"Expected at least 6 indexes, found {index_count}")
                    return False
                
                # Check partition functions
                check_functions_sql = text("""
                    SELECT COUNT(*) as function_count
                    FROM pg_proc 
                    WHERE proname IN ('create_kpi_snapshots_v2_partition', 'ensure_kpi_snapshots_v2_partition');
                """)
                
                function_count = session.execute(check_functions_sql).scalar()
                if function_count < 2:
                    self.errors.append(f"Expected 2 partition functions, found {function_count}")
                    return False
                
                logger.info("✅ kpi_snapshots_v2 validation passed")
                self.validation_results['kpi_snapshots_v2'] = True
                return True
                
        except Exception as e:
            self.errors.append(f"kpi_snapshots_v2 validation error: {e}")
            return False
    
    def validate_user_profiles(self):
        """Validate user_profiles table and default data."""
        try:
            logger.info("🔍 Validating user_profiles table...")
            
            with self.db_manager.get_session() as session:
                # Check table exists
                check_table_sql = text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'user_profiles'
                    );
                """)
                
                table_exists = session.execute(check_table_sql).scalar()
                if not table_exists:
                    self.errors.append("user_profiles table does not exist")
                    return False
                
                # Check required columns
                check_columns_sql = text("""
                    SELECT COUNT(*) as column_count
                    FROM information_schema.columns 
                    WHERE table_name = 'user_profiles' 
                    AND column_name IN ('user_id', 'profile_type', 'preferences', 'selected_kpis', 'cache_ttl');
                """)
                
                column_count = session.execute(check_columns_sql).scalar()
                if column_count < 5:
                    self.errors.append(f"Missing required columns in user_profiles")
                    return False
                
                # Check default profiles
                check_profiles_sql = text("""
                    SELECT COUNT(*) as profile_count
                    FROM user_profiles 
                    WHERE detection_method = 'template';
                """)
                
                profile_count = session.execute(check_profiles_sql).scalar()
                if profile_count < 5:
                    self.errors.append(f"Expected 5 default profiles, found {profile_count}")
                    return False
                
                # Check JSONB preferences
                check_jsonb_sql = text("""
                    SELECT COUNT(*) as jsonb_count
                    FROM user_profiles 
                    WHERE preferences IS NOT NULL 
                    AND jsonb_typeof(preferences) = 'object';
                """)
                
                jsonb_count = session.execute(check_jsonb_sql).scalar()
                if jsonb_count < 5:
                    self.errors.append(f"JSONB preferences not properly stored")
                    return False
                
                logger.info("✅ user_profiles validation passed")
                self.validation_results['user_profiles'] = True
                return True
                
        except Exception as e:
            self.errors.append(f"user_profiles validation error: {e}")
            return False
    
    def validate_operational_costs(self):
        """Validate operational_costs table and sample data."""
        try:
            logger.info("🔍 Validating operational_costs table...")
            
            with self.db_manager.get_session() as session:
                # Check table exists
                check_table_sql = text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'operational_costs'
                    );
                """)
                
                table_exists = session.execute(check_table_sql).scalar()
                if not table_exists:
                    self.errors.append("operational_costs table does not exist")
                    return False
                
                # Check sample data
                check_data_sql = text("""
                    SELECT COUNT(*) as cost_count
                    FROM operational_costs;
                """)
                
                cost_count = session.execute(check_data_sql).scalar()
                if cost_count < 5:
                    self.errors.append(f"Expected at least 5 cost entries, found {cost_count}")
                    return False
                
                # Check views
                check_views_sql = text("""
                    SELECT COUNT(*) as view_count
                    FROM pg_views 
                    WHERE viewname IN ('daily_operational_costs', 'monthly_operational_costs');
                """)
                
                view_count = session.execute(check_views_sql).scalar()
                if view_count < 2:
                    self.errors.append(f"Expected 2 operational cost views, found {view_count}")
                    return False
                
                # Test cost per transaction calculation
                check_calculation_sql = text("""
                    SELECT COUNT(*) as calc_count
                    FROM operational_costs 
                    WHERE cost_per_unit IS NOT NULL;
                """)
                
                calc_count = session.execute(check_calculation_sql).scalar()
                if calc_count < 1:
                    self.errors.append("Cost per unit calculations not working")
                    return False
                
                logger.info("✅ operational_costs validation passed")
                self.validation_results['operational_costs'] = True
                return True
                
        except Exception as e:
            self.errors.append(f"operational_costs validation error: {e}")
            return False
    
    def validate_transactions_processed_at(self):
        """Validate transactions table processed_at field."""
        try:
            logger.info("🔍 Validating transactions processed_at field...")
            
            with self.db_manager.get_session() as session:
                # Check table exists
                check_table_sql = text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'transactions'
                    );
                """)
                
                table_exists = session.execute(check_table_sql).scalar()
                if not table_exists:
                    self.errors.append("transactions table does not exist")
                    return False
                
                # Check processed_at column
                check_column_sql = text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = 'transactions' 
                        AND column_name = 'processed_at'
                    );
                """)
                
                column_exists = session.execute(check_column_sql).scalar()
                if not column_exists:
                    self.errors.append("processed_at column does not exist in transactions table")
                    return False
                
                # Check processing time views
                check_views_sql = text("""
                    SELECT COUNT(*) as view_count
                    FROM pg_views 
                    WHERE viewname LIKE '%processing%';
                """)
                
                view_count = session.execute(check_views_sql).scalar()
                if view_count < 3:
                    self.errors.append(f"Expected 3 processing time views, found {view_count}")
                    return False
                
                # Check processing time indexes
                check_indexes_sql = text("""
                    SELECT COUNT(*) as index_count
                    FROM pg_indexes 
                    WHERE tablename = 'transactions' 
                    AND indexname LIKE '%processing%';
                """)
                
                index_count = session.execute(check_indexes_sql).scalar()
                if index_count < 3:
                    self.errors.append(f"Expected at least 3 processing indexes, found {index_count}")
                    return False
                
                logger.info("✅ transactions processed_at validation passed")
                self.validation_results['transactions_processed_at'] = True
                return True
                
        except Exception as e:
            self.errors.append(f"transactions processed_at validation error: {e}")
            return False
    
    def run_all_validations(self):
        """Run all validation checks."""
        try:
            logger.info("🚀 Starting Week 1 Migration Validation")
            logger.info("=" * 50)
            
            start_time = datetime.now()
            
            # Run all validations
            validations = [
                self.validate_kpi_snapshots_v2,
                self.validate_user_profiles,
                self.validate_operational_costs,
                self.validate_transactions_processed_at
            ]
            
            for validation in validations:
                validation()
            
            # Print summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("\n" + "=" * 50)
            logger.info("📊 VALIDATION SUMMARY")
            logger.info("=" * 50)
            
            logger.info(f"⏱️  Duration: {duration.total_seconds():.1f} seconds")
            
            passed_count = sum(self.validation_results.values())
            total_count = len(self.validation_results)
            
            logger.info(f"\n✅ Passed validations: {passed_count}/{total_count}")
            for component, passed in self.validation_results.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"   - {component}: {status}")
            
            if self.errors:
                logger.info(f"\n❌ Errors found: {len(self.errors)}")
                for error in self.errors:
                    logger.error(f"   - {error}")
            
            if passed_count == total_count:
                logger.info("\n🎉 ALL VALIDATIONS PASSED!")
                logger.info("🏗️ Week 1 database extensions are ready for hybrid architecture")
                logger.info("\n📋 Next steps:")
                logger.info("   1. Proceed to Week 2 backend service implementations")
                logger.info("   2. Implement SmartQueryRouter")
                logger.info("   3. Create PersonalizedCacheSystem")
                logger.info("   4. Develop ProfileDetector service")
                return True
            else:
                logger.error("\n❌ VALIDATION FAILED!")
                logger.error("🔧 Please fix the errors before proceeding to Week 2")
                return False
                
        except Exception as e:
            logger.error(f"❌ Validation process error: {e}")
            return False


def main():
    """Main function to run validation."""
    validator = Week1MigrationValidator()
    
    try:
        success = validator.run_all_validations()
        
        if success:
            logger.info("🎉 Week 1 database validation completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Week 1 database validation failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n👋 Validation process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
