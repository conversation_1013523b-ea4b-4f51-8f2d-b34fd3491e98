#!/usr/bin/env python3
"""
Migration: Add operational_costs table for cost tracking
=======================================================

Creates the operational_costs table for tracking daily operational costs
by category, enabling cost per transaction KPI calculations.

This migration:
1. Creates operational_costs table with cost tracking fields
2. Creates indexes for efficient cost aggregation queries
3. Inserts sample operational cost categories
4. Supports cost per transaction and operational margin KPIs

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import sys
from pathlib import Path
from sqlalchemy import text
from datetime import datetime, date

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.learning_db_utils import get_db_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_operational_costs_table():
    """Create operational_costs table for cost tracking."""
    try:
        logger.info("🔨 Creating operational_costs table...")
        
        # Get database manager
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Create the operational_costs table
            create_table_sql = text("""
                CREATE TABLE IF NOT EXISTS operational_costs (
                    id SERIAL PRIMARY KEY,
                    
                    -- Date and categorization
                    date DATE NOT NULL,
                    category VARCHAR(100) NOT NULL,
                    subcategory VARCHAR(100),
                    
                    -- Cost details
                    amount DECIMAL(15,2) NOT NULL CHECK (amount >= 0),
                    currency VARCHAR(3) DEFAULT 'BRL',
                    
                    -- Cost metadata
                    description TEXT,
                    cost_type VARCHAR(50) DEFAULT 'operational' CHECK (
                        cost_type IN ('operational', 'administrative', 'technology', 'compliance', 'marketing', 'other')
                    ),
                    
                    -- Allocation and tracking
                    allocation_method VARCHAR(50) DEFAULT 'direct' CHECK (
                        allocation_method IN ('direct', 'allocated', 'estimated', 'fixed', 'variable')
                    ),
                    business_unit VARCHAR(100),
                    cost_center VARCHAR(50),
                    
                    -- Transaction relationship (for cost per transaction calculations)
                    transaction_volume INTEGER DEFAULT 0,
                    cost_per_unit DECIMAL(10,4) GENERATED ALWAYS AS (
                        CASE 
                            WHEN transaction_volume > 0 THEN amount / transaction_volume 
                            ELSE NULL 
                        END
                    ) STORED,
                    
                    -- Approval and tracking
                    approved_by VARCHAR(255),
                    approval_date DATE,
                    is_recurring BOOLEAN DEFAULT false,
                    
                    -- Metadata
                    source_system VARCHAR(100),
                    external_reference VARCHAR(255),
                    tags TEXT[],
                    
                    -- Timestamps
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW(),
                    
                    -- Constraints
                    CONSTRAINT valid_date_range CHECK (date >= '2020-01-01' AND date <= CURRENT_DATE + INTERVAL '1 year'),
                    CONSTRAINT valid_currency CHECK (currency IN ('BRL', 'USD', 'EUR', 'GBP')),
                    CONSTRAINT valid_transaction_volume CHECK (transaction_volume >= 0)
                );
            """)
            
            session.execute(create_table_sql)
            logger.info("✅ Created operational_costs table")
            
            # Create indexes for efficient cost queries
            create_indexes_sql = text("""
                -- Primary date-based index for time-series queries
                CREATE INDEX IF NOT EXISTS idx_operational_costs_date 
                ON operational_costs (date DESC);
                
                -- Category-based queries
                CREATE INDEX IF NOT EXISTS idx_operational_costs_category 
                ON operational_costs (category, date DESC);
                
                -- Cost type analysis
                CREATE INDEX IF NOT EXISTS idx_operational_costs_type 
                ON operational_costs (cost_type, date DESC);
                
                -- Business unit cost tracking
                CREATE INDEX IF NOT EXISTS idx_operational_costs_business_unit 
                ON operational_costs (business_unit, date DESC);
                
                -- Composite index for cost per transaction calculations
                CREATE INDEX IF NOT EXISTS idx_operational_costs_transaction_calc 
                ON operational_costs (date, category, transaction_volume) 
                WHERE transaction_volume > 0;
                
                -- Index for recurring cost analysis
                CREATE INDEX IF NOT EXISTS idx_operational_costs_recurring 
                ON operational_costs (is_recurring, date DESC);
                
                -- GIN index for tags array
                CREATE INDEX IF NOT EXISTS idx_operational_costs_tags_gin 
                ON operational_costs USING GIN (tags);
                
                -- Composite index for KPI calculations
                CREATE INDEX IF NOT EXISTS idx_operational_costs_kpi_calc 
                ON operational_costs (date, cost_type, amount);
                
                -- Index for approval tracking
                CREATE INDEX IF NOT EXISTS idx_operational_costs_approval 
                ON operational_costs (approval_date, approved_by) 
                WHERE approved_by IS NOT NULL;
            """)
            
            session.execute(create_indexes_sql)
            logger.info("✅ Created indexes for operational_costs table")
            
            # Create trigger for updated_at timestamp
            create_trigger_sql = text("""
                -- Function to update updated_at timestamp
                CREATE OR REPLACE FUNCTION update_operational_costs_updated_at()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = NOW();
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Trigger to automatically update updated_at
                DROP TRIGGER IF EXISTS trigger_operational_costs_updated_at ON operational_costs;
                CREATE TRIGGER trigger_operational_costs_updated_at
                    BEFORE UPDATE ON operational_costs
                    FOR EACH ROW
                    EXECUTE FUNCTION update_operational_costs_updated_at();
            """)
            
            session.execute(create_trigger_sql)
            logger.info("✅ Created updated_at trigger for operational_costs table")
            
            # Create view for cost aggregations
            create_view_sql = text("""
                -- View for daily cost aggregations
                CREATE OR REPLACE VIEW daily_operational_costs AS
                SELECT 
                    date,
                    category,
                    cost_type,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount,
                    COUNT(*) as cost_entries,
                    SUM(transaction_volume) as total_transaction_volume,
                    CASE 
                        WHEN SUM(transaction_volume) > 0 THEN SUM(amount) / SUM(transaction_volume)
                        ELSE NULL 
                    END as avg_cost_per_transaction
                FROM operational_costs
                GROUP BY date, category, cost_type
                ORDER BY date DESC, category;
                
                -- View for monthly cost aggregations
                CREATE OR REPLACE VIEW monthly_operational_costs AS
                SELECT 
                    DATE_TRUNC('month', date) as month,
                    category,
                    cost_type,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_daily_amount,
                    COUNT(DISTINCT date) as days_with_costs,
                    SUM(transaction_volume) as total_transaction_volume,
                    CASE 
                        WHEN SUM(transaction_volume) > 0 THEN SUM(amount) / SUM(transaction_volume)
                        ELSE NULL 
                    END as avg_cost_per_transaction
                FROM operational_costs
                GROUP BY DATE_TRUNC('month', date), category, cost_type
                ORDER BY month DESC, category;
            """)
            
            session.execute(create_view_sql)
            logger.info("✅ Created aggregation views for operational_costs")
            
            session.commit()
            logger.info("🎉 Successfully created operational_costs table")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error creating operational_costs table: {e}")
        return False


def insert_sample_operational_costs():
    """Insert sample operational cost categories and data."""
    try:
        logger.info("📋 Inserting sample operational cost data...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Sample operational costs for the current month
            today = date.today()
            sample_costs = [
                {
                    'date': today,
                    'category': 'Technology',
                    'subcategory': 'Cloud Infrastructure',
                    'amount': 15000.00,
                    'currency': 'BRL',
                    'description': 'AWS/Azure cloud services monthly cost',
                    'cost_type': 'technology',
                    'allocation_method': 'direct',
                    'business_unit': 'IT',
                    'cost_center': 'TECH001',
                    'transaction_volume': 50000,
                    'is_recurring': True,
                    'tags': ['cloud', 'infrastructure', 'recurring']
                },
                {
                    'date': today,
                    'category': 'Compliance',
                    'subcategory': 'Regulatory Fees',
                    'amount': 8500.00,
                    'currency': 'BRL',
                    'description': 'BACEN and CVM regulatory fees',
                    'cost_type': 'compliance',
                    'allocation_method': 'direct',
                    'business_unit': 'Compliance',
                    'cost_center': 'COMP001',
                    'transaction_volume': 0,
                    'is_recurring': True,
                    'tags': ['regulatory', 'bacen', 'cvm']
                },
                {
                    'date': today,
                    'category': 'Operations',
                    'subcategory': 'Transaction Processing',
                    'amount': 12000.00,
                    'currency': 'BRL',
                    'description': 'Third-party transaction processing fees',
                    'cost_type': 'operational',
                    'allocation_method': 'variable',
                    'business_unit': 'Operations',
                    'cost_center': 'OPS001',
                    'transaction_volume': 75000,
                    'is_recurring': False,
                    'tags': ['processing', 'third-party', 'variable']
                },
                {
                    'date': today,
                    'category': 'Administrative',
                    'subcategory': 'Office Expenses',
                    'amount': 5000.00,
                    'currency': 'BRL',
                    'description': 'Office rent, utilities, and supplies',
                    'cost_type': 'administrative',
                    'allocation_method': 'allocated',
                    'business_unit': 'Administration',
                    'cost_center': 'ADM001',
                    'transaction_volume': 0,
                    'is_recurring': True,
                    'tags': ['office', 'rent', 'utilities']
                },
                {
                    'date': today,
                    'category': 'Technology',
                    'subcategory': 'Software Licenses',
                    'amount': 7500.00,
                    'currency': 'BRL',
                    'description': 'Software licenses and subscriptions',
                    'cost_type': 'technology',
                    'allocation_method': 'direct',
                    'business_unit': 'IT',
                    'cost_center': 'TECH002',
                    'transaction_volume': 0,
                    'is_recurring': True,
                    'tags': ['software', 'licenses', 'subscriptions']
                }
            ]
            
            # Insert sample costs
            for cost_data in sample_costs:
                insert_sql = text("""
                    INSERT INTO operational_costs (
                        date, category, subcategory, amount, currency, description,
                        cost_type, allocation_method, business_unit, cost_center,
                        transaction_volume, is_recurring, tags
                    ) VALUES (
                        :date, :category, :subcategory, :amount, :currency, :description,
                        :cost_type, :allocation_method, :business_unit, :cost_center,
                        :transaction_volume, :is_recurring, :tags
                    );
                """)
                
                session.execute(insert_sql, cost_data)
            
            session.commit()
            logger.info(f"✅ Inserted {len(sample_costs)} sample operational cost entries")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error inserting sample operational costs: {e}")
        return False


def validate_operational_costs_table():
    """Validate that the operational_costs table was created correctly."""
    try:
        logger.info("🔍 Validating operational_costs table structure...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Check if table exists
            check_table_sql = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'operational_costs'
                );
            """)
            
            table_exists = session.execute(check_table_sql).scalar()
            
            if not table_exists:
                logger.error("❌ operational_costs table was not created")
                return False
            
            # Check indexes
            check_indexes_sql = text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'operational_costs' 
                ORDER BY indexname;
            """)
            
            indexes = session.execute(check_indexes_sql).fetchall()
            logger.info(f"✅ Found {len(indexes)} indexes on operational_costs table")
            
            # Check views
            check_views_sql = text("""
                SELECT viewname 
                FROM pg_views 
                WHERE viewname LIKE '%operational_costs%' 
                ORDER BY viewname;
            """)
            
            views = session.execute(check_views_sql).fetchall()
            logger.info(f"✅ Found {len(views)} views for operational costs:")
            for view in views:
                logger.info(f"   - {view.viewname}")
            
            # Check sample data
            check_data_sql = text("""
                SELECT category, COUNT(*) as count, SUM(amount) as total_amount
                FROM operational_costs 
                GROUP BY category
                ORDER BY category;
            """)
            
            data = session.execute(check_data_sql).fetchall()
            logger.info(f"✅ Found {len(data)} cost categories with sample data:")
            for row in data:
                logger.info(f"   - {row.category}: {row.count} entries, R$ {row.total_amount:,.2f}")
            
            logger.info("🎉 operational_costs table validation successful")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error validating operational_costs table: {e}")
        return False


def rollback_operational_costs_table():
    """Rollback function to drop operational_costs table if needed."""
    try:
        logger.warning("🔄 Rolling back operational_costs table creation...")
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            rollback_sql = text("""
                DROP VIEW IF EXISTS monthly_operational_costs CASCADE;
                DROP VIEW IF EXISTS daily_operational_costs CASCADE;
                DROP TRIGGER IF EXISTS trigger_operational_costs_updated_at ON operational_costs;
                DROP FUNCTION IF EXISTS update_operational_costs_updated_at();
                DROP TABLE IF EXISTS operational_costs CASCADE;
            """)
            
            session.execute(rollback_sql)
            session.commit()
            
            logger.info("✅ Successfully rolled back operational_costs table")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error rolling back operational_costs table: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 Starting operational_costs table migration...")
    
    # Create the table
    if create_operational_costs_table():
        # Insert sample data
        if insert_sample_operational_costs():
            # Validate the creation
            if validate_operational_costs_table():
                logger.info("🎉 Migration completed successfully!")
            else:
                logger.error("❌ Migration validation failed")
                sys.exit(1)
        else:
            logger.error("❌ Failed to insert sample operational costs")
            sys.exit(1)
    else:
        logger.error("❌ Migration failed")
        sys.exit(1)
