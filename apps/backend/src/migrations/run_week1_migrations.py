#!/usr/bin/env python3
"""
Master Migration Script: Week 1 Database Extensions
===================================================

Runs all Week 1 database extension migrations for Phase 1 Hybrid Architecture:
1. Create kpi_snapshots_v2 table with partitioning
2. Add user_profiles table for profile management  
3. Create operational_costs table for cost tracking
4. Extend transactions table with processed_at field

This script ensures all migrations run in the correct order and validates
each step before proceeding to the next one.

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import sys
import subprocess
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Week1MigrationRunner:
    """Manages the execution of all Week 1 database migrations."""
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent
        self.migrations = [
            {
                'name': 'KPI Snapshots V2',
                'file': '001_add_kpi_snapshots_v2.py',
                'description': 'Create kpi_snapshots_v2 table with partitioning for hybrid architecture'
            },
            {
                'name': 'User Profiles',
                'file': '002_add_user_profiles.py', 
                'description': 'Add user_profiles table for profile management and personalization'
            },
            {
                'name': 'Operational Costs',
                'file': '003_add_operational_costs.py',
                'description': 'Create operational_costs table for cost per transaction KPI'
            },
            {
                'name': 'Transactions Processed At',
                'file': '004_add_processed_at_transactions.py',
                'description': 'Extend transactions table with processed_at field for processing time KPI'
            }
        ]
        self.completed_migrations = []
        self.failed_migrations = []
    
    def run_migration(self, migration):
        """Run a single migration script."""
        try:
            migration_path = self.migrations_dir / migration['file']
            
            if not migration_path.exists():
                logger.error(f"❌ Migration file not found: {migration_path}")
                return False
            
            logger.info(f"🚀 Running migration: {migration['name']}")
            logger.info(f"📝 Description: {migration['description']}")
            logger.info(f"📁 File: {migration['file']}")
            
            # Run the migration script
            result = subprocess.run(
                [sys.executable, str(migration_path)],
                cwd=self.migrations_dir.parent.parent,  # Run from backend root
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ Migration '{migration['name']}' completed successfully")
                logger.info("📊 Migration output:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        logger.info(f"   {line}")
                
                self.completed_migrations.append(migration)
                return True
            else:
                logger.error(f"❌ Migration '{migration['name']}' failed")
                logger.error("📊 Migration error output:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        logger.error(f"   {line}")
                
                self.failed_migrations.append(migration)
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Migration '{migration['name']}' timed out after 5 minutes")
            self.failed_migrations.append(migration)
            return False
        except Exception as e:
            logger.error(f"❌ Error running migration '{migration['name']}': {e}")
            self.failed_migrations.append(migration)
            return False
    
    def validate_prerequisites(self):
        """Validate that all prerequisites are met before running migrations."""
        try:
            logger.info("🔍 Validating migration prerequisites...")
            
            # Check if all migration files exist
            missing_files = []
            for migration in self.migrations:
                migration_path = self.migrations_dir / migration['file']
                if not migration_path.exists():
                    missing_files.append(migration['file'])
            
            if missing_files:
                logger.error(f"❌ Missing migration files: {missing_files}")
                return False
            
            # Check database connectivity
            try:
                from src.utils.learning_db_utils import get_db_manager
                db_manager = get_db_manager()
                with db_manager.get_session() as session:
                    session.execute("SELECT 1")
                logger.info("✅ Database connection successful")
            except Exception as e:
                logger.error(f"❌ Database connection failed: {e}")
                return False
            
            logger.info("✅ All prerequisites validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating prerequisites: {e}")
            return False
    
    def run_all_migrations(self):
        """Run all Week 1 migrations in order."""
        try:
            start_time = datetime.now()
            logger.info("🚀 Starting Week 1 Database Extensions Migration")
            logger.info("=" * 60)
            
            # Validate prerequisites
            if not self.validate_prerequisites():
                logger.error("❌ Prerequisites validation failed")
                return False
            
            # Run each migration in order
            for i, migration in enumerate(self.migrations, 1):
                logger.info(f"\n📋 Migration {i}/{len(self.migrations)}: {migration['name']}")
                logger.info("-" * 40)
                
                if not self.run_migration(migration):
                    logger.error(f"❌ Migration {i} failed, stopping execution")
                    self.print_summary(start_time, failed_at=i)
                    return False
                
                logger.info(f"✅ Migration {i} completed successfully")
            
            # All migrations completed successfully
            self.print_summary(start_time)
            return True
            
        except Exception as e:
            logger.error(f"❌ Error running migrations: {e}")
            self.print_summary(start_time, error=str(e))
            return False
    
    def print_summary(self, start_time, failed_at=None, error=None):
        """Print migration execution summary."""
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("\n" + "=" * 60)
        logger.info("📊 WEEK 1 MIGRATION SUMMARY")
        logger.info("=" * 60)
        
        logger.info(f"⏱️  Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️  End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️  Duration: {duration.total_seconds():.1f} seconds")
        
        logger.info(f"\n✅ Completed migrations: {len(self.completed_migrations)}")
        for migration in self.completed_migrations:
            logger.info(f"   - {migration['name']}")
        
        if self.failed_migrations:
            logger.info(f"\n❌ Failed migrations: {len(self.failed_migrations)}")
            for migration in self.failed_migrations:
                logger.info(f"   - {migration['name']}")
        
        if failed_at:
            logger.error(f"\n⚠️  Migration stopped at step {failed_at}")
        
        if error:
            logger.error(f"\n⚠️  Error: {error}")
        
        if len(self.completed_migrations) == len(self.migrations):
            logger.info("\n🎉 ALL WEEK 1 MIGRATIONS COMPLETED SUCCESSFULLY!")
            logger.info("🏗️  Hybrid architecture database extensions are ready")
            logger.info("📋 Next steps:")
            logger.info("   1. Verify all tables and indexes are created")
            logger.info("   2. Test KPI calculations with new tables")
            logger.info("   3. Proceed to Week 2 backend service implementations")
        else:
            logger.error("\n❌ MIGRATION EXECUTION INCOMPLETE")
            logger.error("🔧 Please review errors and fix issues before proceeding")
    
    def rollback_completed_migrations(self):
        """Rollback all completed migrations in reverse order."""
        try:
            logger.warning("🔄 Starting rollback of completed migrations...")
            
            # Rollback in reverse order
            for migration in reversed(self.completed_migrations):
                logger.warning(f"🔄 Rolling back: {migration['name']}")
                
                # Import and run rollback function
                migration_module = migration['file'].replace('.py', '')
                try:
                    # Dynamic import of rollback function
                    spec = __import__(f"migrations.{migration_module}", fromlist=[''])
                    rollback_func = getattr(spec, f"rollback_{migration_module.split('_', 1)[1]}")
                    
                    if rollback_func():
                        logger.info(f"✅ Rollback successful: {migration['name']}")
                    else:
                        logger.error(f"❌ Rollback failed: {migration['name']}")
                        
                except Exception as e:
                    logger.error(f"❌ Error during rollback of {migration['name']}: {e}")
            
            logger.warning("🔄 Rollback process completed")
            
        except Exception as e:
            logger.error(f"❌ Error during rollback process: {e}")


def main():
    """Main function to run Week 1 migrations."""
    runner = Week1MigrationRunner()
    
    try:
        success = runner.run_all_migrations()
        
        if success:
            logger.info("🎉 Week 1 database extensions completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Week 1 database extensions failed!")
            
            # Ask user if they want to rollback
            try:
                response = input("\n🔄 Do you want to rollback completed migrations? (y/N): ")
                if response.lower() in ['y', 'yes']:
                    runner.rollback_completed_migrations()
            except KeyboardInterrupt:
                logger.info("\n👋 Migration process interrupted by user")
            
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n👋 Migration process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
