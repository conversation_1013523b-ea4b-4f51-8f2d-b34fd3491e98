"""
Monitoring Routes for DataHero4 Dashboard
Endpoints para monitoramento de performance, cache e saúde do sistema.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, Optional
import logging
import time
from datetime import datetime

from src.services.cache_warming_service import get_cache_warming_service
from src.services.kpi_service import get_kpi_service

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])


@router.get("/cache/status")
async def get_cache_status(
    client_id: str = Query("L2M", description="Client ID to check cache status")
) -> Dict[str, Any]:
    """
    Retorna status detalhado do cache para monitoramento.
    
    Args:
        client_id: ID do cliente para verificar status
        
    Returns:
        Status detalhado do cache incluindo cobertura e métricas
    """
    try:
        cache_service = get_cache_warming_service()
        status = cache_service.get_cache_status(client_id)
        
        logger.info(f"📊 Cache status consultado para {client_id}: {status['cache_coverage_percent']}% coverage")
        
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro obtendo status do cache: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting cache status: {e}")


@router.post("/cache/warm")
async def warm_cache(
    client_id: str = Query("L2M", description="Client ID to warm cache for")
) -> Dict[str, Any]:
    """
    Executa cache warming para KPIs prioritários.
    
    Args:
        client_id: ID do cliente para aquecer cache
        
    Returns:
        Relatório do processo de warming
    """
    try:
        cache_service = get_cache_warming_service()
        
        # Check se warming é necessário
        if not cache_service.should_warm_cache(client_id):
            return {
                "success": True,
                "data": {
                    "status": "skipped",
                    "reason": "cache_already_warm",
                    "client_id": client_id
                },
                "timestamp": datetime.now().isoformat()
            }
        
        # Execute warming
        warming_result = await cache_service.warm_priority_kpis(client_id)
        
        logger.info(f"🔥 Cache warming executado para {client_id}: {warming_result.get('kpis_warmed', 0)} KPIs")
        
        return {
            "success": True,
            "data": warming_result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro executando cache warming: {e}")
        raise HTTPException(status_code=500, detail=f"Error warming cache: {e}")


@router.get("/kpis/health")
async def check_kpis_health(
    client_id: str = Query("L2M", description="Client ID to check KPI health"),
    sector: str = Query("cambio", description="Sector to check")
) -> Dict[str, Any]:
    """
    Verifica saúde dos KPIs prioritários incluindo tempos de resposta.
    
    Args:
        client_id: ID do cliente
        sector: Setor para verificar
        
    Returns:
        Status de saúde dos KPIs com métricas de performance
    """
    try:
        kpi_service = get_kpi_service()
        cache_service = get_cache_warming_service()
        
        health_report = {
            'client_id': client_id,
            'sector': sector,
            'timestamp': datetime.now().isoformat(),
            'kpis_tested': 0,
            'kpis_healthy': 0,
            'kpis_with_errors': 0,
            'average_response_time_ms': 0,
            'kpi_details': [],
            'cache_coverage_percent': 0
        }
        
        # Test priority KPIs
        priority_kpis = cache_service.priority_kpis
        total_response_time = 0
        
        for kpi_id in priority_kpis:
            kpi_health = {
                'id': kpi_id,
                'status': 'unknown',
                'response_time_ms': 0,
                'has_cached_value': False,
                'has_cached_chart_data': False,
                'error': None
            }
            
            start_time = time.time()
            
            try:
                # Test KPI value calculation
                value = kpi_service._calculate_kpi_value(kpi_id, client_id, "week", "all")
                response_time = (time.time() - start_time) * 1000
                
                kpi_health['response_time_ms'] = round(response_time, 2)
                total_response_time += response_time
                
                if value is not None:
                    kpi_health['status'] = 'healthy'
                    health_report['kpis_healthy'] += 1
                else:
                    kpi_health['status'] = 'warning'
                    kpi_health['error'] = 'Returned None value'
                
                # Check cache status
                cached_value = kpi_service.cache.get("kpi:value", kpi_id=kpi_id, client_id=client_id, timeframe="week", currency="all")
                kpi_health['has_cached_value'] = cached_value is not None
                
                cached_chart = kpi_service.cache.get("kpi:chart", kpi_id=kpi_id, client_id=client_id, timeframe="week", currency="all")
                kpi_health['has_cached_chart_data'] = cached_chart is not None
                
            except Exception as e:
                kpi_health['status'] = 'error'
                kpi_health['error'] = str(e)
                kpi_health['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
                health_report['kpis_with_errors'] += 1
                logger.error(f"❌ KPI health check failed for {kpi_id}: {e}")
            
            health_report['kpi_details'].append(kpi_health)
            health_report['kpis_tested'] += 1
        
        # Calculate average response time
        if health_report['kpis_tested'] > 0:
            health_report['average_response_time_ms'] = round(
                total_response_time / health_report['kpis_tested'], 2
            )
        
        # Get cache coverage
        cache_status = cache_service.get_cache_status(client_id)
        health_report['cache_coverage_percent'] = cache_status.get('cache_coverage_percent', 0)
        
        # Determine overall health
        if health_report['kpis_with_errors'] == 0 and health_report['kpis_healthy'] > 0:
            overall_status = 'healthy'
        elif health_report['kpis_with_errors'] > 0 and health_report['kpis_healthy'] > 0:
            overall_status = 'degraded'
        else:
            overall_status = 'unhealthy'
        
        health_report['overall_status'] = overall_status
        
        logger.info(f"🏥 KPI health check para {client_id}: {overall_status} ({health_report['kpis_healthy']}/{health_report['kpis_tested']} healthy)")
        
        return {
            "success": True,
            "data": health_report,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro no health check dos KPIs: {e}")
        raise HTTPException(status_code=500, detail=f"Error checking KPI health: {e}")


@router.get("/performance/metrics")
async def get_performance_metrics(
    client_id: str = Query("L2M", description="Client ID"),
    timeframe: str = Query("week", description="Timeframe for metrics")
) -> Dict[str, Any]:
    """
    Retorna métricas de performance do sistema incluindo tempos de resposta.
    
    Args:
        client_id: ID do cliente
        timeframe: Timeframe para métricas
        
    Returns:
        Métricas detalhadas de performance
    """
    try:
        kpi_service = get_kpi_service()
        
        metrics = {
            'client_id': client_id,
            'timeframe': timeframe,
            'timestamp': datetime.now().isoformat(),
            'dashboard_load_time_ms': 0,
            'kpi_calculation_times': {},
            'cache_hit_rate_percent': 0,
            'database_connection_time_ms': 0
        }
        
        # Simulate dashboard load time by calculating all priority KPIs
        dashboard_start = time.time()
        
        priority_kpis = ['total_volume', 'average_ticket', 'retention_rate']
        cache_hits = 0
        total_kpis = len(priority_kpis)
        
        for kpi_id in priority_kpis:
            kpi_start = time.time()
            
            # Check if cached first
            cached_value = kpi_service.cache.get("kpi:value", kpi_id=kpi_id, client_id=client_id, timeframe="week", currency="all")
            if cached_value is not None:
                cache_hits += 1
                calculation_time = (time.time() - kpi_start) * 1000
            else:
                # Calculate fresh value
                value = kpi_service._calculate_kpi_value(kpi_id, client_id, "week", "all")
                calculation_time = (time.time() - kpi_start) * 1000
            
            metrics['kpi_calculation_times'][kpi_id] = round(calculation_time, 2)
        
        metrics['dashboard_load_time_ms'] = round((time.time() - dashboard_start) * 1000, 2)
        metrics['cache_hit_rate_percent'] = round((cache_hits / total_kpis) * 100, 1) if total_kpis > 0 else 0
        
        # Test database connection time
        db_start = time.time()
        try:
            # Quick database test
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text
            
            db_config = load_db_config(setor="cambio", cliente=client_id)
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            metrics['database_connection_time_ms'] = round((time.time() - db_start) * 1000, 2)
            
        except Exception as e:
            metrics['database_connection_time_ms'] = -1
            metrics['database_error'] = str(e)
        
        logger.info(f"📊 Performance metrics para {client_id}: {metrics['dashboard_load_time_ms']}ms load time")
        
        return {
            "success": True,
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro obtendo métricas de performance: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting performance metrics: {e}")


@router.get("/system/status")
async def get_system_status() -> Dict[str, Any]:
    """
    Retorna status geral do sistema incluindo todos os componentes.
    
    Returns:
        Status geral do sistema
    """
    try:
        import psutil
        import os
        
        system_status = {
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'uptime_seconds': time.time() - psutil.boot_time()
            },
            'application': {
                'process_id': os.getpid(),
                'memory_usage_mb': round(psutil.Process().memory_info().rss / 1024 / 1024, 2),
                'cpu_percent': psutil.Process().cpu_percent()
            },
            'services': {
                'kpi_service': 'healthy',
                'cache_service': 'healthy',
                'database': 'unknown'
            }
        }
        
        # Test services
        try:
            kpi_service = get_kpi_service()
            if kpi_service:
                system_status['services']['kpi_service'] = 'healthy'
            else:
                system_status['services']['kpi_service'] = 'unhealthy'
        except Exception:
            system_status['services']['kpi_service'] = 'error'
        
        try:
            cache_service = get_cache_warming_service()
            if cache_service:
                system_status['services']['cache_service'] = 'healthy'
            else:
                system_status['services']['cache_service'] = 'unhealthy'
        except Exception:
            system_status['services']['cache_service'] = 'error'
        
        # Test database
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text
            
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            system_status['services']['database'] = 'healthy'
            
        except Exception as e:
            system_status['services']['database'] = 'error'
            system_status['database_error'] = str(e)
        
        # Determine overall status
        service_statuses = list(system_status['services'].values())
        if all(status == 'healthy' for status in service_statuses):
            overall_status = 'healthy'
        elif any(status == 'error' for status in service_statuses):
            overall_status = 'degraded'
        else:
            overall_status = 'warning'
        
        system_status['overall_status'] = overall_status
        
        logger.info(f"🏥 System status: {overall_status}")
        
        return {
            "success": True,
            "data": system_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro obtendo status do sistema: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system status: {e}") 