"""
Alert Configuration API Routes for DataHero4

FastAPI endpoints for managing user-specific alert configurations
integrated with the KPI system and intelligent alerting.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.models.alert_config import AlertConfig, ActiveAlert, AlertConfigStorage
from src.config.kpi_definitions import KPI_DEFINITIONS

logger = logging.getLogger(__name__)

# Create router for alert configuration endpoints
alert_config_router = APIRouter(prefix="/api/alerts", tags=["alert-config"])


class AlertConfigRequest(BaseModel):
    """Request model for alert configuration."""
    enabled: bool = Field(..., description="Whether alerts are enabled")
    warningThreshold: Optional[float] = Field(None, description="Warning threshold value")
    criticalThreshold: Optional[float] = Field(None, description="Critical threshold value")
    notificationChannels: list[str] = Field(default=["dashboard"], description="Notification channels")


class AlertConfigResponse(BaseModel):
    """Response model for alert configuration."""
    success: bool
    config: Dict[str, Any]
    message: Optional[str] = None


class ActiveAlertResponse(BaseModel):
    """Response model for active alerts."""
    success: bool
    alert: Optional[Dict[str, Any]] = None
    message: Optional[str] = None


def get_current_user_id() -> str:
    """Get current user ID - in production, extract from JWT token."""
    # TODO: Implement proper user authentication
    return "current_user"


@alert_config_router.get("/config/{kpi_id}", response_model=AlertConfigResponse)
async def get_alert_config(
    kpi_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """Get alert configuration for a specific KPI."""
    try:
        logger.info(f"Getting alert config for KPI {kpi_id}, user {user_id}")
        
        # Check if KPI exists
        if kpi_id not in KPI_DEFINITIONS:
            raise HTTPException(status_code=404, detail=f"KPI {kpi_id} not found")
        
        # Get existing configuration
        config = AlertConfigStorage.get_config(user_id, kpi_id)
        
        if config is None:
            # Create default configuration from KPI definition
            kpi_def = KPI_DEFINITIONS[kpi_id]
            thresholds = kpi_def.get("thresholds", {})
            
            config = AlertConfig(
                kpi_id=kpi_id,
                user_id=user_id,
                enabled=True,
                warning_threshold=thresholds.get("warning"),
                critical_threshold=thresholds.get("critical"),
                notification_channels=["dashboard"]
            )
            
            # Save default configuration
            config = AlertConfigStorage.save_config(config)
        
        return AlertConfigResponse(
            success=True,
            config=config.to_dict(),
            message="Alert configuration retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting alert config for KPI {kpi_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.post("/config/{kpi_id}", response_model=AlertConfigResponse)
async def save_alert_config(
    kpi_id: str,
    request: AlertConfigRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Save alert configuration for a specific KPI."""
    try:
        logger.info(f"Saving alert config for KPI {kpi_id}, user {user_id}")
        
        # Check if KPI exists
        if kpi_id not in KPI_DEFINITIONS:
            raise HTTPException(status_code=404, detail=f"KPI {kpi_id} not found")
        
        # Get existing configuration or create new one
        existing_config = AlertConfigStorage.get_config(user_id, kpi_id)
        
        if existing_config:
            # Update existing configuration
            config = existing_config
            config.enabled = request.enabled
            config.warning_threshold = request.warningThreshold
            config.critical_threshold = request.criticalThreshold
            config.notification_channels = request.notificationChannels
        else:
            # Create new configuration
            config = AlertConfig(
                kpi_id=kpi_id,
                user_id=user_id,
                enabled=request.enabled,
                warning_threshold=request.warningThreshold,
                critical_threshold=request.criticalThreshold,
                notification_channels=request.notificationChannels
            )
        
        # Validate configuration
        validation_errors = config.validate()
        if validation_errors:
            raise HTTPException(status_code=400, detail=f"Validation errors: {', '.join(validation_errors)}")
        
        # Save configuration
        saved_config = AlertConfigStorage.save_config(config)
        
        logger.info(f"Alert configuration saved successfully for KPI {kpi_id}")
        
        return AlertConfigResponse(
            success=True,
            config=saved_config.to_dict(),
            message="Alert configuration saved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving alert config for KPI {kpi_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.get("/active/{kpi_id}", response_model=ActiveAlertResponse)
async def get_active_alert(kpi_id: str):
    """Get active alert for a specific KPI."""
    try:
        logger.info(f"Getting active alert for KPI {kpi_id}")
        
        # Check if KPI exists
        if kpi_id not in KPI_DEFINITIONS:
            raise HTTPException(status_code=404, detail=f"KPI {kpi_id} not found")
        
        # Get active alert from storage
        active_alert = AlertConfigStorage.get_active_alert(kpi_id)
        
        if active_alert is None:
            return ActiveAlertResponse(
                success=True,
                alert=None,
                message="No active alert for this KPI"
            )
        
        return ActiveAlertResponse(
            success=True,
            alert=active_alert.to_dict(),
            message="Active alert retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting active alert for KPI {kpi_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.delete("/config/{kpi_id}")
async def delete_alert_config(
    kpi_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """Delete alert configuration for a specific KPI."""
    try:
        logger.info(f"Deleting alert config for KPI {kpi_id}, user {user_id}")
        
        success = AlertConfigStorage.delete_config(user_id, kpi_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert configuration not found")
        
        return {"success": True, "message": "Alert configuration deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting alert config for KPI {kpi_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.get("/config/user/{user_id}")
async def get_user_alert_configs(user_id: str):
    """Get all alert configurations for a user."""
    try:
        logger.info(f"Getting all alert configs for user {user_id}")
        
        configs = AlertConfigStorage.get_user_configs(user_id)
        
        return {
            "success": True,
            "configs": [config.to_dict() for config in configs],
            "count": len(configs),
            "message": "User alert configurations retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error getting user alert configs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.get("/active")
async def get_all_active_alerts():
    """Get all active alerts."""
    try:
        logger.info("Getting all active alerts")
        
        active_alerts = AlertConfigStorage.get_all_active_alerts()
        
        return {
            "success": True,
            "alerts": [alert.to_dict() for alert in active_alerts],
            "count": len(active_alerts),
            "message": "Active alerts retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error getting active alerts: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@alert_config_router.post("/active/{alert_id}/dismiss")
async def dismiss_alert(alert_id: str):
    """Dismiss an active alert."""
    try:
        logger.info(f"Dismissing alert {alert_id}")
        
        success = AlertConfigStorage.deactivate_alert(alert_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"success": True, "message": "Alert dismissed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error dismissing alert {alert_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
