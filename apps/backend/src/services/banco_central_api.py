"""
Banco Central do Brasil API Integration for DataHero4 Week 4
===========================================================

Integração fail-fast com APIs oficiais do Banco Central do Brasil:
- Cotações USD/BRL oficiais
- Taxa SELIC oficial
- Integração com SmartQueryRouter
- Sem fallbacks para dados mock (fail-fast)

Features:
- Real-time official exchange rates
- SELIC rate integration
- Profile-aware caching via SmartQueryRouter
- Fail-fast error handling
- No mock data fallbacks

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import time
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class BCBQuotation:
    """BCB quotation data structure."""
    currency_pair: str
    rate: float
    date: str
    source: str = "BCB"
    timestamp: str = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class BCBSelicRate:
    """BCB SELIC rate data structure."""
    rate: float
    date: str
    source: str = "BCB"
    timestamp: str = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


class BancoCentralAPI:
    """
    Banco Central do Brasil API client with fail-fast integration.
    
    Provides official exchange rates and SELIC rates with no fallback
    mechanisms - fails fast if BCB APIs are unavailable.
    """
    
    def __init__(self):
        """Initialize BCB API client."""
        # BCB API endpoints
        self.base_url = "https://api.bcb.gov.br"
        self.quotation_endpoint = f"{self.base_url}/dados/serie/bcdata.sgs.1/dados"
        self.selic_endpoint = f"{self.base_url}/dados/serie/bcdata.sgs.432/dados"
        
        # Request timeout (fail-fast)
        self.timeout = 10.0  # 10 seconds max
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DataHero4/1.0 (Financial Analysis System)',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        
        logger.info("✅ BancoCentralAPI initialized with fail-fast configuration")
    
    def get_usd_brl_quotation(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Optional[BCBQuotation]:
        """
        Get official USD/BRL quotation from BCB.
        
        Args:
            start_date: Start date in DD/MM/YYYY format
            end_date: End date in DD/MM/YYYY format
            
        Returns:
            BCBQuotation object or None if API fails
            
        Raises:
            Exception: If BCB API is unavailable (fail-fast)
        """
        start_time = time.time()
        
        try:
            logger.info("🏦 Fetching USD/BRL quotation from BCB API")
            
            # Build URL with date parameters
            url = self.quotation_endpoint
            params = {}
            
            if start_date:
                params['dataInicial'] = start_date
            if end_date:
                params['dataFinal'] = end_date
            
            # Make request with fail-fast timeout
            response = self.session.get(
                url,
                params=params,
                timeout=self.timeout
            )
            
            # Fail fast on HTTP errors
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.error("❌ BCB API returned empty data")
                raise Exception("BCB API returned no quotation data")
            
            # Get latest quotation
            latest = data[-1]
            
            quotation = BCBQuotation(
                currency_pair="USD/BRL",
                rate=float(latest['valor']),
                date=latest['data'],
                source="BCB_Official"
            )
            
            request_time = (time.time() - start_time) * 1000
            logger.info(f"✅ USD/BRL quotation: {quotation.rate:.4f} ({request_time:.1f}ms)")
            
            return quotation
            
        except requests.exceptions.Timeout:
            logger.error(f"❌ BCB API timeout after {self.timeout}s")
            raise Exception(f"BCB API timeout - service unavailable")
            
        except requests.exceptions.ConnectionError:
            logger.error("❌ BCB API connection error")
            raise Exception("BCB API connection failed - service unavailable")
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"❌ BCB API HTTP error: {e}")
            raise Exception(f"BCB API HTTP error: {e.response.status_code}")
            
        except Exception as e:
            logger.error(f"❌ BCB API error: {e}")
            raise Exception(f"BCB API error: {str(e)}")
    
    def get_selic_rate(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Optional[BCBSelicRate]:
        """
        Get official SELIC rate from BCB.
        
        Args:
            start_date: Start date in DD/MM/YYYY format
            end_date: End date in DD/MM/YYYY format
            
        Returns:
            BCBSelicRate object or None if API fails
            
        Raises:
            Exception: If BCB API is unavailable (fail-fast)
        """
        start_time = time.time()
        
        try:
            logger.info("🏦 Fetching SELIC rate from BCB API")
            
            # Build URL with date parameters
            url = self.selic_endpoint
            params = {}
            
            if start_date:
                params['dataInicial'] = start_date
            if end_date:
                params['dataFinal'] = end_date
            
            # Make request with fail-fast timeout
            response = self.session.get(
                url,
                params=params,
                timeout=self.timeout
            )
            
            # Fail fast on HTTP errors
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.error("❌ BCB API returned empty SELIC data")
                raise Exception("BCB API returned no SELIC data")
            
            # Get latest SELIC rate
            latest = data[-1]
            
            selic_rate = BCBSelicRate(
                rate=float(latest['valor']),
                date=latest['data'],
                source="BCB_Official"
            )
            
            request_time = (time.time() - start_time) * 1000
            logger.info(f"✅ SELIC rate: {selic_rate.rate:.2f}% ({request_time:.1f}ms)")
            
            return selic_rate
            
        except requests.exceptions.Timeout:
            logger.error(f"❌ BCB SELIC API timeout after {self.timeout}s")
            raise Exception(f"BCB SELIC API timeout - service unavailable")
            
        except requests.exceptions.ConnectionError:
            logger.error("❌ BCB SELIC API connection error")
            raise Exception("BCB SELIC API connection failed - service unavailable")
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"❌ BCB SELIC API HTTP error: {e}")
            raise Exception(f"BCB SELIC API HTTP error: {e.response.status_code}")
            
        except Exception as e:
            logger.error(f"❌ BCB SELIC API error: {e}")
            raise Exception(f"BCB SELIC API error: {str(e)}")
    
    def get_market_data_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive market data summary from BCB.
        
        Returns:
            Dict with USD/BRL quotation and SELIC rate
            
        Raises:
            Exception: If any BCB API is unavailable (fail-fast)
        """
        start_time = time.time()
        
        try:
            logger.info("🏦 Fetching market data summary from BCB")
            
            # Get both quotation and SELIC rate
            usd_brl = self.get_usd_brl_quotation()
            selic = self.get_selic_rate()
            
            if not usd_brl or not selic:
                raise Exception("Failed to fetch complete market data from BCB")
            
            summary = {
                'usd_brl_quotation': {
                    'rate': usd_brl.rate,
                    'date': usd_brl.date,
                    'source': usd_brl.source,
                    'timestamp': usd_brl.timestamp
                },
                'selic_rate': {
                    'rate': selic.rate,
                    'date': selic.date,
                    'source': selic.source,
                    'timestamp': selic.timestamp
                },
                'summary_metadata': {
                    'fetch_time_ms': (time.time() - start_time) * 1000,
                    'data_source': 'BCB_Official',
                    'fail_fast': True,
                    'no_fallbacks': True,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"✅ Market data summary fetched successfully")
            return summary
            
        except Exception as e:
            logger.error(f"❌ Market data summary failed: {e}")
            raise Exception(f"BCB market data unavailable: {str(e)}")
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check BCB API health status.
        
        Returns:
            Dict with health status
        """
        try:
            # Quick health check with minimal data request
            start_time = time.time()
            
            # Test quotation endpoint
            response = self.session.get(
                self.quotation_endpoint,
                params={'dataInicial': '01/01/2024', 'dataFinal': '01/01/2024'},
                timeout=5.0
            )
            response.raise_for_status()
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'healthy',
                'response_time_ms': response_time,
                'timestamp': datetime.now().isoformat(),
                'endpoints_tested': ['quotation'],
                'fail_fast_enabled': True
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'fail_fast_enabled': True
            }


# Singleton instance
_banco_central_api: Optional[BancoCentralAPI] = None


def get_banco_central_api() -> BancoCentralAPI:
    """Get singleton instance of BancoCentralAPI."""
    global _banco_central_api
    if _banco_central_api is None:
        _banco_central_api = BancoCentralAPI()
    return _banco_central_api
