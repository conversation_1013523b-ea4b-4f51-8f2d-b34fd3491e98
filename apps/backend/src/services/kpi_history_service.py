"""
KPI History Service for DataHero4 Drawer Implementation
======================================================

Service for retrieving and formatting historical KPI data used in drawer components.
Provides the raw tabular data that feeds into KPI calculations, showing trends
and historical values over specified timeframes.

Features:
- Real database queries for historical data
- Multiple timeframe support (1d, week, month, quarter)
- Currency filtering
- Data formatting for frontend consumption
- Integration with existing KPI calculation system

Author: DataHero4 Team
Date: 2025-01-29
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlmodel import Session, select, text
from contextlib import contextmanager

from src.caching.unified_cache_system import get_unified_cache
from src.config.kpi_definitions import get_kpi_definition, TIMEFRAME_CONFIG
from src.utils.timeframe_utils import convert_to_interval, convert_to_days
from src.models.kpi_models import Transaction

logger = logging.getLogger(__name__)


class KpiHistoryService:
    """Service for managing KPI historical data."""
    
    def __init__(self, db_session: Session):
        self.session = db_session
        self.cache = get_unified_cache()
        
    def get_kpi_history_data(
        self,
        kpi_id: str,
        client_id: str = "default",
        timeframe: str = "week",
        currency: str = "all"
    ) -> Dict[str, Any]:
        """
        Get historical data for a specific KPI.
        
        Args:
            kpi_id: KPI identifier
            client_id: Client identifier
            timeframe: Time period for data
            currency: Currency filter
            
        Returns:
            Dictionary with historical data points
        """
        try:
            logger.info(f"📊 Getting history for KPI {kpi_id} - timeframe: {timeframe}")
            
            # Check cache first
            cache_key = f"kpi_history:{kpi_id}:{client_id}:{timeframe}:{currency}"
            cached_result = self.cache.get(cache_key)
            
            if cached_result is not None:
                logger.info(f"✅ KPI history from cache: {kpi_id}")
                return cached_result
            
            # Get KPI definition
            kpi_def = get_kpi_definition(kpi_id)
            if not kpi_def:
                return {
                    'error': 'kpi_not_found',
                    'message': f'KPI {kpi_id} not found in definitions'
                }
            
            # Get historical data based on KPI type
            history_data = self._get_historical_data_points(
                kpi_id, kpi_def, timeframe, currency
            )
            
            # Format response
            result = self.format_history_response(
                kpi_id, kpi_def, history_data, timeframe, currency
            )
            
            # Cache result
            cache_ttl = kpi_def.get('cache_ttl', 600)  # 10 minutes default
            self.cache.set(cache_key, result, ttl=cache_ttl)
            
            logger.info(f"✅ Retrieved {len(history_data)} history points for KPI {kpi_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error getting KPI history for {kpi_id}: {str(e)}")
            return {
                'error': 'history_calculation_failed',
                'message': f'Failed to calculate history for KPI {kpi_id}',
                'details': str(e)
            }
    
    def _get_historical_data_points(
        self,
        kpi_id: str,
        kpi_def: Dict[str, Any],
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Get raw historical data points for the KPI."""
        
        # Get timeframe configuration
        days = convert_to_days(timeframe)
        interval = convert_to_interval(timeframe)
        
        # Build currency filter
        currency_filter = self._build_currency_filter(currency)
        
        # Get historical data based on KPI category
        category = kpi_def.get('category', 'volume')
        
        if category.value == 'volume':
            return self._get_volume_history(days, currency_filter)
        elif category.value == 'spread':
            return self._get_spread_history(days, currency_filter)
        elif category.value == 'conversion':
            return self._get_conversion_history(days, currency_filter)
        else:
            # Default: transaction-based history
            return self._get_transaction_history(days, currency_filter)
    
    def _get_volume_history(self, days: int, currency_filter: str) -> List[Dict[str, Any]]:
        """Get volume-based historical data."""
        query = text(f"""
            SELECT 
                DATE(created_at) as period,
                SUM(amount) as value,
                COUNT(*) as transaction_count
            FROM transactions 
            WHERE created_at >= CURRENT_DATE - INTERVAL '{days} days'
            AND {currency_filter}
            GROUP BY DATE(created_at)
            ORDER BY period DESC
            LIMIT 30
        """)
        
        result = self.session.exec(query)
        return [
            {
                'period': str(row.period),
                'value': float(row.value or 0),
                'transaction_count': int(row.transaction_count),
                'metadata': {'type': 'volume'}
            }
            for row in result
        ]
    
    def _get_spread_history(self, days: int, currency_filter: str) -> List[Dict[str, Any]]:
        """Get spread-based historical data."""
        query = text(f"""
            SELECT 
                DATE(created_at) as period,
                AVG(spread) as value,
                COUNT(*) as transaction_count
            FROM transactions 
            WHERE created_at >= CURRENT_DATE - INTERVAL '{days} days'
            AND spread IS NOT NULL
            AND {currency_filter}
            GROUP BY DATE(created_at)
            ORDER BY period DESC
            LIMIT 30
        """)
        
        result = self.session.exec(query)
        return [
            {
                'period': str(row.period),
                'value': float(row.value or 0),
                'transaction_count': int(row.transaction_count),
                'metadata': {'type': 'spread'}
            }
            for row in result
        ]
    
    def _get_conversion_history(self, days: int, currency_filter: str) -> List[Dict[str, Any]]:
        """Get conversion-based historical data."""
        # This would need more complex logic based on conversion tracking
        # For now, return transaction-based data
        return self._get_transaction_history(days, currency_filter)
    
    def _get_transaction_history(self, days: int, currency_filter: str) -> List[Dict[str, Any]]:
        """Get general transaction-based historical data."""
        query = text(f"""
            SELECT 
                DATE(created_at) as period,
                COUNT(*) as value,
                SUM(amount) as total_amount
            FROM transactions 
            WHERE created_at >= CURRENT_DATE - INTERVAL '{days} days'
            AND {currency_filter}
            GROUP BY DATE(created_at)
            ORDER BY period DESC
            LIMIT 30
        """)
        
        result = self.session.exec(query)
        return [
            {
                'period': str(row.period),
                'value': float(row.value or 0),
                'total_amount': float(row.total_amount or 0),
                'metadata': {'type': 'transaction_count'}
            }
            for row in result
        ]
    
    def _build_currency_filter(self, currency: str) -> str:
        """Build SQL currency filter."""
        if currency == "all":
            return "1=1"
        elif currency == "usd":
            return "currency = 'USD'"
        elif currency == "eur":
            return "currency = 'EUR'"
        elif currency == "gbp":
            return "currency = 'GBP'"
        else:
            return "1=1"
    
    def format_history_response(
        self,
        kpi_id: str,
        kpi_def: Dict[str, Any],
        history_data: List[Dict[str, Any]],
        timeframe: str,
        currency: str
    ) -> Dict[str, Any]:
        """Format historical data for API response."""
        
        # Calculate change percentages
        formatted_history = []
        for i, item in enumerate(history_data):
            # Calculate change from previous period
            change_percent = None
            status = "neutral"
            
            if i < len(history_data) - 1:
                current_value = item['value']
                previous_value = history_data[i + 1]['value']
                
                if previous_value > 0:
                    change_percent = ((current_value - previous_value) / previous_value) * 100
                    status = "positive" if change_percent > 0 else "negative" if change_percent < 0 else "neutral"
            
            # Format value based on KPI type
            format_type = kpi_def.get('format_type', 'number')
            formatted_value = self._format_value(item['value'], format_type)
            
            formatted_history.append({
                'period': item['period'],
                'value': item['value'],
                'formatted_value': formatted_value,
                'change_percent': change_percent,
                'status': status,
                'metadata': item.get('metadata', {})
            })
        
        return {
            'kpi_id': kpi_id,
            'kpi_name': kpi_def.get('name', kpi_id),
            'timeframe': timeframe,
            'currency': currency,
            'total_records': len(formatted_history),
            'history_data': formatted_history,
            'calculation_metadata': {
                'format_type': kpi_def.get('format_type', 'number'),
                'unit': kpi_def.get('unit', ''),
                'category': kpi_def.get('category', '').value if hasattr(kpi_def.get('category', ''), 'value') else str(kpi_def.get('category', ''))
            },
            'generated_at': datetime.now().isoformat()
        }
    
    def _format_value(self, value: float, format_type: str) -> str:
        """Format value based on type."""
        if format_type == 'currency':
            return f"${value:,.2f}"
        elif format_type == 'percentage':
            return f"{value:.2f}%"
        elif format_type == 'duration':
            return f"{value:.1f}s"
        else:
            return f"{value:,.0f}"
    
    def validate_history_request(
        self,
        kpi_id: str,
        timeframe: str,
        currency: str
    ) -> Dict[str, Any]:
        """Validate history request parameters."""
        
        errors = []
        
        # Validate KPI ID
        if not get_kpi_definition(kpi_id):
            errors.append(f"Invalid KPI ID: {kpi_id}")
        
        # Validate timeframe
        valid_timeframes = ['1d', 'week', 'month', 'quarter']
        if timeframe not in valid_timeframes:
            errors.append(f"Invalid timeframe: {timeframe}. Must be one of {valid_timeframes}")
        
        # Validate currency
        valid_currencies = ['all', 'usd', 'eur', 'gbp']
        if currency not in valid_currencies:
            errors.append(f"Invalid currency: {currency}. Must be one of {valid_currencies}")
        
        if errors:
            return {
                'valid': False,
                'errors': errors
            }
        
        return {'valid': True}


# Dependency injection function
def get_kpi_history_service(session: Session) -> KpiHistoryService:
    """Get KPI history service instance."""
    return KpiHistoryService(session)
