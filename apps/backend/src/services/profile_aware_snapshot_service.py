#!/usr/bin/env python3
"""
Profile-Aware Snapshot Service for DataHero4 Hybrid Architecture
================================================================

Extends the existing SnapshotService to generate profile-specific snapshots
using the kpi_snapshots_v2 table with partitioning and JSONB dimensions.

This service:
1. Extends existing SnapshotService without breaking changes
2. Generates snapshots personalized by user profile
3. Uses kpi_snapshots_v2 table with partitioning
4. Maintains 3AM cron system compatibility
5. Implements fail-fast validation without fallbacks

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import time
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from sqlalchemy import text

from src.services.snapshot_service import SnapshotService
from src.utils.learning_db_utils import get_db_manager
from src.config.kpi_definitions import CRITICAL_KPIS, KPI_DEFINITIONS

logger = logging.getLogger(__name__)


class ProfileAwareSnapshotService(SnapshotService):
    """
    Profile-aware extension of SnapshotService for hybrid architecture.
    
    Generates snapshots personalized by user profile using kpi_snapshots_v2
    table with PostgreSQL partitioning and JSONB dimensions.
    """
    
    def __init__(self):
        super().__init__()
        self.profile_configs = self._load_profile_configurations()
    
    def _load_profile_configurations(self) -> Dict[str, Dict[str, Any]]:
        """Load profile-specific configurations from user_profiles table."""
        try:
            with self.db_manager.get_session() as session:
                # Load profile configurations from database
                profiles_sql = text("""
                    SELECT 
                        profile_type,
                        preferences,
                        selected_kpis,
                        cache_ttl,
                        preferred_timeframe
                    FROM user_profiles 
                    WHERE detection_method = 'template'
                    AND is_active = true
                    ORDER BY profile_type;
                """)
                
                profiles = session.execute(profiles_sql).fetchall()
                
                profile_configs = {}
                for profile in profiles:
                    profile_configs[profile.profile_type] = {
                        'preferences': profile.preferences,
                        'selected_kpis': profile.selected_kpis,
                        'cache_ttl': profile.cache_ttl,
                        'preferred_timeframe': profile.preferred_timeframe,
                        'dimensions': self._get_profile_dimensions(profile.profile_type),
                        'priority_kpis': self._get_priority_kpis(profile.profile_type)
                    }
                
                logger.info(f"✅ Loaded {len(profile_configs)} profile configurations")
                return profile_configs
                
        except Exception as e:
            logger.error(f"❌ Error loading profile configurations: {e}")
            # Fail fast - no fallback to default configurations
            raise ValueError(f"Failed to load profile configurations: {e}")
    
    def _get_profile_dimensions(self, profile_type: str) -> Dict[str, Any]:
        """Get profile-specific dimensions for JSONB storage."""
        dimensions_map = {
            'CEO': {
                'granularity': 'daily',
                'focus': 'strategic',
                'timeframe': 'month',
                'alert_threshold': 'high',
                'dashboard_sections': ['revenue', 'growth', 'efficiency']
            },
            'CFO': {
                'granularity': 'daily',
                'focus': 'financial',
                'timeframe': 'week',
                'alert_threshold': 'medium',
                'dashboard_sections': ['profitability', 'costs', 'margins']
            },
            'Risk_Manager': {
                'granularity': 'hourly',
                'focus': 'risk',
                'timeframe': '1d',
                'alert_threshold': 'low',
                'dashboard_sections': ['exposure', 'limits', 'volatility']
            },
            'Trader': {
                'granularity': 'minute',
                'focus': 'operational',
                'timeframe': '1d',
                'alert_threshold': 'low',
                'dashboard_sections': ['spreads', 'volumes', 'performance']
            },
            'Operations': {
                'granularity': 'hourly',
                'focus': 'efficiency',
                'timeframe': 'week',
                'alert_threshold': 'medium',
                'dashboard_sections': ['efficiency', 'processing', 'quality']
            }
        }
        return dimensions_map.get(profile_type, {})
    
    def _get_priority_kpis(self, profile_type: str) -> List[str]:
        """Get priority KPIs for each profile type."""
        priority_map = {
            'CEO': ['spread_income_detailed', 'margem_liquida_operacional', 'volume_total', 'crescimento_receita'],
            'CFO': ['margem_liquida_operacional', 'custo_por_transacao', 'roe', 'spread_income_detailed'],
            'Risk_Manager': ['var_diario', 'exposicao_cambial', 'utilizacao_limites', 'volatilidade'],
            'Trader': ['spread_realtime', 'volume_hora', 'tempo_processamento_medio', 'spread_income_detailed'],
            'Operations': ['tempo_processamento_medio', 'custo_por_transacao', 'taxa_erro', 'volume_processado']
        }
        return priority_map.get(profile_type, CRITICAL_KPIS)
    
    def generate_profile_snapshots(self, client_id: str) -> Dict[str, Any]:
        """
        Generate snapshots for all active profiles.
        
        This method extends the existing 3AM cron system to generate
        profile-specific snapshots while maintaining backward compatibility.
        
        Args:
            client_id: Client identifier
            
        Returns:
            Dict with generation results for all profiles
        """
        start_time = time.time()
        logger.info(f"🚀 Starting profile-aware snapshot generation for client: {client_id}")
        
        results = {
            'client_id': client_id,
            'generated_at': datetime.now().isoformat(),
            'profiles': {},
            'summary': {
                'total_profiles': 0,
                'successful_profiles': 0,
                'failed_profiles': 0,
                'total_kpis': 0,
                'duration_seconds': 0
            }
        }
        
        try:
            # Generate snapshots for each profile
            for profile_type, config in self.profile_configs.items():
                try:
                    logger.info(f"📊 Generating snapshot for profile: {profile_type}")
                    
                    profile_snapshot = self._generate_single_profile_snapshot(
                        client_id=client_id,
                        profile_type=profile_type,
                        config=config
                    )
                    
                    results['profiles'][profile_type] = profile_snapshot
                    results['summary']['successful_profiles'] += 1
                    results['summary']['total_kpis'] += len(profile_snapshot.get('kpis', {}))
                    
                    logger.info(f"✅ Profile {profile_type} snapshot generated successfully")
                    
                except Exception as profile_error:
                    logger.error(f"❌ Failed to generate snapshot for profile {profile_type}: {profile_error}")
                    results['profiles'][profile_type] = {
                        'error': str(profile_error),
                        'status': 'failed',
                        'generated_at': datetime.now().isoformat()
                    }
                    results['summary']['failed_profiles'] += 1
            
            # Update summary
            results['summary']['total_profiles'] = len(self.profile_configs)
            results['summary']['duration_seconds'] = time.time() - start_time
            
            # Save profile snapshots to kpi_snapshots_v2
            self._save_profile_snapshots(results)
            
            logger.info(f"🎉 Profile snapshot generation completed: {results['summary']['successful_profiles']}/{results['summary']['total_profiles']} profiles")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Critical error in profile snapshot generation: {e}")
            results['summary']['duration_seconds'] = time.time() - start_time
            results['error'] = str(e)
            return results
    
    def _generate_single_profile_snapshot(
        self, 
        client_id: str, 
        profile_type: str, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate snapshot for a single profile."""
        
        # Get profile-specific KPIs
        profile_kpis = config.get('priority_kpis', CRITICAL_KPIS)
        dimensions = config.get('dimensions', {})
        
        # Calculate KPIs for this profile
        kpi_data = {}
        failed_kpis = []
        
        for kpi_id in profile_kpis:
            try:
                # Get KPI definition
                kpi_def = self._get_kpi_definition(kpi_id)
                if not kpi_def:
                    logger.warning(f"⚠️ KPI definition not found: {kpi_id}")
                    continue
                
                # Calculate KPI with profile-specific parameters
                kpi_result = self._calculate_profile_kpi(
                    kpi_def=kpi_def,
                    client_id=client_id,
                    profile_type=profile_type,
                    config=config
                )
                
                if kpi_result and kpi_result.get('currentValue') is not None:
                    kpi_data[kpi_id] = {
                        'value': kpi_result['currentValue'],
                        'formatted': self._format_kpi_value(
                            kpi_result['currentValue'],
                            kpi_def['format_type']
                        ),
                        'title': kpi_def['name'],
                        'description': kpi_def['description'],
                        'profile_context': self._get_profile_context(kpi_id, profile_type),
                        'dimensions': dimensions,
                        'status': 'success'
                    }
                else:
                    failed_kpis.append(kpi_id)
                    
            except Exception as kpi_error:
                logger.error(f"❌ Error calculating KPI {kpi_id} for profile {profile_type}: {kpi_error}")
                failed_kpis.append(kpi_id)
        
        # Create profile snapshot structure
        successful_kpis = len([k for k in kpi_data.values() if k.get('status') == 'success'])
        
        return {
            'profile_type': profile_type,
            'client_id': client_id,
            'generated_at': datetime.now().isoformat(),
            'kpis': kpi_data,
            'dimensions': dimensions,
            'metadata': {
                'total_kpis': len(profile_kpis),
                'successful_kpis': successful_kpis,
                'failed_kpis': failed_kpis,
                'success_rate': (successful_kpis / len(profile_kpis) * 100) if profile_kpis else 0,
                'cache_ttl': config.get('cache_ttl', 3600),
                'preferred_timeframe': config.get('preferred_timeframe', 'week')
            },
            'status': 'success' if successful_kpis > 0 else 'failed'
        }
    
    def _get_kpi_definition(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Get KPI definition from database or configuration."""
        try:
            if self.db_manager:
                with self.db_manager.get_session() as session:
                    from src.models.learning_models import KpiDefinition
                    kpi = session.query(KpiDefinition).filter_by(id=kpi_id, is_active=True).first()
                    if kpi:
                        return kpi.to_dict()
            
            # Fallback to configuration
            return KPI_DEFINITIONS.get(kpi_id)
            
        except Exception as e:
            logger.error(f"❌ Error getting KPI definition for {kpi_id}: {e}")
            return KPI_DEFINITIONS.get(kpi_id)
    
    def _calculate_profile_kpi(
        self, 
        kpi_def: Dict[str, Any], 
        client_id: str, 
        profile_type: str, 
        config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Calculate KPI with profile-specific parameters."""
        try:
            # Use profile-specific timeframe
            timeframe = config.get('preferred_timeframe', 'week')
            
            # Calculate using existing service with profile context
            return self.kpi_service._calculate_single_kpi(
                kpi_def=kpi_def,
                client_id=client_id,
                timeframe=timeframe,
                currency="all"
            )
            
        except Exception as e:
            logger.error(f"❌ Error calculating profile KPI {kpi_def['id']}: {e}")
            return None
    
    def _get_profile_context(self, kpi_id: str, profile_type: str) -> Dict[str, Any]:
        """Get profile-specific context for KPI interpretation."""
        context_map = {
            'CEO': {
                'interpretation': 'strategic',
                'focus': 'growth and efficiency',
                'alert_level': 'executive'
            },
            'CFO': {
                'interpretation': 'financial',
                'focus': 'profitability and costs',
                'alert_level': 'financial'
            },
            'Risk_Manager': {
                'interpretation': 'risk',
                'focus': 'exposure and limits',
                'alert_level': 'risk'
            },
            'Trader': {
                'interpretation': 'operational',
                'focus': 'real-time performance',
                'alert_level': 'operational'
            },
            'Operations': {
                'interpretation': 'efficiency',
                'focus': 'process optimization',
                'alert_level': 'operational'
            }
        }
        return context_map.get(profile_type, {})
    
    def _save_profile_snapshots(self, results: Dict[str, Any]):
        """Save profile snapshots to kpi_snapshots_v2 table."""
        try:
            if not self.db_manager:
                raise ValueError("Database manager not available for profile snapshot storage")
            
            with self.db_manager.get_session() as session:
                current_date = date.today()
                
                for profile_type, snapshot_data in results['profiles'].items():
                    if snapshot_data.get('status') != 'success':
                        continue
                    
                    # Save each KPI as a separate record in kpi_snapshots_v2
                    for kpi_id, kpi_data in snapshot_data.get('kpis', {}).items():
                        try:
                            insert_sql = text("""
                                INSERT INTO kpi_snapshots_v2 (
                                    client_id, kpi_id, user_profile, period_type, period_date,
                                    dimensions, metrics, created_at
                                ) VALUES (
                                    :client_id, :kpi_id, :user_profile, :period_type, :period_date,
                                    :dimensions::jsonb, :metrics::jsonb, NOW()
                                ) ON CONFLICT (client_id, kpi_id, user_profile, period_type, period_date) 
                                DO UPDATE SET
                                    dimensions = EXCLUDED.dimensions,
                                    metrics = EXCLUDED.metrics,
                                    created_at = NOW();
                            """)
                            
                            session.execute(insert_sql, {
                                'client_id': results['client_id'],
                                'kpi_id': kpi_id,
                                'user_profile': profile_type,
                                'period_type': 'daily',
                                'period_date': current_date,
                                'dimensions': kpi_data.get('dimensions', {}),
                                'metrics': {
                                    'value': kpi_data['value'],
                                    'formatted': kpi_data['formatted'],
                                    'title': kpi_data['title'],
                                    'description': kpi_data['description'],
                                    'profile_context': kpi_data.get('profile_context', {}),
                                    'status': kpi_data['status']
                                }
                            })
                            
                        except Exception as kpi_save_error:
                            logger.error(f"❌ Error saving KPI {kpi_id} for profile {profile_type}: {kpi_save_error}")
                
                session.commit()
                logger.info(f"✅ Profile snapshots saved to kpi_snapshots_v2 table")
                
        except Exception as e:
            logger.error(f"❌ Error saving profile snapshots: {e}")
            raise
    
    def get_profile_snapshot(
        self, 
        client_id: str, 
        profile_type: str, 
        period_date: Optional[date] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve profile-specific snapshot from kpi_snapshots_v2.
        
        Args:
            client_id: Client identifier
            profile_type: User profile type
            period_date: Specific date (defaults to today)
            
        Returns:
            Profile snapshot or None if not found
        """
        try:
            if not self.db_manager:
                raise ValueError("Database manager not available")
            
            if period_date is None:
                period_date = date.today()
            
            with self.db_manager.get_session() as session:
                query_sql = text("""
                    SELECT 
                        kpi_id,
                        dimensions,
                        metrics,
                        created_at
                    FROM kpi_snapshots_v2
                    WHERE client_id = :client_id
                    AND user_profile = :profile_type
                    AND period_type = 'daily'
                    AND period_date = :period_date
                    ORDER BY kpi_id;
                """)
                
                results = session.execute(query_sql, {
                    'client_id': client_id,
                    'profile_type': profile_type,
                    'period_date': period_date
                }).fetchall()
                
                if not results:
                    logger.info(f"📭 No snapshot found for profile {profile_type} on {period_date}")
                    return None
                
                # Reconstruct snapshot from individual KPI records
                kpis = {}
                for row in results:
                    kpis[row.kpi_id] = {
                        **row.metrics,
                        'dimensions': row.dimensions
                    }
                
                return {
                    'profile_type': profile_type,
                    'client_id': client_id,
                    'period_date': period_date.isoformat(),
                    'kpis': kpis,
                    'retrieved_at': datetime.now().isoformat(),
                    'source': 'kpi_snapshots_v2'
                }
                
        except Exception as e:
            logger.error(f"❌ Error retrieving profile snapshot: {e}")
            return None


# Extend the existing cron system to support profiles
def generate_profile_aware_snapshots_cli():
    """CLI function for profile-aware snapshot generation."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        service = ProfileAwareSnapshotService()
        
        # Generate traditional snapshot (backward compatibility)
        logger.info("🔄 Generating traditional snapshot for backward compatibility...")
        traditional_snapshot = service.generate_daily_snapshot("L2M")
        
        # Generate profile-aware snapshots
        logger.info("🚀 Generating profile-aware snapshots...")
        profile_results = service.generate_profile_snapshots("L2M")
        
        # Check results
        if profile_results.get('error'):
            logger.error(f"❌ Profile snapshot generation failed: {profile_results['error']}")
            return 1
        
        successful_profiles = profile_results['summary']['successful_profiles']
        total_profiles = profile_results['summary']['total_profiles']
        
        if successful_profiles == 0:
            logger.error("❌ No profile snapshots generated successfully")
            return 1
        
        logger.info(f"🎉 Profile snapshot generation completed: {successful_profiles}/{total_profiles} profiles")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Critical error in profile snapshot CLI: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(generate_profile_aware_snapshots_cli())
