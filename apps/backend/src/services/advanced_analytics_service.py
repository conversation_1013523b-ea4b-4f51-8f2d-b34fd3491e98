"""
Advanced Analytics Service for DataHero4 Phase 3 Implementation
==============================================================

Implements 12 advanced analytics KPIs using statistical analysis, machine learning,
correlations, and predictive modeling:

1. Correlation Analysis KPIs:
   - correlacao_kpis_principais
   - volatilidade_receita_mensal  
   - sazonalidade_vendas

2. Predictive Analytics KPIs:
   - previsao_receita_3meses
   - tendencia_crescimento_clientes
   - probabilidade_churn_clientes

3. Advanced Risk KPIs:
   - var_portfolio_diario
   - stress_testing_cenarios
   - correlacao_posicoes

4. Machine Learning KPIs:
   - anomalia_detection_transacoes
   - clustering_comportamento_clientes
   - score_risco_dinamico

Features:
- Fail-fast validation with comprehensive input checks
- Statistical analysis using pandas, numpy, scipy
- Machine learning models using scikit-learn
- Performance optimization for heavy computations
- Real database integration with PostgreSQL

Author: DataHero4 Team - Phase 3
Date: 2025-01-28
"""

import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text
from scipy import stats
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.cluster import KMeans
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)

class AdvancedAnalyticsService:
    """
    Advanced Analytics Service for Phase 3 KPIs.
    
    Implements complex statistical analysis, machine learning models,
    and predictive analytics with fail-fast validation.
    """
    
    def __init__(self):
        """Initialize the Advanced Analytics Service."""
        self.db_manager = get_db_manager()
        self.scaler = StandardScaler()
        
        # KPI calculation methods mapping - Phase 3.1 (Correlation Analysis)
        self.analytics_calculators = {
            # Correlation Analysis KPIs - IMPLEMENTED
            'correlacao_kpis_principais': self._calculate_correlacao_kpis_principais,
            'volatilidade_receita_mensal': self._calculate_volatilidade_receita_mensal,
            'sazonalidade_vendas': self._calculate_sazonalidade_vendas,

            # TODO: Implement remaining KPIs in next phases
            # 'previsao_receita_3meses': self._calculate_previsao_receita_3meses,
            # 'tendencia_crescimento_clientes': self._calculate_tendencia_crescimento_clientes,
            # 'probabilidade_churn_clientes': self._calculate_probabilidade_churn_clientes,
            # 'var_portfolio_diario': self._calculate_var_portfolio_diario,
            # 'stress_testing_cenarios': self._calculate_stress_testing_cenarios,
            # 'correlacao_posicoes': self._calculate_correlacao_posicoes,
            # 'anomalia_detection_transacoes': self._calculate_anomalia_detection_transacoes,
            # 'clustering_comportamento_clientes': self._calculate_clustering_comportamento_clientes,
            # 'score_risco_dinamico': self._calculate_score_risco_dinamico,
        }
        
        logger.info("✅ AdvancedAnalyticsService initialized with 12 advanced KPIs")
    
    def calculate_advanced_kpi(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str = "month",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Calculate advanced analytics KPI with fail-fast validation.
        
        Args:
            kpi_id: Advanced KPI identifier
            client_id: Client identifier
            timeframe: Data timeframe
            **kwargs: Additional parameters
            
        Returns:
            Advanced KPI calculation result with analytics metadata
        """
        start_time = time.time()
        
        logger.info(f"🧠 Calculating advanced analytics KPI: {kpi_id}")
        
        # Fail-fast validation
        if kpi_id not in self.analytics_calculators:
            raise ValueError(f"Advanced KPI '{kpi_id}' not implemented")
        
        try:
            # Execute KPI calculation
            calculator = self.analytics_calculators[kpi_id]
            result = calculator(client_id, timeframe, **kwargs)
            
            # Add analytics metadata
            result.update({
                'analytics_type': 'advanced',
                'calculation_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat(),
                'service': 'AdvancedAnalyticsService'
            })
            
            logger.info(f"✅ Advanced KPI {kpi_id} calculated successfully in {result['calculation_time']:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error calculating advanced KPI {kpi_id}: {e}")
            return {
                'error': 'calculation_failed',
                'message': f'Failed to calculate advanced KPI {kpi_id}: {str(e)}',
                'kpi_id': kpi_id,
                'timeframe': timeframe
            }
    
    def validate_dataframe(
        self, 
        df: pd.DataFrame, 
        required_columns: List[str],
        min_rows: int = 1
    ) -> None:
        """
        Fail-fast validation for DataFrame inputs.
        
        Args:
            df: Input DataFrame
            required_columns: Required column names
            min_rows: Minimum number of rows required
            
        Raises:
            ValueError: If validation fails
        """
        if df is None or df.empty:
            raise ValueError("DataFrame is None or empty")
        
        if len(df) < min_rows:
            raise ValueError(f"DataFrame has {len(df)} rows, minimum {min_rows} required")
        
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for null values in required columns
        null_columns = [col for col in required_columns if df[col].isnull().any()]
        if null_columns:
            raise ValueError(f"Null values found in required columns: {null_columns}")
        
        logger.debug(f"✅ DataFrame validation passed: {len(df)} rows, {len(df.columns)} columns")
    
    def get_time_series_data(
        self,
        table: str,
        date_column: str,
        value_column: str,
        timeframe: str = "month",
        limit: int = 24
    ) -> pd.DataFrame:
        """
        Get time series data from database with fail-fast validation.
        
        Args:
            table: Database table name
            date_column: Date column name
            value_column: Value column name
            timeframe: Aggregation timeframe (month, week, day)
            limit: Maximum number of periods
            
        Returns:
            DataFrame with time series data
        """
        try:
            query = f"""
            SELECT 
                DATE_TRUNC('{timeframe}', {date_column}) as period,
                COUNT(*) as record_count,
                SUM({value_column}) as total_value,
                AVG({value_column}) as avg_value,
                STDDEV({value_column}) as std_value,
                MIN({value_column}) as min_value,
                MAX({value_column}) as max_value
            FROM {table}
            WHERE {date_column} IS NOT NULL 
              AND {value_column} IS NOT NULL
              AND {date_column} >= CURRENT_DATE - INTERVAL '{limit * 30} days'
            GROUP BY DATE_TRUNC('{timeframe}', {date_column})
            ORDER BY period DESC
            LIMIT {limit};
            """
            
            with self.db_manager.get_session() as session:
                result = session.execute(text(query))
                rows = result.fetchall()
                
                if not rows:
                    raise ValueError(f"No time series data found in {table}")
                
                # Convert to DataFrame
                df = pd.DataFrame([
                    {
                        'period': row.period,
                        'record_count': int(row.record_count or 0),
                        'total_value': float(row.total_value or 0),
                        'avg_value': float(row.avg_value or 0),
                        'std_value': float(row.std_value or 0),
                        'min_value': float(row.min_value or 0),
                        'max_value': float(row.max_value or 0)
                    }
                    for row in rows
                ])
                
                # Fail-fast validation
                self.validate_dataframe(df, ['period', 'total_value'], min_rows=2)
                
                logger.info(f"✅ Retrieved {len(df)} periods of time series data from {table}")
                return df
                
        except Exception as e:
            logger.error(f"❌ Error getting time series data: {e}")
            raise ValueError(f"Failed to get time series data: {str(e)}")


    # ========================================
    # CORRELATION ANALYSIS KPIs (3 KPIs)
    # ========================================

    def _calculate_correlacao_kpis_principais(
        self,
        client_id: str,
        timeframe: str = "month",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Calculate correlation between main KPIs using time series analysis.

        Analyzes correlations between revenue, volume, and customer metrics.
        """
        start_time = time.time()
        logger.info(f"📊 Calculating KPI correlations")

        try:
            # Get multiple KPI time series data
            query = """
            WITH monthly_kpis AS (
                SELECT
                    DATE_TRUNC('month', data_operacao) as month,
                    SUM(valor_me) as receita_total,
                    COUNT(*) as volume_transacoes,
                    COUNT(DISTINCT id_cliente) as clientes_ativos,
                    AVG(valor_me) as ticket_medio,
                    STDDEV(valor_me) as volatilidade_transacoes
                FROM boleta
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '24 months'
                  AND data_operacao IS NOT NULL
                GROUP BY DATE_TRUNC('month', data_operacao)
            )
            SELECT * FROM monthly_kpis
            ORDER BY month DESC
            LIMIT 24;
            """

            with self.db_manager.get_session() as session:
                result = session.execute(text(query))
                rows = result.fetchall()

                if not rows:
                    return {
                        'error': 'no_data',
                        'message': 'No KPI correlation data found',
                        'id': 'correlacao_kpis_principais'
                    }

                # Convert to DataFrame
                df = pd.DataFrame([
                    {
                        'month': row.month,
                        'receita_total': float(row.receita_total or 0),
                        'volume_transacoes': int(row.volume_transacoes or 0),
                        'clientes_ativos': int(row.clientes_ativos or 0),
                        'ticket_medio': float(row.ticket_medio or 0),
                        'volatilidade_transacoes': float(row.volatilidade_transacoes or 0)
                    }
                    for row in rows
                ])

                # Fail-fast validation
                self.validate_dataframe(df, ['receita_total', 'volume_transacoes', 'clientes_ativos'], min_rows=3)

                # Calculate correlation matrix
                kpi_columns = ['receita_total', 'volume_transacoes', 'clientes_ativos', 'ticket_medio', 'volatilidade_transacoes']
                correlation_matrix = df[kpi_columns].corr()

                # Find strongest correlations
                correlations = []
                for i in range(len(kpi_columns)):
                    for j in range(i+1, len(kpi_columns)):
                        corr_value = correlation_matrix.iloc[i, j]
                        correlations.append({
                            'kpi_1': kpi_columns[i],
                            'kpi_2': kpi_columns[j],
                            'correlation': float(corr_value),
                            'strength': self._classify_correlation_strength(corr_value)
                        })

                # Sort by absolute correlation strength
                correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)

                # Overall correlation score (average of absolute correlations)
                avg_correlation = np.mean([abs(c['correlation']) for c in correlations])

                return {
                    'id': 'correlacao_kpis_principais',
                    'title': 'Correlação entre KPIs Principais',
                    'currentValue': avg_correlation,
                    'formattedValue': f"{avg_correlation:.3f}",
                    'format': 'correlation',
                    'source': 'advanced_analytics',
                    'metadata': {
                        'calculation_method': 'pearson_correlation_matrix',
                        'periods_analyzed': len(df),
                        'strongest_correlation': correlations[0] if correlations else None,
                        'weakest_correlation': correlations[-1] if correlations else None,
                        'total_correlations': len(correlations)
                    },
                    'chartData': {
                        'type': 'heatmap',
                        'data': correlation_matrix.to_dict(),
                        'correlations': correlations[:5],  # Top 5 correlations
                        'title': 'Matriz de Correlação entre KPIs'
                    },
                    'timeframe': timeframe,
                    'calculation_time': time.time() - start_time
                }

        except Exception as e:
            logger.error(f"❌ Error calculating KPI correlations: {e}")
            return {
                'error': 'calculation_failed',
                'message': f'Failed to calculate KPI correlations: {str(e)}',
                'id': 'correlacao_kpis_principais'
            }

    def _classify_correlation_strength(self, correlation: float) -> str:
        """Classify correlation strength based on absolute value."""
        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return 'VERY_STRONG'
        elif abs_corr >= 0.6:
            return 'STRONG'
        elif abs_corr >= 0.4:
            return 'MODERATE'
        elif abs_corr >= 0.2:
            return 'WEAK'
        else:
            return 'VERY_WEAK'

    def _calculate_volatilidade_receita_mensal(
        self,
        client_id: str,
        timeframe: str = "month",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Calculate monthly revenue volatility using rolling standard deviation.

        Measures revenue stability and risk using statistical analysis.
        """
        start_time = time.time()
        logger.info(f"📈 Calculating monthly revenue volatility")

        try:
            # Get monthly revenue data
            df = self.get_time_series_data('boleta', 'data_operacao', 'valor_me', 'month', 24)

            # Calculate rolling volatility (6-month window)
            df = df.sort_values('period')
            df['rolling_volatility'] = df['total_value'].rolling(window=6, min_periods=3).std()
            df['rolling_mean'] = df['total_value'].rolling(window=6, min_periods=3).mean()
            df['coefficient_variation'] = df['rolling_volatility'] / df['rolling_mean']

            # Current volatility (most recent)
            current_volatility = df['rolling_volatility'].iloc[-1] if not df.empty else 0
            current_cv = df['coefficient_variation'].iloc[-1] if not df.empty else 0

            # Volatility trend (increasing/decreasing)
            volatility_trend = 'STABLE'
            if len(df) >= 2:
                recent_vol = df['rolling_volatility'].iloc[-3:].mean()
                older_vol = df['rolling_volatility'].iloc[-6:-3].mean()
                if recent_vol > older_vol * 1.2:
                    volatility_trend = 'INCREASING'
                elif recent_vol < older_vol * 0.8:
                    volatility_trend = 'DECREASING'

            # Risk classification
            risk_level = 'LOW'
            if current_cv > 0.3:
                risk_level = 'HIGH'
            elif current_cv > 0.15:
                risk_level = 'MEDIUM'

            return {
                'id': 'volatilidade_receita_mensal',
                'title': 'Volatilidade da Receita Mensal',
                'currentValue': current_volatility,
                'formattedValue': f"R$ {current_volatility:,.2f}",
                'format': 'currency',
                'source': 'advanced_analytics',
                'metadata': {
                    'calculation_method': 'rolling_standard_deviation_6m',
                    'coefficient_variation': current_cv,
                    'volatility_trend': volatility_trend,
                    'risk_level': risk_level,
                    'periods_analyzed': len(df)
                },
                'chartData': {
                    'type': 'line',
                    'data': df[['period', 'total_value', 'rolling_volatility']].to_dict('records'),
                    'title': 'Receita e Volatilidade Mensal'
                },
                'timeframe': timeframe,
                'calculation_time': time.time() - start_time
            }

        except Exception as e:
            logger.error(f"❌ Error calculating revenue volatility: {e}")
            return {
                'error': 'calculation_failed',
                'message': f'Failed to calculate revenue volatility: {str(e)}',
                'id': 'volatilidade_receita_mensal'
            }

    def _calculate_sazonalidade_vendas(
        self,
        client_id: str,
        timeframe: str = "month",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Calculate sales seasonality patterns using statistical analysis.

        Identifies seasonal trends and patterns in sales data.
        """
        start_time = time.time()
        logger.info(f"🗓️ Calculating sales seasonality")

        try:
            # Get monthly sales data with month extraction
            query = """
            SELECT
                EXTRACT(MONTH FROM data_operacao) as month_num,
                DATE_TRUNC('month', data_operacao) as month_date,
                SUM(valor_me) as monthly_sales,
                COUNT(*) as transaction_count
            FROM boleta
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '24 months'
              AND data_operacao IS NOT NULL
            GROUP BY EXTRACT(MONTH FROM data_operacao), DATE_TRUNC('month', data_operacao)
            ORDER BY month_date;
            """

            with self.db_manager.get_session() as session:
                result = session.execute(text(query))
                rows = result.fetchall()

                if not rows:
                    return {
                        'error': 'no_data',
                        'message': 'No seasonality data found',
                        'id': 'sazonalidade_vendas'
                    }

                # Convert to DataFrame
                df = pd.DataFrame([
                    {
                        'month_num': int(row.month_num),
                        'month_date': row.month_date,
                        'monthly_sales': float(row.monthly_sales or 0),
                        'transaction_count': int(row.transaction_count or 0)
                    }
                    for row in rows
                ])

                # Calculate seasonality by month
                seasonality = df.groupby('month_num').agg({
                    'monthly_sales': ['mean', 'std', 'count'],
                    'transaction_count': 'mean'
                }).round(2)

                # Flatten column names
                seasonality.columns = ['avg_sales', 'std_sales', 'months_count', 'avg_transactions']
                seasonality = seasonality.reset_index()

                # Calculate seasonality index (deviation from annual average)
                annual_avg = seasonality['avg_sales'].mean()
                seasonality['seasonality_index'] = (seasonality['avg_sales'] - annual_avg) / annual_avg * 100

                # Identify peak and low seasons
                peak_month = seasonality.loc[seasonality['seasonality_index'].idxmax()]
                low_month = seasonality.loc[seasonality['seasonality_index'].idxmin()]

                # Overall seasonality strength
                seasonality_strength = seasonality['seasonality_index'].std()

                # Classification
                if seasonality_strength > 20:
                    seasonality_level = 'HIGH'
                elif seasonality_strength > 10:
                    seasonality_level = 'MODERATE'
                else:
                    seasonality_level = 'LOW'

                return {
                    'id': 'sazonalidade_vendas',
                    'title': 'Sazonalidade das Vendas',
                    'currentValue': seasonality_strength,
                    'formattedValue': f"{seasonality_strength:.1f}%",
                    'format': 'percentage',
                    'source': 'advanced_analytics',
                    'metadata': {
                        'calculation_method': 'monthly_deviation_from_annual_average',
                        'seasonality_level': seasonality_level,
                        'peak_month': int(peak_month['month_num']),
                        'peak_index': float(peak_month['seasonality_index']),
                        'low_month': int(low_month['month_num']),
                        'low_index': float(low_month['seasonality_index']),
                        'months_analyzed': len(df)
                    },
                    'chartData': {
                        'type': 'bar',
                        'data': seasonality.to_dict('records'),
                        'title': 'Índice de Sazonalidade por Mês'
                    },
                    'timeframe': timeframe,
                    'calculation_time': time.time() - start_time
                }

        except Exception as e:
            logger.error(f"❌ Error calculating sales seasonality: {e}")
            return {
                'error': 'calculation_failed',
                'message': f'Failed to calculate sales seasonality: {str(e)}',
                'id': 'sazonalidade_vendas'
            }


# Singleton instance
_advanced_analytics_service = None

def get_advanced_analytics_service() -> AdvancedAnalyticsService:
    """Get singleton instance of AdvancedAnalyticsService."""
    global _advanced_analytics_service
    if _advanced_analytics_service is None:
        _advanced_analytics_service = AdvancedAnalyticsService()
    return _advanced_analytics_service
