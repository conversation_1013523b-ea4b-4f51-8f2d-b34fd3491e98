"""DataHero Unified API - Production-ready with all features."""

import os
import sys
import time
import uuid
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Core imports
from src.graphs.optimized_workflow import create_optimized_workflow

# CRITICAL FIX: Lazy-load graph to avoid async/threading issues
_graph = None

def get_graph():
    """Get LangGraph instance with lazy loading to avoid async issues."""
    global _graph
    if _graph is None:
        logger.info("🏗️ Lazy-loading LangGraph workflow...")
        _graph = create_optimized_workflow()
        logger.info("✅ LangGraph workflow loaded successfully")
    return _graph
from src.models.feedback import (
    FeedbackCorrection, FeedbackResponse, FeedbackSummary,
    create_feedback_from_natural_language, FeedbackSource
)
from src.utils.learning_db_utils import Learning<PERSON><PERSON>ana<PERSON>, get_db_manager
from src.models.learning_models import FeedbackCorrection as PGFeedbackCorrection
# from src.utils.frontend_processor import frontend_processor  # Não mais necessário - LLM gera diretamente
from sqlalchemy import text

# Load environment variables
load_dotenv()

# Import chat API router
from src.interfaces.chat_api import chat_router

# Import dashboard API router
from src.interfaces.dashboard_api import dashboard_router

# Import monitoring API router
from src.api.monitoring_routes import router as monitoring_router

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)  # Garante que os logs vão para stdout
    ]
)
logger = logging.getLogger(__name__)

# Teste de log na inicialização
logger.debug("🔍 [DEBUG] API inicializando em modo debug")
logger.info("ℹ️ [INFO] API inicializando")

# Initialize FastAPI
app = FastAPI(
    title="DataHero API",
    description="DataHero LangGraph API with Learning and Feedback",
    version="4.0.0"
)

# -------------------------------------------------
# CORS Configuration
# -------------------------------------------------
# The allowed origins can be controlled via the `CORS_ALLOW_ORIGINS` environment
# variable (comma-separated list). If the variable is not set, we fall back to a
# sane default that works for local development and production.
origins_env = os.getenv("CORS_ALLOW_ORIGINS")

if origins_env:
    # Split by comma, remove surrounding whitespace and discard empty strings
    origins = [o.strip() for o in origins_env.split(",") if o.strip()]
else:
    origins = [
        "http://localhost:8080",  # Vite dev server (default port)
        "http://localhost:8081",  # React dev server / alternative
        "http://localhost:3000",  # Vite dev server (default port)
        "http://localhost:3001",  # Vite dev server (alternative port)
        "http://localhost:3002",  # Vite dev server (alternative port 2)
        "https://frontend-production-324f.up.railway.app",  # Production frontend
    ]

# Apply CORS middleware with the configured origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include chat API router
app.include_router(chat_router)

# Include dashboard API router
app.include_router(dashboard_router)

# Include monitoring API router
app.include_router(monitoring_router)

# Include snapshot API router
from src.api.dashboard_snapshot import router as snapshot_router
app.include_router(snapshot_router)

# Dashboard KPIs are now handled by dashboard_api.py

# Include health API router
from src.api.health_routes import router as health_router
app.include_router(health_router)

# Include hybrid KPI API router
try:
    from src.interfaces.hybrid_kpi_api import hybrid_kpi_router
    app.include_router(hybrid_kpi_router)
    logger.info("✅ Hybrid KPI router included successfully")
except Exception as e:
    logger.error(f"❌ Failed to include hybrid KPI router: {e}")
    import traceback
    logger.error(traceback.format_exc())

# Include alert configuration API router
try:
    from src.api.alert_config_routes import alert_config_router
    app.include_router(alert_config_router)
    logger.info("✅ Alert configuration router included successfully")
except Exception as e:
    logger.error(f"❌ Failed to include alert configuration router: {e}")
    import traceback
    logger.error(traceback.format_exc())
# Cache warming on startup
@app.on_event("startup")
async def startup_event():
    """Warm up caches and initialize critical components on startup."""
    logger.info("🔥 Starting cache warming and component initialization...")
    
    try:
        # Import cache warming utilities
        from src.caching.context_singleton import ContextSingleton
        
        # 1. Warm context cache for main client/sector
        logger.info("🔥 Warming context cache for cambio/L2M...")
        ContextSingleton.preload_context("cambio", "L2M")
        
        # 2. Initialize database connections
        logger.info("🔗 Warming database connections...")
        if db_manager:
            # Test connection and warm connection pool
            with db_manager.get_session() as session:
                session.execute(text("SELECT 1"))
        
        # 3. Initialize and preload critical components
        logger.info("⚙️ Initializing and preloading critical components...")
        from src.optimization.lazy_loader import initialize_lazy_components
        initialize_lazy_components()
        
        # 4. Initialize workflow graph (already done but ensure it's ready)
        logger.info("🏗️ Ensuring workflow graph is ready...")
        _ = create_optimized_workflow()
        
        # 5. Initialize scheduler service for snapshot generation
        logger.info("📅 Initializing scheduler service...")
        from src.services.scheduler_service import get_scheduler_service
        scheduler_service = get_scheduler_service()
        await scheduler_service.start_scheduler()
        
        logger.info("✅ Cache warming and initialization completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Cache warming failed: {e}")
        # Don't fail startup, just log the error


@app.on_event("shutdown")
async def shutdown_event():
    """Clean shutdown of components."""
    logger.info("🛑 Starting application shutdown...")
    
    try:
        # Stop scheduler service
        logger.info("📅 Stopping scheduler service...")
        from src.services.scheduler_service import get_scheduler_service
        scheduler_service = get_scheduler_service()
        await scheduler_service.stop_scheduler()
        
        logger.info("✅ Application shutdown completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Shutdown failed: {e}")

# Static files will be mounted at the end of the file after all API routes are defined

# Initialize PostgreSQL learning system
try:
    db_manager = get_db_manager()
    LEARNING_SYSTEMS_AVAILABLE = True
    logger.info("✅ PostgreSQL learning system initialized successfully")
except Exception as e:
    db_manager = None
    LEARNING_SYSTEMS_AVAILABLE = False
    logger.error(f"❌ PostgreSQL learning system failed to initialize: {e}")


# Request/Response Models
class AskRequest(BaseModel):
    """Request model for asking questions."""
    question: str = Field(..., min_length=3, description="User question")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    channel: str = Field(default="api", description="Channel (api/cli/whatsapp)")
    session_id: Optional[str] = Field(None, description="Session ID for conversation tracking")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversational context")


class AskResponse(BaseModel):
    """Response model for ask endpoint with progressive disclosure."""
    query_id: str = Field(..., description="Unique query identifier")
    question: str = Field(..., description="Original question")
    sql_query: Optional[str] = Field(None, description="Generated SQL query")
    results: Optional[Any] = Field(None, description="Query execution results")
    direct_answer: Optional[str] = Field(None, description="Level 1: Direct answer to the question")
    analysis_level: Optional[str] = Field("direct_answer", description="Current analysis level")
    visualization_data: Optional[Dict[str, Any]] = Field(None, description="Chart/visualization data")
    error: Optional[str] = Field(None, description="Error message if any")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversational context")

    # CRITICAL FIX: Add validation fields to response model
    sql_valid: Optional[bool] = Field(None, description="SQL validation status")
    query_valid: Optional[bool] = Field(None, description="Query validation status")
    validation_errors: Optional[List[str]] = Field(default_factory=list, description="Validation errors")
    validation_warnings: Optional[List[str]] = Field(default_factory=list, description="Validation warnings")
    validation_confidence: Optional[float] = Field(None, description="Validation confidence score")
    validation_type: Optional[str] = Field(None, description="Validation type (cached, llm)")
    validation_suggestions: Optional[List[str]] = Field(default_factory=list, description="Validation suggestions")
    attempts: Optional[int] = Field(None, description="Number of attempts")

    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class FeedbackRequest(BaseModel):
    """Request model for submitting feedback."""
    query_id: str = Field(..., description="ID of query being corrected")
    feedback_text: str = Field(..., min_length=3, description="Feedback in natural language")
    original_question: str = Field(..., description="Original question asked")
    corrected_sql: Optional[str] = Field(None, description="Corrected SQL query")
    user_id: Optional[str] = Field(None, description="User providing feedback")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")


class SimpleFeedbackRequest(BaseModel):
    """Request model for simple positive/negative feedback."""
    query_id: str = Field(..., description="ID of query being rated")
    feedback_type: str = Field(..., description="positive or negative")
    original_question: str = Field(..., description="Original question asked")
    category: Optional[str] = Field(None, description="Feedback category for negative feedback")
    comment: Optional[str] = Field(None, description="Additional comment")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    user_id: Optional[str] = Field(None, description="User providing feedback")


class FeedbackReprocessRequest(BaseModel):
    """Request model for feedback submission with immediate reprocessing."""
    feedback: SimpleFeedbackRequest = Field(..., description="Feedback data")
    reprocess: bool = Field(default=True, description="Whether to reprocess immediately")


class ReprocessRequest(BaseModel):
    """Request to reprocess a question with learned corrections."""
    original_question: str = Field(..., description="Question to reprocess")
    query_id: Optional[str] = Field(None, description="Reference to original query")
    feedback_id: Optional[str] = Field(None, description="Specific feedback to apply")
    client_id: str = Field(default="L2M", description="Client identifier")
    sector: str = Field(default="cambio", description="Business sector")
    apply_recent_feedback: bool = Field(default=True, description="Apply recent feedback patterns")


class HealthResponse(BaseModel):
    """Health check response."""
    status: str = Field(..., description="System health status")
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = Field(default="4.0.0")
    database: Dict[str, Any] = Field(default_factory=dict)
    systems: Dict[str, Any] = Field(default_factory=dict)
    feature_flags: Dict[str, bool] = Field(default_factory=dict)


# Helper Functions
def build_cli_like_state(request: AskRequest) -> Dict[str, Any]:
    """Build initial state exactly like CLI does - no conversation history, minimal complexity."""
    import json
    from pathlib import Path
    
    # Use simple thread_id like CLI
    thread_id = f"api-cli-{request.client_id}-{int(time.time())}"
    
    logger.info(f"🔧 [DIAGNOSTIC] Building CLI-like state for thread_id: {thread_id}")
    
    # Replicate CLI path construction
    base_config_path = Path("src") / "config" / "setores" / request.sector / request.client_id
    schema_path = base_config_path / f"{request.client_id}_schema_relevance.json"
    kpi_path = Path("src") / "config" / "setores" / request.sector / "kpis-exchange-json.json"
    llm_config_path = base_config_path / "llm.yaml"
    prompt_base_path = base_config_path / "prompts"
    query_gen_prompt = prompt_base_path / "query_generator.txt"
    business_analyst_prompt = prompt_base_path / "business_analyst.txt"
    categorical_values_path = base_config_path / "colunas_categoricas.json"
    
    # Load categorical values like CLI
    categorical_values = {}
    if categorical_values_path.exists():
        try:
            with open(categorical_values_path, "r") as f:
                categorical_values = json.load(f)
            logger.info(f"🔧 [DIAGNOSTIC] Categorical values loaded: {categorical_values_path}")
        except Exception as e:
            logger.warning(f"🔧 [DIAGNOSTIC] Error loading categorical values: {e}")
    
    # Build initial_state exactly like CLI
    initial_state = {
        # Input fields (exact match with CLI)
        "question": request.question,
        "client_id": request.client_id,
        "sector": request.sector,
        "channel": "api-diagnostic",
        
        # Configuration fields (exact match with CLI)
        "schema": {},  # Will be loaded below
        "schema_relevance_path": str(schema_path),
        "sector_kpi_path": str(kpi_path),
        "llm_config_path": str(llm_config_path),
        "prompt_path": str(query_gen_prompt),
        "prompt_path_business_analyst": str(business_analyst_prompt),
        "thread_id": thread_id,
        "categorical_values": categorical_values,
        "categorical_values_path": str(categorical_values_path),
        "messages": [],
        "kpi_context": {},
        "attempts": 0,
        "user_role": None,
        
        # Default processing fields (exact match with CLI)
        "extracted_entities": {},
        "entity_confidence": 0.0,
        "cache_hit": False,
        "cache_source": None,
        "similar_queries": [],
        "sql_query": "",
        "query_generation_attempts": 0,
        "generation_method": "llm",
        "query_valid": False,
        "validation_errors": [],
        "validation_warnings": [],
        "semantic_validation_passed": True,
        
        # Default result fields (exact match with CLI)
        "results": None,
        "result_count": 0,
        "execution_time": 0.0,
        "error": None,
        "error_type": None,
        
        # Default learning fields (exact match with CLI)
        "confidence_score": 0.8,
        "feedback_applied": [],
        "patterns_matched": [],
        "learning_updated": False,
        "cache_size": 0,
        "pattern_count": 0,
        
        # Default presentation fields (exact match with CLI)
        "formatted_response": "",
        "insights": [],
        "suggestions": [],
        "visualization_data": None,
        "business_analysis": None,
    }
    
    # Load schema exactly like CLI
    try:
        from src.utils.component_manager import get_cached_schema
        schema_data = get_cached_schema(str(schema_path))
        if schema_data:
            initial_state["schema"] = schema_data
            logger.info(f"🔧 [DIAGNOSTIC] Schema loaded: {len(schema_data.get('tables', {}))} tables")
        else:
            logger.warning(f"🔧 [DIAGNOSTIC] Error loading schema: {schema_path}")
    except Exception as e:
        logger.error(f"🔧 [DIAGNOSTIC] Schema loading failed: {e}")
    
    logger.info(f"🔧 [DIAGNOSTIC] CLI-like state built with {len(initial_state)} keys")
    return initial_state


def build_initial_state(request: AskRequest) -> Dict[str, Any]:
    """Build initial state for pipeline execution."""
    # Use provided thread_id or generate new one for conversational context
    thread_id = request.thread_id or f"api-{uuid.uuid4()}"

    logger.info(f"🔄 Building initial state for thread_id: {thread_id}")
    logger.info(f"🔄 Request thread_id: {request.thread_id}")

    # Load conversation history if thread_id is provided
    conversation_messages = []
    message_history = []

    if request.thread_id:
        logger.info(f"🔄 Attempting to load conversation history for thread {request.thread_id}")
        try:
            # Try to load conversation history from thread manager
            from .services.thread_manager import ThreadManager
            from .utils.simple_conversation_db import SimpleConversationDB

            # Initialize conversation DB with connection string
            if LEARNING_SYSTEMS_AVAILABLE and db_manager:
                connection_string = db_manager.connection_string
                conversation_db = SimpleConversationDB(connection_string)
                logger.info(f"🔄 SimpleConversationDB initialized for thread loading")
            else:
                # Skip loading if no learning system available
                logger.warning("⚠️ Learning system not available - skipping conversation history load")
                conversation_db = None

            # Load messages from the thread if conversation_db is available
            if conversation_db:
                logger.info(f"🔄 Loading messages for thread {request.thread_id}")
                messages, _ = conversation_db.list_thread_messages(
                    thread_id=request.thread_id,
                    limit=20,  # Last 20 messages for context
                    offset=0
                )
                logger.info(f"🔄 Found {len(messages)} messages in database")
            else:
                messages = []
                logger.info(f"🔄 No conversation_db available, using empty messages")

            # Convert to conversation_messages format for LLM
            for msg in messages:
                conversation_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })

                # Also add to message_history format
                message_history.append({
                    "id": msg.id,
                    "role": msg.role.value,
                    "content": msg.content,
                    "timestamp": msg.created_at.isoformat() if msg.created_at else "",
                    "tokens_used": msg.tokens_used or 0,
                    "processing_time": msg.processing_time or 0.0,
                    "query_id": msg.query_id,
                    "metadata": msg.metadata or {}
                })

            logger.info(f"🔄 Loaded {len(conversation_messages)} messages for thread {request.thread_id}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to load conversation history for thread {request.thread_id}: {e}")
            # Continue with empty history

    return {
        "messages": [],
        "question": request.question,
        "sql_query": None,
        "sql_valid": False,
        "validation_errors": [],
        "query_result": None,
        "business_analysis": None,
        "insights": None,
        "suggestions": [],
        "kpi_context": {},
        "schema": {},
        "attempts": 0,
        "user_role": None,
        "client_id": request.client_id,
        "sector": request.sector,
        "channel": request.channel,
        "session_id": request.session_id,
        "schema_relevance_path": f"src/config/setores/{request.sector}/{request.client_id}/{request.client_id}_schema_relevance.json",
        "sector_kpi_path": f"src/config/setores/{request.sector}/kpis-exchange-json.json",
        "llm_config_path": f"src/config/setores/{request.sector}/{request.client_id}/llm.yaml",
        "prompt_path": f"src/config/setores/{request.sector}/{request.client_id}/prompts/query_generator.txt",
        "prompt_path_business_analyst": f"src/config/setores/{request.sector}/{request.client_id}/prompts/business_analyst.txt",
        "thread_id": thread_id,
        # Add conversation history for conversational correction
        "conversation_messages": conversation_messages,
        "message_history": message_history,
    }


# Main Endpoints
@app.post("/ask", response_model=AskResponse)
async def ask(request: AskRequest):
    """Process a question and return structured response with optimized latency."""
    logger.info(f"🔥 [API] ===== NEW REQUEST RECEIVED =====")
    logger.info(f"🔥 [API] Question: '{request.question}'")
    logger.info(f"🔥 [API] Client: {request.client_id}, Sector: {request.sector}")

    # Start timing for performance monitoring
    start_time = time.time()
    logger.info(f"🔥 [API] Channel: {request.channel}")

    try:
        # Generate query ID
        query_id = f"QRY_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        logger.info(f"🔥 [API] Generated Query ID: {query_id}")

        # Build state
        logger.info(f"🔥 [API] Building initial state...")
        state = build_initial_state(request)
        state["query_id"] = query_id
        # Set default analysis level to direct_answer (Level 1)
        state["analysis_level"] = "direct_answer"
        logger.info(f"🔥 [API] State built with keys: {list(state.keys())}")
        logger.info(f"🔥 [API] Analysis level: {state['analysis_level']}")

        # Execute pipeline
        logger.info(f"🔥 [API] Starting LangGraph pipeline execution...")
        logger.info(f"🔥 [API] Thread ID: {state['thread_id']}")
        pipeline_start = datetime.now()
        
        try:
            logger.info(f"🔥 [API-DEBUG] About to invoke graph...")
            # LangGraph Best Practice: Use sync invoke() with threading isolation
            # Set BG_JOB_ISOLATED_LOOPS=true in environment for FastAPI compatibility
            final_state = get_graph().invoke(state, config={"thread_id": state["thread_id"]})
            execution_time = (datetime.now() - pipeline_start).total_seconds()
            logger.info(f"🔥 [API] Pipeline execution completed in {execution_time:.2f}s!")
        except Exception as pipeline_error:
            logger.error(f"🔥 [API-ERROR] Pipeline execution failed: {pipeline_error}")
            import traceback
            logger.error(f"🔥 [API-ERROR] Traceback: {traceback.format_exc()}")
            # Return a minimal error response
            execution_time = (datetime.now() - pipeline_start).total_seconds()
            final_state = {
                **state,
                "error": f"Pipeline error: {str(pipeline_error)}",
                "sql_query": None,
                "business_analysis": {
                    "direct_answer": "Erro ao processar sua consulta. Por favor, tente novamente.",
                    "analysis_level": "error",
                    "query_id": query_id
                }
            }
        
        logger.info(f"Final state keys: {list(final_state.keys())}")
        logger.info(f"business_analysis in final_state: {final_state.get('business_analysis') is not None}")
        if final_state.get('business_analysis'):
            logger.info(f"business_analysis keys: {list(final_state['business_analysis'].keys())}")

        # Save to query history (async, don't block response)
        if LEARNING_SYSTEMS_AVAILABLE and db_manager:
            try:
                sql_query = final_state.get("sql_query", "")
                results = final_state.get("query_result") or final_state.get("results", [])
                success = bool(sql_query and not final_state.get("error"))
                result_count = len(results) if results else 0
                error_message = final_state.get("error") if not success else None

                db_manager.save_query_history(
                    query_id=query_id,
                    question=request.question,
                    sql_query=sql_query or "No SQL generated",
                    client_id=request.client_id,
                    sector=request.sector,
                    channel=request.channel,
                    execution_time=execution_time,
                    result_count=result_count,
                    success=success,
                    error_message=error_message,
                    user_id=None,  # Could be extracted from session in future
                    session_id=request.session_id,
                    meta_data={
                        "thread_id": state["thread_id"],
                        "has_business_analysis": bool(final_state.get("business_analysis")),
                        "has_visualization": bool(final_state.get("visualization_data")),
                        "tokens_used": final_state.get("tokens_used", 0)
                    }
                )
                logger.info(f"🔥 [API] Query history saved for {query_id}")
            except Exception as e:
                logger.error(f"🔥 [API] Failed to save query history: {e}")
                # Don't fail the request if history saving fails

        # Save conversation messages to database for conversational feedback
        try:
            from src.utils.simple_conversation_db import SimpleConversationDB

            # Get connection string from LearningDBManager
            if LEARNING_SYSTEMS_AVAILABLE and db_manager:
                connection_string = db_manager.connection_string
                conversation_db = SimpleConversationDB(connection_string)

                # Import message models
                from src.models.conversation_models import ConversationMessageCreate, MessageRole, ConversationThreadCreate

                # Ensure thread exists before saving messages
                thread_id = final_state.get("thread_id")
                existing_thread = conversation_db.get_thread(thread_id)

                if not existing_thread:
                    # Create thread if it doesn't exist - using direct SQL to set custom thread_id
                    import psycopg2.extras
                    import json

                    conn = conversation_db.get_connection()
                    conn.autocommit = True
                    cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

                    thread_insert_query = """
                    INSERT INTO conversation_threads (
                        id, user_id, client_id, sector, title, description,
                        is_active, is_archived, context_optimization_enabled,
                        streaming_enabled, metadata, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
                    )
                    """

                    cursor.execute(
                        thread_insert_query,
                        (
                            thread_id,  # Use our API-generated thread_id
                            "api_user",  # Default user for API requests
                            request.client_id,
                            request.sector,
                            f"API Conversation - {request.question[:50]}...",
                            f"Conversation started via API for {request.client_id}",
                            True,  # is_active
                            False,  # is_archived
                            True,  # context_optimization_enabled
                            False,  # streaming_enabled
                            json.dumps({"source": "api", "channel": request.channel})
                        )
                    )

                    cursor.close()
                    conn.close()
                    logger.info(f"📝 Created new thread {thread_id} for conversation")

                # Save user message
                user_message = ConversationMessageCreate(
                    thread_id=final_state.get("thread_id"),
                    role=MessageRole.USER,
                    content=request.question,
                    query_id=query_id,
                    tokens_used=0,  # We don't track tokens for user messages
                    processing_time=0.0,
                    metadata={"client_id": request.client_id, "sector": request.sector}
                )
                conversation_db.create_message(user_message)

                # Save assistant message
                assistant_content = final_state.get("business_analysis", {}).get("direct_answer", "")
                if assistant_content:
                    assistant_message = ConversationMessageCreate(
                        thread_id=final_state.get("thread_id"),
                        role=MessageRole.ASSISTANT,
                        content=assistant_content,
                        query_id=query_id,
                        tokens_used=final_state.get("tokens_used", 0),
                        processing_time=execution_time,
                        metadata={
                            "client_id": request.client_id,
                            "sector": request.sector,
                            "sql_query": final_state.get("sql_query", ""),
                            "cache_hit": final_state.get("cache_hit", False)
                        }
                    )
                    conversation_db.create_message(assistant_message)

                logger.info(f"💬 Conversation messages saved for thread {final_state.get('thread_id')}")
            else:
                logger.warning("⚠️ Learning system not available - skipping conversation save")

        except Exception as e:
            logger.error(f"❌ Failed to save conversation messages: {e}")
            # Don't fail the request if conversation saving fails

        # Build response with progressive disclosure
        response_data = {
            "query_id": query_id,
            "question": request.question,
            "sql_query": final_state.get("sql_query"),
            "results": final_state.get("query_result") or final_state.get("results"),
            "error": final_state.get("error"),
            "thread_id": final_state.get("thread_id"),  # Include thread_id for conversational context
            "metadata": {
                "pipeline": "langgraph",
                "execution_time": final_state.get("execution_time", 0),
                "tokens_used": final_state.get("tokens_used", 0),
                "timestamp": datetime.now().isoformat(),
                "cache_hit": final_state.get("cache_hit", False),
                "ultra_fast_execution": bool(final_state.get("cached_business_analysis") and final_state.get("cached_visualization_data")),
                "cache_confidence": final_state.get("cache_confidence", 0.0),
                "pipeline_path": "ultra_fast" if (final_state.get("cached_business_analysis") and final_state.get("cached_visualization_data")) else "normal"
            }
        }
        
        # Extract business_analysis and implement progressive disclosure
        business_analysis = final_state.get("business_analysis") or None
        if business_analysis:
            # Level 1 (Default): Return only direct answer from progressive disclosure
            direct_answer = business_analysis.get("direct_answer", "Resposta não disponível")
            response_data["direct_answer"] = direct_answer
            response_data["analysis_level"] = "direct_answer"
            logger.info(f"📝 Level 1 response: {direct_answer[:100]}...")
        else:
            # No business analysis available
            response_data["direct_answer"] = "Não foi possível processar sua consulta."
            response_data["analysis_level"] = "error"
            
        # Add visualization_data if available
        if final_state.get("visualization_data"):
            response_data["visualization_data"] = final_state["visualization_data"]

        # Log performance metrics
        total_time = time.time() - start_time
        logger.info(f"🔥 [API] Response prepared successfully!")
        logger.info(f"🔥 [API] SQL Query: {response_data.get('sql_query', 'None')}")
        logger.info(f"🔥 [API] Has Results: {bool(response_data.get('results'))}")
        logger.info(f"⏱️ [PERFORMANCE] Total request time: {total_time:.2f}s")
        logger.info(f"🔥 [API] ===== REQUEST COMPLETED =====")

        return AskResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}", exc_info=True)

        # Save error to query history if possible
        error_query_id = f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        if LEARNING_SYSTEMS_AVAILABLE and db_manager:
            try:
                db_manager.save_query_history(
                    query_id=error_query_id,
                    question=request.question,
                    sql_query="Error occurred before SQL generation",
                    client_id=request.client_id,
                    sector=request.sector,
                    channel=request.channel,
                    execution_time=0.0,
                    result_count=0,
                    success=False,
                    error_message=str(e),
                    user_id=None,
                    session_id=request.session_id,
                    meta_data={
                        "error_type": type(e).__name__,
                        "pipeline_stage": "error_before_completion"
                    }
                )
                logger.info(f"🔥 [API] Error query history saved for {error_query_id}")
            except Exception as history_error:
                logger.error(f"🔥 [API] Failed to save error query history: {history_error}")

        return AskResponse(
            query_id=error_query_id,
            question=request.question,
            sql_query=None,
            results=None,
            direct_answer="Erro ao processar sua consulta.",
            analysis_level="error",
            error=str(e),
            metadata={
                "pipeline": "langgraph",
                "error_type": type(e).__name__,
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/ask/{query_id}/analysis")
async def get_comparative_analysis(query_id: str):
    """
    Level 2: Get comparative analysis for a specific query.

    Returns contextual insights comparing the result to previous periods,
    trends, or benchmarks in a single paragraph.
    """
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")

    try:
        # Get original query data from cache/history
        with db_manager.get_session() as session:
            # First try to get from query_cache
            cache_query = session.execute(text("""
                SELECT question, sql_query, business_analysis, client_id, sector
                FROM query_cache
                WHERE query_id = :query_id OR id::text = :query_id
                LIMIT 1
            """), {"query_id": query_id}).fetchone()

            if not cache_query:
                # Try query_history as fallback
                history_query = session.execute(text("""
                    SELECT question, sql_query, client_id, sector
                    FROM query_history
                    WHERE query_id = :query_id
                    LIMIT 1
                """), {"query_id": query_id}).fetchone()

                if not history_query:
                    raise HTTPException(status_code=404, detail=f"Query {query_id} not found")

                # Re-execute the query to get fresh results
                question, sql_query, client_id, sector = history_query
                business_analysis = None
            else:
                question, sql_query, business_analysis, client_id, sector = cache_query

        # If we have cached business analysis, check if it already has comparative analysis
        if business_analysis and isinstance(business_analysis, dict):
            if business_analysis.get("analysis_level") == "comparative_analysis":
                return {
                    "query_id": query_id,
                    "analysis_level": "comparative_analysis",
                    "comparative_analysis": business_analysis.get("comparative_analysis", ""),
                    "generation_time": business_analysis.get("generation_time", 0)
                }

        # Need to generate comparative analysis
        # Re-execute the pipeline with Level 2 analysis
        request = AskRequest(
            question=question,
            client_id=client_id,
            sector=sector,
            channel="api_analysis",
            session_id=f"analysis_{query_id}"
        )

        state = build_initial_state(request)
        state["query_id"] = f"ANL_{query_id}"
        state["analysis_level"] = "comparative_analysis"  # Level 2
        state["original_query_id"] = query_id

        # Execute pipeline
        final_state = get_graph().invoke(state, config={"thread_id": state["thread_id"]})

        # Extract comparative analysis
        business_analysis = final_state.get("business_analysis", {})
        comparative_analysis = business_analysis.get("comparative_analysis", "Análise comparativa não disponível")

        return {
            "query_id": query_id,
            "analysis_level": "comparative_analysis",
            "comparative_analysis": comparative_analysis,
            "generation_time": business_analysis.get("generation_time", 0),
            "data_confidence": business_analysis.get("data_confidence", 0.8)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating comparative analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating analysis: {str(e)}")


@app.post("/ask/{query_id}/report")
async def get_comprehensive_report(query_id: str):
    """
    Level 3: Get comprehensive business intelligence report for a specific query.

    Returns detailed analysis with KPIs, strategic recommendations,
    risk factors, and comprehensive business insights.
    """
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")

    try:
        # Get original query data from cache/history
        with db_manager.get_session() as session:
            # First try to get from query_cache
            cache_query = session.execute(text("""
                SELECT question, sql_query, business_analysis, client_id, sector
                FROM query_cache
                WHERE query_id = :query_id OR id::text = :query_id
                LIMIT 1
            """), {"query_id": query_id}).fetchone()

            if not cache_query:
                # Try query_history as fallback
                history_query = session.execute(text("""
                    SELECT question, sql_query, client_id, sector
                    FROM query_history
                    WHERE query_id = :query_id
                    LIMIT 1
                """), {"query_id": query_id}).fetchone()

                if not history_query:
                    raise HTTPException(status_code=404, detail=f"Query {query_id} not found")

                # Re-execute the query to get fresh results
                question, sql_query, client_id, sector = history_query
                business_analysis = None
            else:
                question, sql_query, business_analysis, client_id, sector = cache_query

        # If we have cached comprehensive report, return it
        if business_analysis and isinstance(business_analysis, dict):
            if business_analysis.get("analysis_level") == "comprehensive_report":
                return {
                    "query_id": query_id,
                    "analysis_level": "comprehensive_report",
                    "business_analysis": business_analysis,
                    "generation_time": business_analysis.get("generation_time", 0)
                }

        # Need to generate comprehensive report
        # Re-execute the pipeline with Level 3 analysis
        request = AskRequest(
            question=question,
            client_id=client_id,
            sector=sector,
            channel="api_report",
            session_id=f"report_{query_id}"
        )

        state = build_initial_state(request)
        state["query_id"] = f"RPT_{query_id}"
        state["analysis_level"] = "comprehensive_report"  # Level 3
        state["original_query_id"] = query_id

        # Execute pipeline
        final_state = get_graph().invoke(state, config={"thread_id": state["thread_id"]})

        # Extract comprehensive report
        business_analysis = final_state.get("business_analysis", {})

        return {
            "query_id": query_id,
            "analysis_level": "comprehensive_report",
            "business_analysis": business_analysis,
            "visualization_data": final_state.get("visualization_data"),
            "generation_time": business_analysis.get("generation_time", 0),
            "data_confidence": business_analysis.get("data_confidence", 0.9)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating comprehensive report: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")


@app.post("/feedback/simple", response_model=FeedbackResponse)
async def submit_simple_feedback(request: SimpleFeedbackRequest):
    """Submit simple positive/negative feedback."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Save to PostgreSQL using ORM
        feedback_id = None
        with db_manager.get_session() as session:
            explanation = ""
            if request.feedback_type == "negative":
                explanation = f"Categoria: {request.category or 'Não especificada'}"
                if request.comment:
                    explanation += f". Comentário: {request.comment}"
            elif request.feedback_type == "positive":
                explanation = "Feedback positivo - resposta satisfatória"
                
            pg_feedback = PGFeedbackCorrection(
                question=request.original_question,
                original_query="",
                corrected_query="",
                explanation=explanation,
                feedback_type=request.feedback_type,  # Novo campo
                category=request.category,            # Novo campo
                user_comment=request.comment,         # Novo campo
                client_id=request.client_id,
                sector=request.sector,
                user_id=request.user_id or "anonymous",
                patterns_extracted=[],
                meta_data={
                    "feedback_type": request.feedback_type,
                    "query_id": request.query_id,
                    "category": request.category or "",
                    "comment": request.comment or "",
                    "client_id": request.client_id,
                    "sector": request.sector,
                    "user_id": request.user_id or "anonymous"
                }
            )
            session.add(pg_feedback)
            session.flush()
            
            # Update related cache query feedback metrics if we can find it
            from src.models.learning_models import QueryCache
            cache_query = session.execute(text("""
                SELECT id FROM query_cache 
                WHERE question ILIKE :question_pattern
                ORDER BY confidence DESC, use_count DESC
                LIMIT 1
            """), {"question_pattern": f"%{request.original_question[:50]}%"}).fetchone()
            
            if cache_query:
                cache_id = cache_query[0]
                # Link feedback to cache
                pg_feedback.query_cache_id = cache_id
                
                # Update feedback metrics in cache
                if request.feedback_type == "positive":
                    session.execute(text("""
                        UPDATE query_cache 
                        SET positive_feedback_count = COALESCE(positive_feedback_count, 0) + 1,
                            last_feedback_date = CURRENT_TIMESTAMP,
                            feedback_score = CASE 
                                WHEN feedback_score IS NULL THEN 0.2
                                ELSE LEAST(1.0, feedback_score + 0.2)
                            END
                        WHERE id = :cache_id
                    """), {"cache_id": cache_id})
                elif request.feedback_type == "negative":
                    # CRITICAL: Invalidate cache entry when negative feedback is received
                    logger.info(f"🗑️ NEGATIVE FEEDBACK: Invalidating cache entry {cache_id}")

                    # First, mark as invalid by setting very low feedback score
                    session.execute(text("""
                        UPDATE query_cache
                        SET negative_feedback_count = COALESCE(negative_feedback_count, 0) + 1,
                            last_feedback_date = CURRENT_TIMESTAMP,
                            feedback_score = -1.0,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = :cache_id
                    """), {"cache_id": cache_id})

                    # Also mark similar queries with low scores to prevent reuse
                    session.execute(text("""
                        UPDATE query_cache
                        SET feedback_score = CASE
                                WHEN feedback_score IS NULL THEN -0.8
                                ELSE GREATEST(-1.0, feedback_score - 0.8)
                            END,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE question ILIKE :question_pattern
                          AND id != :cache_id
                          AND COALESCE(feedback_score, 0) > -0.5
                    """), {
                        "question_pattern": f"%{request.original_question[:30]}%",
                        "cache_id": cache_id
                    })

                    logger.info(f"✅ Cache invalidated due to negative feedback (score set to -1.0)")
                    
                logger.info(f"✅ Updated cache feedback metrics for query {cache_id}")
            
            session.commit()
            feedback_id = str(pg_feedback.id)
        
        logger.info(f"✅ Simple feedback saved: {feedback_id} ({request.feedback_type})")
        
        # Update learning patterns based on feedback type
        patterns_found = []
        if request.feedback_type == "positive":
            patterns_found.append("positive_confirmation")
        elif request.feedback_type == "negative":
            if request.category:
                patterns_found.append(f"negative_{request.category}")
        
        return FeedbackResponse(
            feedback_id=feedback_id,
            status="accepted",
            message=f"Feedback {request.feedback_type} registrado com sucesso",
            patterns_found=patterns_found,
            learning_updated=True
        )
        
    except Exception as e:
        logger.error(f"Error processing simple feedback: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")


@app.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(request: FeedbackRequest):
    """Submit feedback to improve the system."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Create feedback model
        feedback = create_feedback_from_natural_language(
            feedback_text=request.feedback_text,
            query_id=request.query_id,
            original_question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            source=FeedbackSource.API,
            user_id=request.user_id
        )
        
        if request.corrected_sql:
            feedback.corrected_sql = request.corrected_sql
        
        # Save to PostgreSQL
        feedback_id = None
        with db_manager.get_session() as session:
            pg_feedback = PGFeedbackCorrection(
                question=feedback.original_question,
                original_query=feedback.original_sql or "",
                corrected_query=feedback.corrected_sql or "",
                explanation=feedback.feedback_text,
                patterns_extracted=[],
                meta_data={
                    "feedback_type": feedback.feedback_type.value,
                    "source": feedback.source.value,
                    "client_id": feedback.client_id,
                    "sector": feedback.sector,
                    "user_id": feedback.user_id,
                    "query_id": feedback.query_id
                }
            )
            session.add(pg_feedback)
            session.flush()
            feedback_id = str(pg_feedback.id)
        
        logger.info(f"✅ Feedback saved: {feedback_id}")
        
        # Extract patterns
        patterns_found = []
        feedback_lower = request.feedback_text.lower()
        if "should use" in feedback_lower or "deveria usar" in feedback_lower:
            patterns_found.append("column_preference")
        if "filter" in feedback_lower or "filtro" in feedback_lower:
            patterns_found.append("filtering_logic")
        
        return FeedbackResponse(
            feedback_id=feedback_id,
            status="accepted",
            message="Feedback received and will improve future queries",
            patterns_found=patterns_found,
            learning_applied=True,
            cache_updated=bool(request.corrected_sql)
        )
        
    except Exception as e:
        logger.error(f"Error processing feedback: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")


@app.post("/feedback/reprocess", response_model=AskResponse)
async def feedback_and_reprocess(request: FeedbackReprocessRequest):
    """Submit feedback and immediately reprocess with intelligent corrections."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # 1. Submit feedback first (save to database and update cache metrics)
        logger.info(f"📝 Processing feedback for reprocessing: {request.feedback.feedback_type}")
        feedback_response = await submit_simple_feedback(request.feedback)
        
        if not request.reprocess:
            # Just return feedback response if no reprocessing requested
            return AskResponse(
                query_id=request.feedback.query_id,
                question=request.feedback.original_question,
                error=None,
                metadata={"feedback_only": True, "feedback_id": feedback_response.feedback_id}
            )
        
        # 2. Build feedback context for intelligent reprocessing
        feedback_context = None
        with db_manager.get_session() as session:
            # Get the feedback we just saved
            query = text("""
                SELECT question, original_query, corrected_query, explanation, meta_data
                FROM feedback_corrections 
                WHERE id = :feedback_id
            """)
            result = session.execute(query, {"feedback_id": feedback_response.feedback_id}).fetchone()
            
            if result:
                feedback_context = {
                    "question": result[0],
                    "original_query": result[1] or "",
                    "corrected_query": result[2] or "",
                    "explanation": result[3] or "",
                    "metadata": result[4] or {}
                }
                logger.info(f"✅ Feedback context prepared for reprocessing")
        
        # 3. Build state for reprocessing with feedback context
        reprocess_request = AskRequest(
            question=request.feedback.original_question,
            client_id=request.feedback.client_id,
            sector=request.feedback.sector,
            channel="api_reprocess",
            session_id=f"reprocess_{request.feedback.query_id}"
        )
        
        state = build_initial_state(reprocess_request)
        state["query_id"] = f"RPR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        state["original_query_id"] = request.feedback.query_id
        state["feedback_context"] = feedback_context  # Key: inject feedback for analysis
        state["reprocessing_mode"] = True
        state["feedback_id"] = feedback_response.feedback_id

        # Debug: Log state construction
        logger.info(f"🔍 DEBUG - State constructed with feedback_context: {bool(feedback_context)}")
        logger.info(f"🔍 DEBUG - State constructed with reprocessing_mode: {state.get('reprocessing_mode')}")
        logger.info(f"🔍 DEBUG - State keys before pipeline: {list(state.keys())}")

        # 4. Execute pipeline with feedback-aware improvements
        logger.info(f"🔄 Reprocessing with feedback context: {request.feedback.feedback_type}")
        final_state = get_graph().invoke(state, config={"thread_id": state["thread_id"]})
        
        # 5. Build enhanced response with validation fields
        response_data = {
            "query_id": state["query_id"],
            "question": request.feedback.original_question,
            "sql_query": final_state.get("sql_query"),
            "results": final_state.get("query_result") or final_state.get("results"),
            "insights": final_state.get("insights", []),
            "suggestions": final_state.get("suggestions", []),
            "formatted_response": final_state.get("formatted_response") or (
                final_state.get("messages", [""])[-1] if final_state.get("messages") else ""
            ),
            "error": final_state.get("error"),

            # CRITICAL FIX: Include validation fields from final_state
            "sql_valid": final_state.get("sql_valid", False),
            "query_valid": final_state.get("query_valid", False),
            "validation_errors": final_state.get("validation_errors", []),
            "validation_warnings": final_state.get("validation_warnings", []),
            "validation_confidence": final_state.get("validation_confidence", 0.0),
            "validation_type": final_state.get("validation_type", "unknown"),
            "validation_suggestions": final_state.get("validation_suggestions", []),
            "attempts": final_state.get("attempts", 0),

            "metadata": {
                "pipeline": "langgraph_reprocess",
                "original_query_id": request.feedback.query_id,
                "feedback_id": feedback_response.feedback_id,
                "feedback_type": request.feedback.feedback_type,
                "feedback_category": request.feedback.category,
                "reprocessing_applied": True,
                "correction_insights": final_state.get("correction_insights", {}),
                "feedback_analyzed": final_state.get("feedback_analyzed", False),
                "execution_time": final_state.get("execution_time", 0),
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Add business analysis and visualization data if available
        business_analysis = final_state.get("business_analysis")
        if business_analysis:
            response_data["business_analysis"] = business_analysis
            if business_analysis.get("frontend_display"):
                response_data["frontend_display"] = business_analysis["frontend_display"]
            response_data["insights"] = business_analysis.get("key_findings", [])
            response_data["suggestions"] = business_analysis.get("next_actions", [])
        
        if final_state.get("visualization_data"):
            response_data["visualization_data"] = final_state["visualization_data"]
        
        logger.info(f"✅ Reprocessing completed successfully with feedback improvements")
        return AskResponse(**response_data)
        
    except Exception as e:
        logger.error(f"❌ Error in feedback reprocessing: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error in feedback reprocessing: {str(e)}")


@app.post("/reprocess")
async def reprocess_with_feedback(request: ReprocessRequest):
    """Reprocess a question with learned corrections."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        raise HTTPException(status_code=503, detail="Learning system not available")
    
    try:
        # Get recent feedback if requested
        feedback_context = None
        if request.apply_recent_feedback:
            with db_manager.get_session() as session:
                query = text("""
                    SELECT question, original_query, corrected_query, explanation, meta_data
                    FROM feedback_corrections 
                    WHERE question ILIKE :question
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                result = session.execute(query, {"question": f"%{request.original_question[:50]}%"}).fetchone()
                
                if result:
                    feedback_context = {
                        "question": result[0],
                        "original_query": result[1],
                        "corrected_query": result[2],
                        "explanation": result[3],
                        "metadata": result[4]
                    }
        
        # Build state with feedback context
        state = build_initial_state(AskRequest(
            question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            channel="api"
        ))
        state["feedback_context"] = feedback_context
        state["query_id"] = f"RPR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # Execute pipeline
        final_state = get_graph().invoke(state, config={"thread_id": state["thread_id"]})
        
        # Return same format as /ask
        return await ask(AskRequest(
            question=request.original_question,
            client_id=request.client_id,
            sector=request.sector,
            channel="api"
        ))
        
    except Exception as e:
        logger.error(f"Error in reprocessing: {e}")
        raise HTTPException(status_code=500, detail=f"Error reprocessing: {str(e)}")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """System health check with mandatory Nível 2 verification."""
    # Check PostgreSQL
    pg_status = "unavailable"
    if LEARNING_SYSTEMS_AVAILABLE and db_manager:
        try:
            health = db_manager.health_check()
            pg_status = health["status"]
        except:
            pg_status = "error"
    
    # 🚀 VERIFICAÇÃO OBRIGATÓRIA: Nível 2 para performance adequada
    from pathlib import Path
    nivel2_status = "disabled"
    nivel2_path = Path("src/config/setores/cambio/L2M/nivel2")
    nivel2_required_files = [
        nivel2_path / "patterns.json",
        nivel2_path / "domain_config.json", 
        nivel2_path / "column_aliases.json"
    ]
    
    if nivel2_path.exists() and all(f.exists() for f in nivel2_required_files):
        nivel2_status = "active"
    
    # Sistema deve estar degraded se Nível 2 não estiver ativo
    overall_status = "healthy"
    if pg_status != "healthy":
        overall_status = "degraded"
    elif nivel2_status != "active":
        overall_status = "degraded"
    
    return HealthResponse(
        status=overall_status,
        database={
            "postgresql": pg_status,
            "learning_available": LEARNING_SYSTEMS_AVAILABLE
        },
        systems={
            "pipeline": "operational",
            "learning": "operational" if LEARNING_SYSTEMS_AVAILABLE else "limited",
            "optimization_status": nivel2_status,
            "workflow_type": "optimized_parallel",
            "level2_enabled": nivel2_status == "active"
        },
        feature_flags={
            "learning_enabled": LEARNING_SYSTEMS_AVAILABLE,
            "feedback_enabled": True,
            "frontend_display": True,
            "nivel2_required": True
        }
    )


@app.get("/metrics")
async def get_metrics():
    """Get comprehensive system metrics including cache performance."""
    if not LEARNING_SYSTEMS_AVAILABLE:
        return {"error": "Metrics unavailable - learning system not initialized"}
    
    try:
        # Get feedback statistics
        stats = db_manager.get_feedback_statistics()
        
        # Get enhanced cache metrics
        from src.utils.enhanced_metrics import get_metrics_collector
        metrics_collector = get_metrics_collector()
        cache_performance = metrics_collector.get_cache_performance_stats()
        
        # Get component stats if available
        component_stats = {}
        try:
            from src.optimization.lazy_loader import component_manager
            component_stats = component_manager.get_component_stats()
        except Exception as e:
            logger.warning(f"Could not get component stats: {e}")
        
        return {
            "feedback": {
                "total": stats.get("feedback_corrections", {}).get("total", 0),
                "recent_7_days": stats.get("feedback_corrections", {}).get("recent_7_days", 0)
            },
            "patterns": {
                "total": stats.get("correction_patterns", {}).get("total", 0),
                "by_type": stats.get("correction_patterns", {}).get("by_type", [])
            },
            "cache_legacy": {
                "total_queries": stats.get("query_cache", {}).get("total", 0)
            },
            "cache_performance": cache_performance,
            "components": component_stats,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving metrics")


@app.get("/metrics/cache")
async def get_cache_metrics():
    """Get detailed cache performance metrics only."""
    try:
        from src.utils.enhanced_metrics import get_metrics_collector
        metrics_collector = get_metrics_collector()
        cache_stats = metrics_collector.get_cache_performance_stats()
        
        return cache_stats
    except Exception as e:
        logger.error(f"Error getting cache metrics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving cache metrics")


@app.get("/ping")
async def ping():
    """🔧 ULTRA-BASIC: Test if FastAPI itself works without LangGraph."""
    return {
        "status": "pong",
        "timestamp": datetime.now().isoformat(),
        "message": "FastAPI is working - no LangGraph involved"
    }


@app.post("/test-no-graph", response_model=AskResponse)
async def test_no_graph(request: AskRequest):
    """🔧 CRITICAL TEST: Test pipeline without LangGraph to isolate problem."""
    logger.info(f"🧪 [NO-GRAPH-TEST] Testing without LangGraph: '{request.question}'")
    
    start_time = time.time()
    
    try:
        # Build state like normal
        state = build_cli_like_state(request)
        state["query_id"] = f"NOGRAPH_{int(time.time())}"
        
        # SIMULATE what the pipeline would do, but WITHOUT calling graph.invoke()
        logger.info(f"🧪 [NO-GRAPH-TEST] Simulating pipeline response...")
        
        # Simulate a successful response without actually running LangGraph
        simulated_response = {
            "query_id": state["query_id"],
            "question": request.question,
            "sql_query": "SELECT 'This is a simulated response' as message",
            "results": [{"message": "This is a simulated response without LangGraph"}],
            "direct_answer": "Esta é uma resposta simulada para testar se o problema está no LangGraph.",
            "analysis_level": "simulated",
            "error": None,
            "thread_id": state["thread_id"],
            "metadata": {
                "pipeline": "no-langgraph-simulation",
                "execution_time": time.time() - start_time,
                "timestamp": datetime.now().isoformat(),
                "simulation_mode": True,
                "graph_bypassed": True
            }
        }
        
        logger.info(f"🧪 [NO-GRAPH-TEST] Completed in {time.time() - start_time:.3f}s - LangGraph bypassed!")
        return AskResponse(**simulated_response)
        
    except Exception as e:
        logger.error(f"🧪 [NO-GRAPH-TEST] Error: {str(e)}", exc_info=True)
        return AskResponse(
            query_id=f"NOGRAPH_ERR_{int(time.time())}",
            question=request.question,
            direct_answer="Erro no teste sem LangGraph.",
            analysis_level="error",
            error=str(e),
            metadata={
                "pipeline": "no-langgraph-error",
                "error_type": type(e).__name__,
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/setup-chat-tables")
async def setup_chat_tables():
    """
    Endpoint para criar tabelas de chat.
    """
    try:
        # Obter connection string do ambiente Railway
        railway_db_url = os.getenv('DATABASE_URL_LEARNING')
        
        if not railway_db_url:
            raise HTTPException(
                status_code=503,
                detail="DATABASE_URL_LEARNING not available"
            )
        
        # Parse da URL
        from urllib.parse import urlparse
        parsed = urlparse(railway_db_url)
        
        db_config = {
            'host': parsed.hostname,
            'port': parsed.port,
            'database': parsed.path[1:],  # Remove leading slash
            'user': parsed.username,
            'password': parsed.password
        }
        
        # Conectar ao banco
        import psycopg2
        
        # Usar hostname público se necessário
        if db_config['host'] == 'postgres.railway.internal':
            db_config['host'] = 'viaduct.proxy.rlwy.net'
            db_config['port'] = 40697  # Porta pública comum do Railway
        
        conn = psycopg2.connect(**db_config)
        conn.autocommit = True
        cur = conn.cursor()
        
        # SQL para criar tabelas
        sql = """
        -- Criar tabelas para o sistema de chat avançado
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );

        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tokens_used INTEGER DEFAULT 0,
            metadata JSONB DEFAULT '{}'
        );

        -- Índices para performance
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_client 
            ON conversation_threads(user_id, client_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_active 
            ON conversation_threads(is_active);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_created_at 
            ON conversation_threads(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_id 
            ON conversation_messages(thread_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at 
            ON conversation_messages(created_at DESC);
        """
        
        cur.execute(sql)
        
        # Verificar tabelas criadas
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        threads_count = result[0] if result else 0
        
        cur.execute("SELECT COUNT(*) FROM conversation_messages;")
        result = cur.fetchone()
        messages_count = result[0] if result else 0
        
        conn.close()
        
        return {
            "status": "success",
            "message": "Chat tables created successfully",
            "tables": {
                "conversation_threads": threads_count,
                "conversation_messages": messages_count
            }
        }
        
    except Exception as e:
        logger.error(f"Error creating chat tables: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating chat tables: {str(e)}"
        )


@app.get("/create-chat-tables")
async def create_chat_tables():
    """
    Criar tabelas de chat usando o LearningDBManager.
    """
    try:
        # Verificar se o sistema está disponível
        if not LEARNING_SYSTEMS_AVAILABLE or not db_manager:
            raise HTTPException(
                status_code=503,
                detail="Learning database not available"
            )
        
        # Usar o db_manager para criar as tabelas
        with db_manager.get_session() as session:
            # SQL para criar tabelas
            sql_commands = [
                """
                CREATE TABLE IF NOT EXISTS conversation_threads (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL,
                    client_id VARCHAR(255) NOT NULL,
                    sector VARCHAR(255) NOT NULL,
                    title VARCHAR(255),
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_message_at TIMESTAMP,
                    message_count INTEGER DEFAULT 0,
                    total_tokens_used INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT true,
                    metadata JSONB DEFAULT '{}'
                );
                """,
                """
                CREATE TABLE IF NOT EXISTS conversation_messages (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
                    role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
                    content TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    tokens_used INTEGER DEFAULT 0,
                    metadata JSONB DEFAULT '{}'
                );
                """
            ]
            
            # Executar comandos SQL
            for sql in sql_commands:
                session.execute(text(sql))
            
            # Verificar se as tabelas foram criadas
            threads_count = session.execute(text("SELECT COUNT(*) FROM conversation_threads")).scalar()
            messages_count = session.execute(text("SELECT COUNT(*) FROM conversation_messages")).scalar()
            
            return {
                "status": "success",
                "message": "Chat tables created successfully",
                "database": db_manager.connection_string[:50] + "...",
                "tables": {
                    "conversation_threads": threads_count,
                    "conversation_messages": messages_count
                }
            }
    
    except Exception as e:
        logger.error(f"Error creating chat tables: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating chat tables: {str(e)}"
        )


@app.post("/debug-pipeline")
async def debug_pipeline(request: AskRequest):
    """Debug endpoint to investigate pipeline flow."""
    logger.info(f"🔥 [DEBUG] ===== DEBUG PIPELINE REQUEST =====")
    logger.info(f"🔥 [DEBUG] Question: '{request.question}'")
    logger.info(f"🔥 [DEBUG] Client: {request.client_id}, Sector: {request.sector}")
    
    try:
        # Build state
        logger.info(f"🔥 [DEBUG] Building initial state...")
        state = build_initial_state(request)
        query_id = f"DEBUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        state["query_id"] = query_id
        state["analysis_level"] = "direct_answer"
        
        logger.info(f"🔥 [DEBUG] State built with keys: {list(state.keys())}")
        
        # Check LLM configuration
        try:
            llm_config_path = state.get("llm_config_path")
            logger.info(f"🔥 [DEBUG] LLM config path: {llm_config_path}")
            
            if llm_config_path and os.path.exists(llm_config_path):
                with open(llm_config_path, 'r') as f:
                    import yaml
                    llm_config = yaml.safe_load(f)
                    logger.info(f"🔥 [DEBUG] LLM config loaded: {list(llm_config.keys())}")
                    query_gen_config = llm_config.get('query_generator_agent', {})
                    logger.info(f"🔥 [DEBUG] Query generator provider: {query_gen_config.get('provider')}")
                    logger.info(f"🔥 [DEBUG] Query generator model: {query_gen_config.get('model')}")
            else:
                logger.error(f"🔥 [DEBUG] LLM config file not found: {llm_config_path}")
        except Exception as e:
            logger.error(f"🔥 [DEBUG] LLM config load error: {e}")
        
        # Check environment variables
        logger.info(f"🔥 [DEBUG] Environment vars:")
        logger.info(f"🔥 [DEBUG] - DATAHERO_LLM_PROVIDER: {os.getenv('DATAHERO_LLM_PROVIDER')}")
        logger.info(f"🔥 [DEBUG] - FIREWORKS_API_KEY: {bool(os.getenv('FIREWORKS_API_KEY'))}")
        logger.info(f"🔥 [DEBUG] - GROQ_API_KEY: {bool(os.getenv('GROQ_API_KEY'))}")
        logger.info(f"🔥 [DEBUG] - ENVIRONMENT: {os.getenv('ENVIRONMENT')}")
        
        return {
            "debug": "Pipeline debug completed",
            "query_id": query_id,
            "state_keys": list(state.keys()),
            "llm_config_path": state.get("llm_config_path"),
            "environment": os.getenv('ENVIRONMENT'),
            "datahero_llm_provider": os.getenv('DATAHERO_LLM_PROVIDER')
        }
        
    except Exception as e:
        logger.error(f"🔥 [DEBUG] Debug endpoint error: {e}")
        import traceback
        logger.error(f"🔥 [DEBUG] Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Debug error: {e}")


@app.post("/test-llm-direct")
async def test_llm_direct(request: AskRequest):
    """Test LLM directly without LangGraph to isolate issues."""
    logger.info(f"🔥 [TEST-LLM] Direct LLM test for: {request.question}")
    
    try:
        # Import LLM provider
        from src.tools.llm_provider import LLMProvider
        
        # Create provider instance
        provider = LLMProvider(
            setor=request.sector,
            cliente=request.client_id,
            agent_name="query_generator_agent"
        )
        
        logger.info(f"🔥 [TEST-LLM] Provider: {provider.provider}, Model: {provider.model}")
        
        # Simple prompt
        prompt = f"""Generate SQL for: "{request.question}"
        
        Available tables: vendas, cambio, clientes
        
        Return only the SQL query."""
        
        # Call LLM
        logger.info(f"🔥 [TEST-LLM] Calling LLM...")
        response = await provider.ainvoke(prompt)
        
        logger.info(f"🔥 [TEST-LLM] LLM Response: {response[:200]}...")
        
        return {
            "test": "llm_direct",
            "question": request.question,
            "provider": provider.provider,
            "model": provider.model,
            "response": response,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"🔥 [TEST-LLM] Error: {e}")
        import traceback
        logger.error(f"🔥 [TEST-LLM] Traceback: {traceback.format_exc()}")
        
        return {
            "test": "llm_direct",
            "question": request.question,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "success": False
        }


@app.get("/")
async def root():
    """API information."""
    return {
        "name": "DataHero API",
        "version": "4.0.0",
        "description": "Unified DataHero API with LangGraph pipeline",
        "endpoints": {
            "/ask": "Process questions",
            "/ask-simple": "Diagnostic endpoint (CLI-like, no conversation history)",
            "/debug-pipeline": "🔧 DEBUG: Detailed pipeline investigation",
            "/test-llm-direct": "🔧 TEST: Direct LLM call without LangGraph",
            "/ping": "🔧 ULTRA-BASIC: Test FastAPI without any pipeline",
            "/feedback": "Submit feedback",
            "/feedback/simple": "Submit simple positive/negative feedback",
            "/feedback/reprocess": "Submit feedback and reprocess immediately",
            "/reprocess": "Reprocess with corrections",
            "/health": "System health",
            "/metrics": "System metrics",
            "/docs": "API documentation"
        }
    }


@app.post("/ask-simple", response_model=AskResponse)
async def ask_simple(request: AskRequest):
    """🔧 DIAGNOSTIC: Process question using CLI-like logic (no conversation history)."""
    logger.info(f"🔧 [DIAGNOSTIC] ===== SIMPLE REQUEST RECEIVED =====")
    logger.info(f"🔧 [DIAGNOSTIC] Question: '{request.question}'")
    logger.info(f"🔧 [DIAGNOSTIC] Client: {request.client_id}, Sector: {request.sector}")

    # Start timing
    start_time = time.time()
    
    try:
        # Generate query ID
        query_id = f"DIAG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        logger.info(f"🔧 [DIAGNOSTIC] Generated Query ID: {query_id}")

        # Build CLI-like state (no conversation history, minimal complexity)
        logger.info(f"🔧 [DIAGNOSTIC] Building CLI-like state...")
        build_start = time.time()
        state = build_cli_like_state(request)
        state["query_id"] = query_id
        build_time = time.time() - build_start
        logger.info(f"🔧 [DIAGNOSTIC] State built in {build_time:.3f}s with keys: {list(state.keys())}")

        # Execute pipeline exactly like CLI
        logger.info(f"🔧 [DIAGNOSTIC] Starting LangGraph pipeline execution...")
        logger.info(f"🔧 [DIAGNOSTIC] Thread ID: {state['thread_id']}")
        pipeline_start = time.time()
        
        # Use same config as CLI: {"configurable": {"thread_id": thread_id}}
        config = {"configurable": {"thread_id": state["thread_id"]}}
        final_state = get_graph().invoke(state, config=config)
        
        pipeline_time = time.time() - pipeline_start
        logger.info(f"🔧 [DIAGNOSTIC] Pipeline completed in {pipeline_time:.2f}s!")
        
        # Build minimal response (no complex database operations)
        response_data = {
            "query_id": query_id,
            "question": request.question,
            "sql_query": final_state.get("sql_query"),
            "results": final_state.get("query_result") or final_state.get("results"),
            "error": final_state.get("error"),
            "thread_id": final_state.get("thread_id"),
            "metadata": {
                "pipeline": "langgraph-diagnostic",
                "execution_time": pipeline_time,
                "build_time": build_time,
                "total_time": time.time() - start_time,
                "timestamp": datetime.now().isoformat(),
                "cache_hit": final_state.get("cache_hit", False),
                "diagnostic_mode": True
            }
        }
        
        # Extract business analysis
        business_analysis = final_state.get("business_analysis")
        if business_analysis:
            direct_answer = business_analysis.get("direct_answer", "Resposta não disponível")
            response_data["direct_answer"] = direct_answer
            response_data["analysis_level"] = "direct_answer"
            logger.info(f"🔧 [DIAGNOSTIC] Business analysis extracted: {direct_answer[:100]}...")
        else:
            response_data["direct_answer"] = "Não foi possível processar sua consulta."
            response_data["analysis_level"] = "error"

        # Add visualization data if available
        if final_state.get("visualization_data"):
            response_data["visualization_data"] = final_state["visualization_data"]

        total_time = time.time() - start_time
        logger.info(f"🔧 [DIAGNOSTIC] ===== DIAGNOSTIC COMPLETED in {total_time:.2f}s =====")
        logger.info(f"🔧 [DIAGNOSTIC] Build: {build_time:.3f}s, Pipeline: {pipeline_time:.2f}s")

        return AskResponse(**response_data)
        
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"🔧 [DIAGNOSTIC] Error after {error_time:.2f}s: {str(e)}", exc_info=True)

        error_query_id = f"DIAG_ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        return AskResponse(
            query_id=error_query_id,
            question=request.question,
            sql_query=None,
            results=None,
            direct_answer="Erro no diagnóstico da consulta.",
            analysis_level="error",
            error=str(e),
            metadata={
                "pipeline": "langgraph-diagnostic",
                "error_type": type(e).__name__,
                "error_time": error_time,
                "timestamp": datetime.now().isoformat(),
                "diagnostic_mode": True
            }
        )


# Mount static files at the very end, after all API routes are defined
# This ensures API routes take precedence over static file serving
static_dir = "/app/static"
if os.path.exists(static_dir):
    # Mount static files at /static prefix to avoid conflicts
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # Serve index.html for the root and any non-API routes (SPA support)
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        # Skip API routes - they should 404 if not found
        if full_path.startswith(("ask", "feedback", "health", "metrics", "reprocess", "v1/chat")):
            raise HTTPException(status_code=404, detail="API endpoint not found")
        
        # For the root path or any other path, serve index.html
        index_path = os.path.join(static_dir, "index.html")
        if os.path.exists(index_path):
            return FileResponse(index_path)
        
        # If index.html doesn't exist, return 404
        raise HTTPException(status_code=404, detail="Frontend not found")
    
    logger.info(f"✅ Frontend static files mounted from {static_dir}")
else:
    logger.info("⚠️ Frontend static files not found, serving API only")


# Profile Detection Endpoint
@app.post("/api/profiles/detect")
async def detect_profile(request: Dict[str, Any]):
    """Detect user profile based on usage patterns."""
    try:
        user_id = request.get("userId", "test_user_ceo")
        logger.info(f"Detecting profile for user: {user_id}")

        # Simple profile detection logic based on user_id
        # In production, this would analyze user behavior patterns
        if "ceo" in user_id.lower():
            detected_profile = "CEO"
            confidence = 0.85
        elif "cfo" in user_id.lower():
            detected_profile = "CFO"
            confidence = 0.80
        elif "trader" in user_id.lower():
            detected_profile = "Trader"
            confidence = 0.90
        elif "risk" in user_id.lower():
            detected_profile = "Risk Manager"
            confidence = 0.85
        elif "ops" in user_id.lower() or "operations" in user_id.lower():
            detected_profile = "Operations"
            confidence = 0.75
        else:
            # Default to CEO for unknown users
            detected_profile = "CEO"
            confidence = 0.60

        return {
            "success": True,
            "detectedProfile": detected_profile,
            "confidence": confidence,
            "message": f"Profile detected with {confidence*100:.0f}% confidence"
        }

    except Exception as e:
        logger.error(f"Error detecting profile: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Profile detection failed: {str(e)}")
