"""
Profile API for DataHero4 Week 4 Implementation
===============================================

FastAPI endpoints for profile management and detection:
- /detect: Profile detection based on usage patterns
- /configure: Profile configuration and preferences
- /kpis: Profile-specific KPI recommendations
- /bcb: BCB market data with profile-aware caching

Features:
- Profile detection via ProfileDetector
- SmartQueryRouter integration
- BCB API integration with fail-fast
- Profile-aware caching strategies

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.profile_detector import get_profile_detector
from src.services.smart_query_router import get_smart_query_router
from src.services.banco_central_api import get_banco_central_api

logger = logging.getLogger(__name__)

# Create router
profile_router = APIRouter(prefix="/api/profiles", tags=["Profile Management"])


class ProfileDetectionRequest(BaseModel):
    """Request model for profile detection."""
    user_id: str = Field(..., description="User identifier")
    analysis_days: int = Field(default=30, description="Days to analyze for detection")


class ProfileConfigurationRequest(BaseModel):
    """Request model for profile configuration."""
    user_id: str = Field(..., description="User identifier")
    profile_type: str = Field(..., description="Profile type to configure")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="Profile preferences")


class ProfileKpiRequest(BaseModel):
    """Request model for profile-specific KPIs."""
    user_id: str = Field(..., description="User identifier")
    profile_type: str = Field(..., description="Profile type")
    timeframe: str = Field(default="week", description="Data timeframe")
    currency: str = Field(default="all", description="Currency filter")


class BCBDataRequest(BaseModel):
    """Request model for BCB market data."""
    user_id: str = Field(..., description="User identifier")
    profile_type: Optional[str] = Field(None, description="Profile type for caching")


def get_profile_service():
    """Dependency to get ProfileDetector instance."""
    return get_profile_detector()


def get_router_service():
    """Dependency to get SmartQueryRouter instance."""
    return get_smart_query_router()


def get_bcb_service():
    """Dependency to get BancoCentralAPI instance."""
    return get_banco_central_api()


@profile_router.get("/supported", summary="Get supported profile types")
async def get_supported_profiles():
    """Get list of supported profile types and their characteristics."""
    return {
        "supported_profiles": [
            {
                "id": "CEO",
                "name": "Chief Executive Officer",
                "description": "Strategic overview with focus on high-level KPIs",
                "cache_ttl": 3600,
                "preferred_layer": "snapshot",
                "key_kpis": ["spread_income_detailed", "margem_liquida_operacional"]
            },
            {
                "id": "CFO",
                "name": "Chief Financial Officer",
                "description": "Financial analysis with accuracy focus",
                "cache_ttl": 1800,
                "preferred_layer": "snapshot",
                "key_kpis": ["margem_liquida_operacional", "custo_por_transacao"]
            },
            {
                "id": "Risk_Manager",
                "name": "Risk Manager",
                "description": "Real-time risk monitoring and compliance",
                "cache_ttl": 300,
                "preferred_layer": "direct",
                "key_kpis": ["tempo_processamento_medio"]
            },
            {
                "id": "Trader",
                "name": "Trader",
                "description": "Real-time trading data with minimal latency",
                "cache_ttl": 60,
                "preferred_layer": "cache",
                "key_kpis": ["spread_income_detailed", "tempo_processamento_medio"]
            },
            {
                "id": "Operations",
                "name": "Operations Manager",
                "description": "Operational efficiency and cost management",
                "cache_ttl": 900,
                "preferred_layer": "cache",
                "key_kpis": ["custo_por_transacao", "tempo_processamento_medio"]
            }
        ],
        "total_profiles": 5,
        "detection_confidence_threshold": 0.30
    }


@profile_router.post("/detect", summary="Detect user profile")
async def detect_user_profile(
    request: ProfileDetectionRequest,
    profile_service: get_profile_service = Depends()
):
    """
    Detect user profile based on usage patterns and query history.
    
    Uses ProfileDetector to analyze user behavior and suggest
    the most appropriate profile for personalization.
    """
    try:
        logger.info(f"🔍 API request to detect profile for user {request.user_id}")
        
        # Detect profile
        detection_result = profile_service.detect_profile(
            user_id=request.user_id,
            analysis_days=request.analysis_days
        )
        
        return {
            'user_id': request.user_id,
            'detected_profile': detection_result.get('detected_profile'),
            'confidence': detection_result.get('confidence', 0.0),
            'reason': detection_result.get('reason', 'unknown'),
            'analysis': detection_result.get('analysis', {}),
            'recommendations': {
                'should_use_profile': detection_result.get('confidence', 0.0) >= 0.30,
                'suggested_kpis': _get_profile_kpi_recommendations(
                    detection_result.get('detected_profile')
                ),
                'cache_strategy': _get_profile_cache_strategy(
                    detection_result.get('detected_profile')
                )
            },
            'analysis_period_days': request.analysis_days
        }
        
    except Exception as e:
        logger.error(f"❌ API error detecting profile for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'profile_detection_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@profile_router.post("/configure", summary="Configure user profile")
async def configure_user_profile(
    request: ProfileConfigurationRequest,
    router_service: get_router_service = Depends()
):
    """
    Configure user profile preferences and caching strategy.
    
    Sets up profile-specific configurations for optimal
    performance and personalization.
    """
    try:
        logger.info(f"⚙️ API request to configure profile {request.profile_type} for user {request.user_id}")
        
        # Validate profile type
        supported_profiles = ["CEO", "CFO", "Risk_Manager", "Trader", "Operations"]
        if request.profile_type not in supported_profiles:
            raise HTTPException(
                status_code=400,
                detail={
                    'error': 'invalid_profile_type',
                    'message': f'Profile type must be one of: {supported_profiles}',
                    'provided': request.profile_type
                }
            )
        
        # Configure profile in cache system
        cache_ttl = _get_profile_cache_ttl(request.profile_type)
        
        # Store profile configuration
        profile_config = {
            'user_id': request.user_id,
            'profile_type': request.profile_type,
            'preferences': request.preferences,
            'cache_ttl': cache_ttl,
            'configured_at': router_service.cache_system._get_current_time()
        }
        
        # Cache the profile configuration
        router_service.cache_system.set_personalized(
            namespace="profile:config",
            user_id=request.user_id,
            value=profile_config,
            profile_type=request.profile_type,
            ttl=86400  # 24 hours
        )
        
        return {
            'user_id': request.user_id,
            'profile_type': request.profile_type,
            'configuration': {
                'cache_ttl': cache_ttl,
                'preferred_layer': _get_profile_preferred_layer(request.profile_type),
                'key_kpis': _get_profile_kpi_recommendations(request.profile_type),
                'preferences': request.preferences
            },
            'status': 'configured',
            'message': f'Profile {request.profile_type} configured successfully for user {request.user_id}'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API error configuring profile for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'profile_configuration_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@profile_router.post("/kpis", summary="Get profile-specific KPIs")
async def get_profile_kpis(
    request: ProfileKpiRequest,
    router_service: get_router_service = Depends()
):
    """
    Get KPI recommendations and data specific to user profile.
    
    Returns personalized KPI data routed through the optimal
    layer based on user profile characteristics.
    """
    try:
        logger.info(f"📊 API request for profile KPIs: {request.profile_type} for user {request.user_id}")
        
        # Get profile-specific KPI recommendations
        recommended_kpis = _get_profile_kpi_recommendations(request.profile_type)
        
        if not recommended_kpis:
            return {
                'user_id': request.user_id,
                'profile_type': request.profile_type,
                'recommended_kpis': [],
                'message': 'No specific KPI recommendations for this profile'
            }
        
        # Route KPIs through SmartQueryRouter
        kpi_results = {}
        routing_metadata = {}
        
        for kpi_id in recommended_kpis:
            try:
                result = router_service.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",  # Default client
                    user_id=request.user_id,
                    timeframe=request.timeframe,
                    currency=request.currency,
                    profile_type=request.profile_type
                )
                
                kpi_results[kpi_id] = result
                if result.get('routing_metadata'):
                    routing_metadata[kpi_id] = result['routing_metadata']
                    
            except Exception as e:
                logger.warning(f"⚠️ KPI {kpi_id} routing failed: {e}")
                kpi_results[kpi_id] = {
                    'error': 'routing_failed',
                    'message': str(e),
                    'kpi_id': kpi_id
                }
        
        return {
            'user_id': request.user_id,
            'profile_type': request.profile_type,
            'timeframe': request.timeframe,
            'currency': request.currency,
            'recommended_kpis': recommended_kpis,
            'kpi_results': kpi_results,
            'routing_metadata': routing_metadata,
            'profile_strategy': {
                'preferred_layer': _get_profile_preferred_layer(request.profile_type),
                'cache_ttl': _get_profile_cache_ttl(request.profile_type)
            }
        }
        
    except Exception as e:
        logger.error(f"❌ API error getting profile KPIs for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'profile_kpis_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@profile_router.post("/bcb", summary="Get BCB market data with profile caching")
async def get_bcb_market_data(
    request: BCBDataRequest,
    router_service: get_router_service = Depends()
):
    """
    Get Banco Central do Brasil market data with profile-aware caching.

    Returns official USD/BRL quotation and SELIC rate with
    caching strategy optimized for user profile.
    """
    try:
        logger.info(f"🏦 API request for BCB data for user {request.user_id}")

        # Get BCB market data through SmartQueryRouter
        bcb_data = router_service.get_bcb_market_data(
            user_id=request.user_id,
            profile_type=request.profile_type
        )

        if bcb_data.get('error'):
            raise HTTPException(
                status_code=503,
                detail={
                    'error': bcb_data['error'],
                    'message': bcb_data.get('message', 'BCB API unavailable'),
                    'fail_fast': True,
                    'no_fallbacks': True
                }
            )

        return {
            'user_id': request.user_id,
            'profile_type': request.profile_type,
            'bcb_data': bcb_data,
            'caching_strategy': {
                'cache_ttl': router_service._get_bcb_cache_ttl(request.profile_type),
                'cache_hit': bcb_data.get('cache_hit', False),
                'source': bcb_data.get('source', 'unknown')
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API error getting BCB data for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'bcb_data_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@profile_router.get("/health", summary="Profile API health check")
async def health_check(
    profile_service: get_profile_service = Depends(),
    router_service: get_router_service = Depends(),
    bcb_service: get_bcb_service = Depends()
):
    """
    Health check for all profile-related services.

    Checks ProfileDetector, SmartQueryRouter, and BCB API status.
    """
    try:
        health_status = {
            'profile_api': 'healthy',
            'services': {},
            'timestamp': router_service.cache_system._get_current_time()
        }

        # Check ProfileDetector
        try:
            # Simple test detection
            test_result = profile_service.detect_profile("health_check_user")
            health_status['services']['profile_detector'] = {
                'status': 'healthy',
                'test_detection': 'passed'
            }
        except Exception as e:
            health_status['services']['profile_detector'] = {
                'status': 'unhealthy',
                'error': str(e)
            }

        # Check SmartQueryRouter
        try:
            router_stats = router_service.get_routing_stats()
            health_status['services']['smart_query_router'] = {
                'status': 'healthy',
                'routing_strategies': len(router_stats.get('routing_strategies', {}))
            }
        except Exception as e:
            health_status['services']['smart_query_router'] = {
                'status': 'unhealthy',
                'error': str(e)
            }

        # Check BCB API
        bcb_health = bcb_service.health_check()
        health_status['services']['bcb_api'] = bcb_health

        # Overall status
        unhealthy_services = [
            name for name, service in health_status['services'].items()
            if service.get('status') != 'healthy'
        ]

        if unhealthy_services:
            health_status['profile_api'] = 'degraded'
            health_status['unhealthy_services'] = unhealthy_services

        return health_status

    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return {
            'profile_api': 'unhealthy',
            'error': str(e),
            'timestamp': router_service.cache_system._get_current_time()
        }


def _get_profile_kpi_recommendations(profile_type: Optional[str]) -> List[str]:
    """Get KPI recommendations for a profile type."""
    if not profile_type:
        return []

    recommendations = {
        'CEO': ['spread_income_detailed', 'margem_liquida_operacional'],
        'CFO': ['margem_liquida_operacional', 'custo_por_transacao'],
        'Risk_Manager': ['tempo_processamento_medio'],
        'Trader': ['spread_income_detailed', 'tempo_processamento_medio'],
        'Operations': ['custo_por_transacao', 'tempo_processamento_medio']
    }

    return recommendations.get(profile_type, [])


def _get_profile_cache_strategy(profile_type: Optional[str]) -> Dict[str, Any]:
    """Get cache strategy for a profile type."""
    if not profile_type:
        return {}

    strategies = {
        'CEO': {'ttl': 3600, 'layer': 'snapshot', 'priority': 'accuracy'},
        'CFO': {'ttl': 1800, 'layer': 'snapshot', 'priority': 'accuracy'},
        'Risk_Manager': {'ttl': 300, 'layer': 'direct', 'priority': 'real_time'},
        'Trader': {'ttl': 60, 'layer': 'cache', 'priority': 'speed'},
        'Operations': {'ttl': 900, 'layer': 'cache', 'priority': 'efficiency'}
    }

    return strategies.get(profile_type, {})


def _get_profile_cache_ttl(profile_type: str) -> int:
    """Get cache TTL for a profile type."""
    ttl_map = {
        'CEO': 3600,      # 1 hour
        'CFO': 1800,      # 30 minutes
        'Risk_Manager': 300,  # 5 minutes
        'Trader': 60,     # 1 minute
        'Operations': 900  # 15 minutes
    }
    return ttl_map.get(profile_type, 900)  # Default 15 minutes


def _get_profile_preferred_layer(profile_type: str) -> str:
    """Get preferred routing layer for a profile type."""
    layer_map = {
        'CEO': 'snapshot',
        'CFO': 'snapshot',
        'Risk_Manager': 'direct',
        'Trader': 'cache',
        'Operations': 'cache'
    }
    return layer_map.get(profile_type, 'cache')  # Default cache
