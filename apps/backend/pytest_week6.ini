[tool:pytest]
# Week 6 Testing & Validation - Pytest Configuration
# ==================================================
# 
# Specialized pytest configuration for Week 6 hybrid architecture testing.
# Optimized for unit tests, integration tests, and performance tests.

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output and reporting
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=json:coverage.json
    --cov-fail-under=90
    --maxfail=5

# Test markers for Week 6
markers =
    unit: Unit tests for individual components
    integration: Integration tests for multi-layer architecture
    performance: Performance tests with specific metrics validation
    slow: Slow running tests (>5 seconds)
    database: Tests that require database connection
    external: Tests that require external services (BCB API)
    cache: Tests focused on caching functionality
    routing: Tests focused on routing logic
    profile: Tests focused on profile detection
    concurrent: Tests with concurrent execution
    memory: Tests that monitor memory usage
    fail_fast: Tests that validate fail-fast behavior

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# Minimum version requirements
minversion = 7.0

# Test timeout (for performance tests)
timeout = 300

# Parallel execution settings
# Use with: pytest -n auto
# Requires: pip install pytest-xdist
addopts_parallel = 
    --dist=loadscope
    --tx=popen//python=python

# Coverage settings
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*
    */node_modules/*
    */htmlcov/*
    */coverage/*
    */build/*
    */dist/*
    */.tox/*
    */conftest.py

branch = true
parallel = true

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = true
skip_covered = false
precision = 2

[coverage:html]
directory = htmlcov
title = Week 6 Testing & Validation - Coverage Report

[coverage:json]
output = coverage.json
pretty_print = true

# Performance test configuration
[performance]
# Cache hit rate targets by profile
cache_hit_rate_targets = {
    "CEO": 0.60,
    "CFO": 0.60, 
    "Risk_Manager": 0.30,
    "Trader": 0.80,
    "Operations": 0.80
}

# Response time targets by layer (milliseconds)
response_time_targets = {
    "snapshot": 500,
    "cache": 100,
    "direct": 1000
}

# Concurrent user targets
concurrent_user_targets = {
    "success_rate": 0.95,
    "max_users": 50,
    "requests_per_user": 5
}

# Memory usage limits
memory_limits = {
    "max_increase_mb": 100,
    "baseline_samples": 10,
    "load_requests": 1000
}

# Throughput targets
throughput_targets = {
    "requests_per_second": 20,
    "duration_seconds": 60,
    "min_success_rate": 0.95
}
