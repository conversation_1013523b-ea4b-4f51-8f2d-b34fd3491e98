# Tools

Esta pasta contém ferramentas de desenvolvimento e utilitários para o projeto DataHero4.

## Estrutura

```
tools/
├── debug/          # Ferramentas de debug e visualização
│   ├── debug_visualization.html
│   ├── test_chart_visualization.html
│   └── README.md
└── README.md       # Este arquivo
```

## Subpastas

### `debug/`
Contém ferramentas para debug e teste de visualizações:
- Interfaces web para testar APIs
- Ferramentas de visualização de dados
- Utilitários de health check

## Propósito

Esta pasta organiza todas as ferramentas auxiliares que não fazem parte do código principal da aplicação, mas são úteis para desenvolvimento, debug e manutenção.

## Como usar

Cada subpasta contém seu próprio README com instruções específicas de uso.

## Nota

Esta estrutura foi criada para manter o root do projeto limpo e organizado, seguin<PERSON> as melhores práticas de organização de repositórios. 