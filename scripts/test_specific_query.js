const https = require('https');

function testSpecificQuery() {
  console.log('🚀 Testing with specific L2M query...');
  
  const postData = JSON.stringify({
    question: 'volume de vendas em janeiro 2023',
    client_id: 'L2M',
    sector: 'cambio'
  });

  const options = {
    hostname: 'backend-production-9857.up.railway.app',
    port: 443,
    path: '/ask',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = https.request(options, (res) => {
    console.log('📡 Response status:', res.statusCode);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const jsonData = JSON.parse(data);
          console.log('✅ Response analysis:');
          console.log('- SQL Query:', jsonData.sql_query ? 'Generated' : 'null');
          console.log('- Direct Answer:', jsonData.direct_answer);
          console.log('- Pipeline Path:', jsonData.metadata?.pipeline_path);
          console.log('- Execution Time:', jsonData.metadata?.execution_time);
          console.log('- Tokens Used:', jsonData.metadata?.tokens_used);
          
          if (jsonData.sql_query) {
            console.log('📝 Generated SQL:', jsonData.sql_query);
          }
          
        } catch (e) {
          console.log('✅ Raw response:', data);
        }
      } else {
        console.error('❌ Error response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.write(postData);
  req.end();
}

testSpecificQuery();