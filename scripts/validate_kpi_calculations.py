#!/usr/bin/env python3
"""
Script de Validação dos KPIs Críticos - DataHero4
================================================

Este script valida os 6 KPIs críticos após as correções implementadas:
1. total_volume
2. average_spread (corrigido para percentual)
3. conversion_rate
4. average_ticket
5. retention_rate
6. operations_per_analyst (agora prioritário)

Uso:
    python scripts/validate_kpi_calculations.py
"""

import sys
import os
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend', 'src'))

from services.kpi_service import KpiCalculationService
from config.critical_kpis import CriticalKpisConfig

class KpiValidator:
    """Classe para validar os KPIs críticos."""
    
    def __init__(self):
        self.kpi_service = KpiCalculationService()
        self.critical_kpis = CriticalKpisConfig.get_critical_kpi_ids()
        
    def validate_critical_kpis(self) -> Dict[str, Any]:
        """
        Valida todos os 6 KPIs críticos.
        
        Returns:
            Dict com resultados da validação
        """
        print("🔍 Iniciando validação dos KPIs críticos...")
        print(f"📊 KPIs a serem validados: {', '.join(self.critical_kpis)}")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_kpis': len(self.critical_kpis),
            'kpis_validated': 0,
            'kpis_failed': 0,
            'kpi_results': {},
            'summary': {}
        }
        
        for kpi_id in self.critical_kpis:
            print(f"\n🔢 Validando KPI: {kpi_id}")
            
            try:
                # Calcular valor real do KPI
                kpi_value = self.kpi_service._calculate_real_kpi_value(kpi_id)
                
                # Obter metadados do KPI
                metadata = CriticalKpisConfig.get_kpi_metadata().get(kpi_id, {})
                
                # Validar resultado
                validation_result = self._validate_kpi_result(kpi_id, kpi_value, metadata)
                
                results['kpi_results'][kpi_id] = validation_result
                
                if validation_result['is_valid']:
                    results['kpis_validated'] += 1
                    print(f"✅ {kpi_id}: {validation_result['formatted_value']}")
                else:
                    results['kpis_failed'] += 1
                    print(f"❌ {kpi_id}: {validation_result['error']}")
                    
            except Exception as e:
                results['kpis_failed'] += 1
                results['kpi_results'][kpi_id] = {
                    'is_valid': False,
                    'error': str(e),
                    'value': None
                }
                print(f"❌ {kpi_id}: Erro - {str(e)}")
        
        # Gerar resumo
        results['summary'] = self._generate_summary(results)
        
        return results
    
    def _validate_kpi_result(self, kpi_id: str, value: Optional[float], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valida o resultado de um KPI específico.
        
        Args:
            kpi_id: ID do KPI
            value: Valor calculado
            metadata: Metadados do KPI
            
        Returns:
            Dict com resultado da validação
        """
        if value is None:
            return {
                'is_valid': False,
                'error': 'Valor não calculado (None)',
                'value': None
            }
        
        # Validações específicas por KPI
        validation_rules = {
            'total_volume': lambda v: v >= 0,
            'average_spread': lambda v: 0 <= v <= 100,  # Percentual
            'conversion_rate': lambda v: 0 <= v <= 100,  # Percentual
            'average_ticket': lambda v: v >= 0,
            'retention_rate': lambda v: 0 <= v <= 100,  # Percentual
            'operations_per_analyst': lambda v: v >= 0
        }
        
        # Aplicar validação
        is_valid = True
        error = None
        
        if kpi_id in validation_rules:
            if not validation_rules[kpi_id](value):
                is_valid = False
                error = f"Valor fora do intervalo esperado: {value}"
        
        # Formatação do valor
        format_type = metadata.get('format_type', 'number')
        formatted_value = self._format_value(value, format_type)
        
        return {
            'is_valid': is_valid,
            'error': error,
            'value': value,
            'formatted_value': formatted_value,
            'format_type': format_type,
            'metadata': metadata
        }
    
    def _format_value(self, value: float, format_type: str) -> str:
        """
        Formata o valor conforme o tipo.
        
        Args:
            value: Valor numérico
            format_type: Tipo de formatação
            
        Returns:
            Valor formatado como string
        """
        if format_type == 'currency':
            return f"R$ {value:,.2f}"
        elif format_type == 'percentage':
            return f"{value:.2f}%"
        else:
            return f"{value:.2f}"
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gera resumo dos resultados.
        
        Args:
            results: Resultados da validação
            
        Returns:
            Resumo dos resultados
        """
        return {
            'success_rate': (results['kpis_validated'] / results['total_kpis']) * 100,
            'kpis_validated': results['kpis_validated'],
            'kpis_failed': results['kpis_failed'],
            'total_kpis': results['total_kpis'],
            'validation_status': 'SUCCESS' if results['kpis_failed'] == 0 else 'PARTIAL' if results['kpis_validated'] > 0 else 'FAILED'
        }
    
    def print_detailed_report(self, results: Dict[str, Any]):
        """
        Imprime relatório detalhado dos resultados.
        
        Args:
            results: Resultados da validação
        """
        print("\n" + "="*60)
        print("📋 RELATÓRIO DETALHADO DA VALIDAÇÃO DOS KPIs")
        print("="*60)
        
        print(f"📅 Data/Hora: {results['timestamp']}")
        print(f"📊 Total de KPIs: {results['total_kpis']}")
        print(f"✅ KPIs Validados: {results['kpis_validated']}")
        print(f"❌ KPIs com Falha: {results['kpis_failed']}")
        print(f"📈 Taxa de Sucesso: {results['summary']['success_rate']:.1f}%")
        print(f"🎯 Status: {results['summary']['validation_status']}")
        
        print("\n" + "-"*60)
        print("📊 RESULTADOS POR KPI:")
        print("-"*60)
        
        for kpi_id, kpi_result in results['kpi_results'].items():
            status = "✅" if kpi_result['is_valid'] else "❌"
            print(f"{status} {kpi_id}:")
            
            if kpi_result['is_valid']:
                print(f"   Valor: {kpi_result['formatted_value']}")
                print(f"   Formato: {kpi_result['format_type']}")
            else:
                print(f"   Erro: {kpi_result['error']}")
            
            print()
        
        print("-"*60)
        print("🔧 RECOMENDAÇÕES:")
        
        if results['kpis_failed'] > 0:
            print("• Verificar conexão com banco de dados")
            print("• Revisar fórmulas dos KPIs com falha")
            print("• Validar estrutura das tabelas")
        else:
            print("• Todos os KPIs críticos estão funcionando corretamente!")
            print("• Dashboard frontend pode ser atualizado")
        
        print("="*60)

def main():
    """Função principal do script."""
    print("🚀 DataHero4 - Validação dos KPIs Críticos")
    print("==========================================")
    
    try:
        validator = KpiValidator()
        results = validator.validate_critical_kpis()
        validator.print_detailed_report(results)
        
        # Salvar resultados em arquivo
        import json
        output_file = f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        # Exit code baseado no resultado
        if results['summary']['validation_status'] == 'SUCCESS':
            sys.exit(0)
        elif results['summary']['validation_status'] == 'PARTIAL':
            sys.exit(1)
        else:
            sys.exit(2)
            
    except Exception as e:
        print(f"❌ Erro durante a validação: {str(e)}")
        sys.exit(3)

if __name__ == "__main__":
    main() 