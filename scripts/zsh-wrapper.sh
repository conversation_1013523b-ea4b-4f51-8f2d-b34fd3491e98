#!/usr/bin/env zsh
# -*- coding: utf-8 -*-
# scripts/zsh-wrapper.sh
# --------------------------------------------------
# Wrapper que garante que todos os comandos sejam executados no zsh
# e configura o ambiente corretamente para o DataHero4
# --------------------------------------------------

set -euo pipefail

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    printf "${BLUE}ℹ️  $1${NC}\n"
}

log_success() {
    printf "${GREEN}✅  $1${NC}\n"
}

log_warning() {
    printf "${YELLOW}⚠️  $1${NC}\n"
}

log_error() {
    printf "${RED}❌  $1${NC}\n"
}

# Verifica se estamos no diretório correto
check_project_root() {
    if [[ ! -f "package.json" || ! -d "apps" ]]; then
        log_error "Execute este script a partir do diretório raiz do projeto DataHero4"
        log_info "Diretório atual: $(pwd)"
        log_info "Use: cd /Users/<USER>/coding-projects/datahero4"
        exit 1
    fi
}

# Configura o ambiente zsh
setup_zsh_environment() {
    # Força o uso do zsh
    if [[ "$SHELL" != "/bin/zsh" && "$SHELL" != "/opt/homebrew/bin/zsh" ]]; then
        log_warning "Shell atual: $SHELL"
        export SHELL=$(which zsh)
        log_info "Forçando uso do zsh: $SHELL"
    fi
    
    # Configura variáveis de ambiente para desenvolvimento
    export PYTHONUNBUFFERED=1
    export LOG_LEVEL=DEBUG
    export UVICORN_LOG_LEVEL=debug
    
    # Adiciona node_modules/.bin ao PATH se necessário
    if [[ ":$PATH:" != *":./node_modules/.bin:"* ]]; then
        export PATH="./node_modules/.bin:$PATH"
    fi
    
    log_success "Ambiente zsh configurado"
}

# Função principal
main() {
    local command="$1"
    shift
    
    log_info "🐚 Executando comando no zsh: $command"
    
    check_project_root
    setup_zsh_environment
    
    case "$command" in
        "dev")
            log_info "🚀 Iniciando ambiente de desenvolvimento..."
            exec zsh ./scripts/dev.sh "$@"
            ;;
        "test")
            log_info "🧪 Executando testes..."
            exec zsh ./scripts/test-all.sh "$@"
            ;;
        "validate")
            log_info "🔍 Validando dependências..."
            exec zsh ./scripts/validate-deps.sh "$@"
            ;;
        "setup")
            log_info "⚙️ Configurando terminal..."
            exec zsh ./scripts/setup-zsh.sh "$@"
            ;;
        "turbo")
            log_info "🏃 Executando turbo..."
            exec turbo "$@"
            ;;
        "npm")
            log_info "📦 Executando npm..."
            exec npm "$@"
            ;;
        "poetry")
            log_info "🐍 Executando poetry..."
            exec poetry "$@"
            ;;
        "git")
            log_info "📝 Executando git..."
            exec git "$@"
            ;;
        *)
            log_error "Comando não reconhecido: $command"
            echo ""
            echo "Comandos disponíveis:"
            echo "  dev      - Inicia ambiente de desenvolvimento"
            echo "  test     - Executa todos os testes"
            echo "  validate - Valida dependências"
            echo "  setup    - Configura terminal zsh"
            echo "  turbo    - Executa comandos turbo"
            echo "  npm      - Executa comandos npm"
            echo "  poetry   - Executa comandos poetry"
            echo "  git      - Executa comandos git"
            echo ""
            echo "Exemplo: ./scripts/zsh-wrapper.sh dev"
            exit 1
            ;;
    esac
}

# Verifica se foi passado pelo menos um argumento
if [[ $# -eq 0 ]]; then
    log_error "Nenhum comando especificado"
    echo ""
    echo "Uso: $0 <comando> [argumentos...]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  dev      - Inicia ambiente de desenvolvimento"
    echo "  test     - Executa todos os testes"
    echo "  validate - Valida dependências"
    echo "  setup    - Configura terminal zsh"
    echo "  turbo    - Executa comandos turbo"
    echo "  npm      - Executa comandos npm"
    echo "  poetry   - Executa comandos poetry"
    echo "  git      - Executa comandos git"
    exit 1
fi

main "$@"
