#!/bin/bash

# MCP Playwright Cleanup Script
# Limpa processos travados e cache do Playwright

echo "🧹 Iniciando limpeza do Playwright..."

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Mata processos Chrome relacionados ao Playwright
echo "🔄 Matando processos Chrome do Playwright..."
pkill -f "chrome.*playwright" 2>/dev/null || true
pkill -f "chromium.*playwright" 2>/dev/null || true

# Aguarda processos terminarem
sleep 3

# Mata processos Chrome restantes se necessário
REMAINING_CHROME=$(ps aux | grep -E "(chrome|chromium)" | grep playwright | wc -l)
if [ $REMAINING_CHROME -gt 0 ]; then
    echo "⚠️  Processos persistentes encontrados. Forçando encerramento..."
    pkill -9 -f "chrome.*playwright" 2>/dev/null || true
    pkill -9 -f "chromium.*playwright" 2>/dev/null || true
    sleep 2
fi

# Remove cache do Playwright
echo "🗑️  Removendo cache do Playwright..."

# macOS
if [ -d "$HOME/Library/Caches/ms-playwright" ]; then
    rm -rf "$HOME/Library/Caches/ms-playwright/"
    echo "✅ Cache macOS removido"
fi

# Linux
if [ -d "$HOME/.cache/ms-playwright" ]; then
    rm -rf "$HOME/.cache/ms-playwright/"
    echo "✅ Cache Linux removido"
fi

# Remove arquivos temporários
echo "🧽 Limpando arquivos temporários..."
find /tmp -name "*playwright*" -type f -delete 2>/dev/null || true
find /tmp -name "*chrome*" -type d -empty -delete 2>/dev/null || true

# Verifica resultado da limpeza
echo "🔍 Verificando resultado da limpeza..."

CHROME_PROCESSES=$(ps aux | grep -E "(chrome|chromium)" | grep playwright | wc -l)
CACHE_EXISTS=false

if [ -d "$HOME/Library/Caches/ms-playwright" ] || [ -d "$HOME/.cache/ms-playwright" ]; then
    CACHE_EXISTS=true
fi

# Relatório final
echo ""
echo "📊 RELATÓRIO DE LIMPEZA:"
echo "========================"
echo "Processos Chrome ativos: $CHROME_PROCESSES"
echo "Cache existe: $CACHE_EXISTS"

if [ $CHROME_PROCESSES -eq 0 ] && [ "$CACHE_EXISTS" = false ]; then
    echo "✅ Limpeza concluída com sucesso!"
    echo "🚀 Playwright pronto para uso"
else
    echo "⚠️  Limpeza parcial:"
    if [ $CHROME_PROCESSES -gt 0 ]; then
        echo "   - Ainda há $CHROME_PROCESSES processos ativos"
        echo "   - Tente reiniciar o sistema se o problema persistir"
    fi
    if [ "$CACHE_EXISTS" = true ]; then
        echo "   - Cache ainda existe (pode ser normal)"
    fi
fi

echo ""
echo "💡 Dicas:"
echo "- Execute 'scripts/check-playwright-health.sh' para verificar status"
echo "- Se problemas persistirem, reinicie o sistema"
echo "- Use sempre browser_close_playwright() ao final dos testes"

exit 0
