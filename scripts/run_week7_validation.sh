#!/bin/bash

# Week 7 Validation Script - DataHero4
# ====================================
# 
# Script completo para validar implementação da Week 7:
# - Deploy & Monitoring
# - Observabilidade Completa
# - Alertas Inteligentes
# - Rollback Procedures
# - Testes de Regressão
#
# NO MOCKS, NO FALLBACKS - fail fast and fail loud.

set -e  # Exit on any error

echo "🧪 WEEK 7 VALIDATION - DataHero4"
echo "================================="
echo "Started at: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if service is running
check_service() {
    local service_name=$1
    local port=$2
    
    print_status $BLUE "🔍 Checking $service_name on port $port..."
    
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        print_status $GREEN "✅ $service_name is running"
        return 0
    else
        print_status $RED "❌ $service_name is not running"
        return 1
    fi
}

# Function to run tests
run_test_suite() {
    local test_name=$1
    local test_command=$2
    
    print_status $BLUE "🧪 Running $test_name..."
    
    if eval $test_command; then
        print_status $GREEN "✅ $test_name PASSED"
        return 0
    else
        print_status $RED "❌ $test_name FAILED"
        return 1
    fi
}

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test results array
declare -a FAILED_TEST_NAMES=()

# 1. ENVIRONMENT VALIDATION
print_status $YELLOW "📋 STEP 1: Environment Validation"
echo "=================================="

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [[ -f "apps/backend/webapp_unified.py" ]]; then
    print_status $GREEN "✅ Main application file exists"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_status $RED "❌ Main application file missing"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Environment Validation")
fi

# Check Python dependencies
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if command -v python3 &> /dev/null; then
    print_status $GREEN "✅ Python3 is available"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_status $RED "❌ Python3 is not available"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Python3 Check")
fi

# Check Poetry
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if command -v poetry &> /dev/null; then
    print_status $GREEN "✅ Poetry is available"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_status $RED "❌ Poetry is not available"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Poetry Check")
fi

echo ""

# 2. SERVICE HEALTH CHECKS
print_status $YELLOW "🏥 STEP 2: Service Health Checks"
echo "================================="

# Start the application in background if not running
if ! check_service "DataHero4 Backend" 8000; then
    print_status $BLUE "🚀 Starting DataHero4 Backend..."
    cd apps/backend
    poetry run python webapp_unified.py &
    BACKEND_PID=$!
    cd ../..
    
    # Wait for service to start
    sleep 10
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if check_service "DataHero4 Backend" 8000; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        FAILED_TEST_NAMES+=("Backend Service Start")
    fi
else
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

echo ""

# 3. MONITORING SYSTEM VALIDATION
print_status $YELLOW "📊 STEP 3: Monitoring System Validation"
echo "======================================="

# Test health endpoint
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "Health Endpoint" "curl -s -f http://localhost:8000/health | jq .status | grep -q healthy"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Health Endpoint")
fi

# Test metrics endpoint
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "Metrics Endpoint" "curl -s -f http://localhost:8000/metrics > /dev/null"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Metrics Endpoint")
fi

# Test alerting system
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "Alerting System" "curl -s -f http://localhost:8000/api/alerts/statistics | jq .total_alerts > /dev/null"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Alerting System")
fi

# Test rollback system
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "Rollback System" "curl -s -f http://localhost:8000/api/rollback/snapshots | jq .count > /dev/null"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Rollback System")
fi

echo ""

# 4. REGRESSION TESTS
print_status $YELLOW "🔄 STEP 4: Regression Tests"
echo "============================"

TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test_suite "Regression Test Suite" "cd apps/backend && poetry run python scripts/run_regression_tests.py"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Regression Test Suite")
fi

echo ""

# 5. PERFORMANCE VALIDATION
print_status $YELLOW "⚡ STEP 5: Performance Validation"
echo "=================================="

# Test response times
TOTAL_TESTS=$((TOTAL_TESTS + 1))
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8000/health)
if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    print_status $GREEN "✅ Health endpoint response time: ${RESPONSE_TIME}s"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_status $RED "❌ Health endpoint too slow: ${RESPONSE_TIME}s"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Response Time")
fi

# Test concurrent requests
TOTAL_TESTS=$((TOTAL_TESTS + 1))
print_status $BLUE "🔄 Testing concurrent requests..."
for i in {1..10}; do
    curl -s http://localhost:8000/health > /dev/null &
done
wait

if [[ $? -eq 0 ]]; then
    print_status $GREEN "✅ Concurrent requests handled successfully"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_status $RED "❌ Concurrent requests failed"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("Concurrent Requests")
fi

echo ""

# 6. INTEGRATION TESTS
print_status $YELLOW "🔗 STEP 6: Integration Tests"
echo "============================="

# Test API endpoints
API_ENDPOINTS=(
    "/health"
    "/health/quick"
    "/metrics"
    "/api/alerts/statistics"
    "/api/rollback/snapshots"
)

for endpoint in "${API_ENDPOINTS[@]}"; do
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test_suite "API Endpoint $endpoint" "curl -s -f http://localhost:8000$endpoint > /dev/null"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        FAILED_TEST_NAMES+=("API Endpoint $endpoint")
    fi
done

echo ""

# 7. CLEANUP
print_status $YELLOW "🧹 STEP 7: Cleanup"
echo "==================="

# Kill background processes if we started them
if [[ -n "$BACKEND_PID" ]]; then
    print_status $BLUE "🛑 Stopping backend service..."
    kill $BACKEND_PID 2>/dev/null || true
    wait $BACKEND_PID 2>/dev/null || true
    print_status $GREEN "✅ Backend service stopped"
fi

echo ""

# 8. FINAL REPORT
print_status $YELLOW "📋 FINAL VALIDATION REPORT"
echo "==========================="
echo "Completed at: $(date)"
echo ""
echo "📊 SUMMARY:"
echo "  Total Tests: $TOTAL_TESTS"
echo "  Passed: $PASSED_TESTS"
echo "  Failed: $FAILED_TESTS"

if [[ $FAILED_TESTS -eq 0 ]]; then
    print_status $GREEN "🎉 ALL TESTS PASSED! Week 7 validation successful!"
    SUCCESS_RATE=100
else
    print_status $RED "💥 SOME TESTS FAILED!"
    echo ""
    echo "❌ Failed Tests:"
    for test_name in "${FAILED_TEST_NAMES[@]}"; do
        echo "  - $test_name"
    done
    SUCCESS_RATE=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
fi

echo ""
echo "📈 Success Rate: $SUCCESS_RATE%"
echo ""

# Generate JSON report
REPORT_FILE="week7_validation_report_$(date +%s).json"
cat > "$REPORT_FILE" << EOF
{
  "validation_summary": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "total_tests": $TOTAL_TESTS,
    "passed_tests": $PASSED_TESTS,
    "failed_tests": $FAILED_TESTS,
    "success_rate": $SUCCESS_RATE,
    "status": "$(if [[ $FAILED_TESTS -eq 0 ]]; then echo "PASSED"; else echo "FAILED"; fi)"
  },
  "failed_tests": $(printf '%s\n' "${FAILED_TEST_NAMES[@]}" | jq -R . | jq -s .),
  "week7_components": {
    "monitoring_system": "$(if curl -s -f http://localhost:8000/health > /dev/null; then echo "OPERATIONAL"; else echo "FAILED"; fi)",
    "alerting_system": "$(if curl -s -f http://localhost:8000/api/alerts/statistics > /dev/null; then echo "OPERATIONAL"; else echo "FAILED"; fi)",
    "rollback_system": "$(if curl -s -f http://localhost:8000/api/rollback/snapshots > /dev/null; then echo "OPERATIONAL"; else echo "FAILED"; fi)",
    "observability": "IMPLEMENTED",
    "regression_tests": "EXECUTED"
  }
}
EOF

print_status $BLUE "📄 Detailed report saved to: $REPORT_FILE"

# Exit with appropriate code
if [[ $FAILED_TESTS -eq 0 ]]; then
    print_status $GREEN "✅ Week 7 validation completed successfully!"
    exit 0
else
    print_status $RED "❌ Week 7 validation failed!"
    exit 1
fi
