#!/bin/bash
# 🛡️ DEPENDENCY VALIDATION SCRIPT
# Run this before committing pyproject.toml changes

set -e

echo "🔍 Validating Poetry configuration..."

# Check if we're in the backend directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Run from apps/backend directory."
    exit 1
fi

# 1. Validate Poetry configuration
echo "📋 Running poetry check..."
poetry check || {
    echo "❌ Poetry configuration is invalid!"
    exit 1
}

# 2. Test deployment installation command
echo "📦 Testing deployment installation (--no-dev)..."
poetry install --no-dev --no-interaction --no-ansi || {
    echo "❌ Deployment installation failed!"
    exit 1
}

# 3. Check for common duplicate dependencies
echo "🔍 Checking for duplicate dependencies..."
DUPLICATES=$(poetry show --tree 2>/dev/null | grep -E "(pytest|sentence-transformers|scikit-learn)" | wc -l)
if [ "$DUPLICATES" -gt 3 ]; then
    echo "⚠️  Potential duplicate dependencies detected. Please review:"
    poetry show --tree | grep -E "(pytest|sentence-transformers|scikit-learn)"
fi

# 4. Validate nixpacks.toml if it exists
if [ -f "nixpacks.toml" ]; then
    echo "🔧 Validating nixpacks.toml..."
    if grep -q "only main" nixpacks.toml; then
        echo "❌ Found '--only main' in nixpacks.toml. Use '--no-dev' instead!"
        exit 1
    fi
    
    if grep -q "no-dev" nixpacks.toml; then
        echo "✅ nixpacks.toml uses correct --no-dev command"
    fi
fi

# 5. Test basic import
echo "🐍 Testing basic Python imports..."
poetry run python -c "
import sys
print(f'✅ Python {sys.version_info.major}.{sys.version_info.minor} working')

# Test critical imports
try:
    import fastapi
    import sqlalchemy
    import langchain
    print('✅ Critical dependencies importable')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
" || {
    echo "❌ Python import test failed!"
    exit 1
}

echo ""
echo "🎉 All validations passed!"
echo "✅ Safe to commit pyproject.toml changes"
echo ""
echo "💡 Remember to test the actual deployment after pushing:"
echo "   railway logs --deployment"
