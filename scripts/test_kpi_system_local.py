#!/usr/bin/env python3
"""
Script para testar o sistema de KPIs localmente antes do deploy.
"""

import os
import sys
import logging
from pathlib import Path

# Add the backend src to path
backend_src = Path(__file__).parent.parent / "apps" / "backend" / "src"
sys.path.insert(0, str(backend_src))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_kpi_json_loading():
    """Test if KPI JSON file can be loaded."""
    logger.info("🧪 Testing KPI JSON loading...")
    
    try:
        from services.kpi_service import KpiService
        
        kpi_service = KpiService()
        kpi_definitions = kpi_service.get_kpi_definitions(sector="cambio")
        
        logger.info(f"✅ Loaded {len(kpi_definitions)} KPI definitions")
        
        # Show first few KPIs
        for i, kpi in enumerate(kpi_definitions[:3]):
            logger.info(f"  {i+1}. {kpi['id']}: {kpi['name']}")
            
        return len(kpi_definitions) > 0
        
    except Exception as e:
        logger.error(f"❌ Error loading KPI definitions: {e}")
        return False

def test_kpi_calculation():
    """Test KPI calculation with real database."""
    logger.info("🧪 Testing KPI calculation...")
    
    try:
        from services.kpi_service import KpiService
        
        kpi_service = KpiService()
        
        # Test critical KPIs
        critical_kpis = ['total_volume', 'average_spread', 'average_ticket']
        
        for kpi_id in critical_kpis:
            logger.info(f"  Testing {kpi_id}...")
            
            try:
                value = kpi_service._calculate_real_kpi_value(kpi_id, "L2M")
                if value is not None:
                    logger.info(f"    ✅ {kpi_id}: {value}")
                else:
                    logger.warning(f"    ⚠️  {kpi_id}: No value calculated")
            except Exception as e:
                logger.error(f"    ❌ {kpi_id}: Error - {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing KPI calculation: {e}")
        return False

def test_dashboard_kpis():
    """Test dashboard KPIs endpoint logic."""
    logger.info("🧪 Testing dashboard KPIs...")
    
    try:
        from services.kpi_service import KpiService
        
        kpi_service = KpiService()
        dashboard_kpis = kpi_service.get_dashboard_kpis(
            sector="cambio",
            client_id="L2M",
            timeframe="1d"
        )
        
        logger.info(f"✅ Dashboard returned {len(dashboard_kpis)} KPIs")
        
        # Show first few calculated KPIs
        for i, kpi in enumerate(dashboard_kpis[:3]):
            logger.info(f"  {i+1}. {kpi['id']}: {kpi.get('currentValue', 'N/A')}")
            
        return len(dashboard_kpis) > 0
        
    except Exception as e:
        logger.error(f"❌ Error testing dashboard KPIs: {e}")
        return False

def test_database_connection():
    """Test database connection."""
    logger.info("🧪 Testing database connection...")
    
    try:
        from tools.db_utils import load_db_config, build_connection_string, get_engine
        
        # Test client database connection
        db_config = load_db_config(setor="cambio", cliente="L2M")
        connection_string = build_connection_string(db_config)
        engine = get_engine(connection_string)
        
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as test")
            row = result.fetchone()
            if row and row[0] == 1:
                logger.info("✅ Client database connection successful")
                return True
            else:
                logger.error("❌ Client database connection failed")
                return False
                
    except Exception as e:
        logger.error(f"❌ Database connection error: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting KPI system tests...")
    
    tests = [
        ("JSON Loading", test_kpi_json_loading),
        ("Database Connection", test_database_connection),
        ("KPI Calculation", test_kpi_calculation),
        ("Dashboard KPIs", test_dashboard_kpis),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready for deployment.")
        return True
    else:
        logger.error("❌ Some tests failed. Fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
