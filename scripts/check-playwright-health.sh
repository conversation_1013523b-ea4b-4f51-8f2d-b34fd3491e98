#!/bin/bash

# MCP Playwright Health Check Script
# Verifica se o sistema está saudável para usar Playwright

echo "🔍 Verificando saúde do Playwright..."
echo "===================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Função para status colorido
status_color() {
    local status=$1
    local message=$2
    
    case $status in
        "OK")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Verificar processos Chrome
echo ""
echo "🔍 PROCESSOS:"
CHROME_COUNT=$(ps aux | grep -E "(chrome|chromium)" | grep playwright | wc -l)
echo "Processos Chrome/Playwright ativos: $CHROME_COUNT"

if [ $CHROME_COUNT -eq 0 ]; then
    status_color "OK" "Nenhum processo Chrome ativo"
elif [ $CHROME_COUNT -eq 1 ]; then
    status_color "OK" "1 processo Chrome ativo (normal)"
else
    status_color "WARNING" "$CHROME_COUNT processos Chrome ativos (pode indicar problema)"
fi

# Verificar cache
echo ""
echo "💾 CACHE:"
CACHE_SIZE_MACOS=""
CACHE_SIZE_LINUX=""

if [ -d "$HOME/Library/Caches/ms-playwright" ]; then
    CACHE_SIZE_MACOS=$(du -sh "$HOME/Library/Caches/ms-playwright" 2>/dev/null | cut -f1)
    echo "Cache macOS: $CACHE_SIZE_MACOS"
fi

if [ -d "$HOME/.cache/ms-playwright" ]; then
    CACHE_SIZE_LINUX=$(du -sh "$HOME/.cache/ms-playwright" 2>/dev/null | cut -f1)
    echo "Cache Linux: $CACHE_SIZE_LINUX"
fi

if [ -z "$CACHE_SIZE_MACOS" ] && [ -z "$CACHE_SIZE_LINUX" ]; then
    status_color "INFO" "Nenhum cache encontrado (será criado no primeiro uso)"
else
    status_color "OK" "Cache presente"
fi

# Verificar portas dos serviços
echo ""
echo "🌐 SERVIÇOS:"

# Frontend (porta 3000)
FRONTEND_PORT=$(lsof -i :3000 2>/dev/null | wc -l)
if [ $FRONTEND_PORT -gt 0 ]; then
    FRONTEND_PROCESS=$(lsof -i :3000 2>/dev/null | tail -n 1 | awk '{print $1}')
    status_color "OK" "Frontend rodando na porta 3000 ($FRONTEND_PROCESS)"
else
    status_color "ERROR" "Frontend não está rodando na porta 3000"
fi

# Backend (porta 8000)
BACKEND_PORT=$(lsof -i :8000 2>/dev/null | wc -l)
if [ $BACKEND_PORT -gt 0 ]; then
    BACKEND_PROCESS=$(lsof -i :8000 2>/dev/null | tail -n 1 | awk '{print $1}')
    status_color "OK" "Backend rodando na porta 8000 ($BACKEND_PROCESS)"
else
    status_color "ERROR" "Backend não está rodando na porta 8000"
fi

# Teste de conectividade
echo ""
echo "🔗 CONECTIVIDADE:"

# Teste Frontend
if command_exists curl; then
    FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null)
    if [ "$FRONTEND_RESPONSE" = "200" ]; then
        status_color "OK" "Frontend responde corretamente (HTTP 200)"
    else
        status_color "ERROR" "Frontend não responde ou erro HTTP ($FRONTEND_RESPONSE)"
    fi
    
    # Teste Backend
    BACKEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health 2>/dev/null)
    if [ "$BACKEND_RESPONSE" = "200" ]; then
        status_color "OK" "Backend responde corretamente (HTTP 200)"
    else
        # Tenta endpoint alternativo
        BACKEND_RESPONSE_ALT=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000 2>/dev/null)
        if [ "$BACKEND_RESPONSE_ALT" = "200" ]; then
            status_color "OK" "Backend responde corretamente (HTTP 200)"
        else
            status_color "ERROR" "Backend não responde ou erro HTTP ($BACKEND_RESPONSE)"
        fi
    fi
else
    status_color "WARNING" "curl não disponível - não foi possível testar conectividade"
fi

# Verificar dependências
echo ""
echo "🛠️  DEPENDÊNCIAS:"

if command_exists node; then
    NODE_VERSION=$(node --version)
    status_color "OK" "Node.js disponível ($NODE_VERSION)"
else
    status_color "ERROR" "Node.js não encontrado"
fi

if command_exists npm; then
    NPM_VERSION=$(npm --version)
    status_color "OK" "npm disponível ($NPM_VERSION)"
else
    status_color "ERROR" "npm não encontrado"
fi

# Status geral
echo ""
echo "📊 STATUS GERAL:"
echo "================"

ISSUES=0

if [ $CHROME_COUNT -gt 2 ]; then
    ((ISSUES++))
fi

if [ $FRONTEND_PORT -eq 0 ]; then
    ((ISSUES++))
fi

if [ $BACKEND_PORT -eq 0 ]; then
    ((ISSUES++))
fi

if [ $ISSUES -eq 0 ]; then
    status_color "OK" "Sistema saudável para usar Playwright"
    echo ""
    echo "🚀 PRONTO PARA USO!"
    echo "Você pode usar o MCP Playwright com segurança."
else
    status_color "WARNING" "Sistema tem $ISSUES problema(s) que podem afetar o Playwright"
    echo ""
    echo "🔧 AÇÕES RECOMENDADAS:"
    
    if [ $CHROME_COUNT -gt 2 ]; then
        echo "- Execute 'scripts/cleanup-playwright.sh' para limpar processos"
    fi
    
    if [ $FRONTEND_PORT -eq 0 ]; then
        echo "- Inicie o frontend: npm run dev (ou turbo dev)"
    fi
    
    if [ $BACKEND_PORT -eq 0 ]; then
        echo "- Inicie o backend: verifique se está rodando na porta 8000"
    fi
fi

echo ""
echo "💡 COMANDOS ÚTEIS:"
echo "- Limpeza: scripts/cleanup-playwright.sh"
echo "- Monitoramento: watch -n 2 'scripts/check-playwright-health.sh'"
echo "- Logs: tail -f apps/backend/logs/*.log"

exit 0
