---
type: "manual"
---

Veja a imagem que o usuário postou, ou então acesse a interface do front-end usando o MCP Playwright para descobrir a causa raiz do problema, ou então testar a funcionalidade indicada, extensamente.

Se encontrar algum erro, siga esse roteiro:

- entenda o que a funcionalidade deveria fazer, lendo a documentação (inclusive da pasta docs)
- busque e analise com MCP Ref a documentação atual e pertinente das dependências
- busque e analise com MCP Exa Search analise as melhores e mais modernas práticas de implementação e repositórios do github trending e populares, de sucesso

Com isso, investigue as três mais prováveis causas raizes do problema

Em seguida, faça um plano para solucionar e implemente. 

Não use mocks, fallbacks, ou soluções simplificadas que não realizam tudo o que se espera. 

Teste extensamente com MCP Playwright. APENAS diga que tudo está funcionando quando analisar o conteúdo esperado do que deve aparecer no frontend. Por exemplo, não basta ver apenas que um componente (como um card, com KPI) aparece na tela, mas precisa também ver o conteúdo (dados esperados do KPI, gráficos, interação com filtros etc.)
