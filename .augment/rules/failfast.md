---
type: "always_apply"
---

Never uso mock data, fallback mechanisms, and placeholder implementations - improve debugging and error visibility.

**Objective**: Avoid all instances where the system uses fake/mock data or fallback logic that could mask real issues, making it harder to identify and fix actual problems.

**Specific Actions Required**:

1. **No Mock Data**:
   - Never use hardcoded mock values in KPI calculations
   - No placeholder data in API responses
   - No fake database records or simulated data
   - No mock implementations in service classes

2. **No Fallback Logic**:
   - No fallback queries when primary queries fail
   - No default/placeholder values when real data is unavailable
   - No backup implementations that hide primary system failures
   - No try/catch blocks that silently fall back to mock data

3. **Error Handling Strategy**:
   - When data is unavailable, return explicit errors instead of fallback values
   - Log all failures clearly without masking them with alternatives
   - Prefer system failures that expose problems over silent degradation
   - Implement fail-fast mechanisms rather than graceful degradation

**Success Criteria**:
- No mock data anywhere in the system
- All failures are immediately visible and not masked
- Real database queries for all functionality
- Clear error messages when systems fail
- No silent fallbacks that hide underlying issues

**Philosophy**: "Fail fast and fail loud" - prefer immediate, visible failures over hidden problems that make debugging difficult.