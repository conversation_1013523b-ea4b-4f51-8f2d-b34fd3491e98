---
type: "manual"
---

quando o usuário mandar implementar alguma task ou grupo de tasks:

Siga esse roteiro

- entenda o que a funcionalidade deveria fazer, lendo a documentação (inclusive da pasta docs)
- analise o codebase
- busque e analise com MCP Ref a documentação atual e pertinente das dependências
- busque e analise com MCP Exa Search analise as melhores e mais modernas práticas de implementação e repositórios do github trending e populares, de sucesso

Em seguida, faça ou ajuste, se necessário, o plano de tarefas para implemente. 

Execute o plano, mas não use mocks, fallbacks, ou soluções simplificadas que não realizam tudo o que se espera. 

EM HIPÓTESE ALGUMA USE MOCK OU FALLBACK. FAIL FAST AND LOUD, se preciso.

Teste extensamente com MCP Playwright. APENAS diga que tudo está funcionando quando analisar o conteúdo esperado do que deve aparecer no frontend. Por exemplo, não basta ver apenas que um componente (como um card, com KPI) aparece na tela, mas precisa também ver o conteúdo (dados esperados do KPI, gráficos, interação com filtros etc.)
